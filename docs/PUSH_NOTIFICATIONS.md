# Push Notifications Implementation

This document describes the Firebase Cloud Messaging (FCM) push notification implementation for Accustom.

## Overview

The app now supports both:
1. **Firebase Notification Messages**: Users receive notifications even when the app is closed or in the background
2. **Firebase In-App Messages**: Users receive notifications only when the app is in the foreground

## Architecture

### Backend (Firebase Cloud Functions)

#### FCM Functions (`backend/src/fcm.ts`)

1. **`registerFcmToken`**: Registers FCM tokens for users
   - Stores tokens in Firestore under `users/{userId}/fcmTokens/{tokenId}`
   - Includes device information (platform, model, OS version, app version)

2. **`unregisterFcmToken`**: Removes FCM tokens when devices are no longer active

3. **`sendPushNotification`**: Sends push notifications via FCM
   - Creates in-app notification in Firestore
   - Sends push notification to all registered devices
   - Supports both notification and data-only messages
   - <PERSON>les failed tokens by cleaning them up

#### Message Types

- **Notification Messages**: Show system notifications even when app is closed
- **Data Messages**: Only processed when app is in foreground

### Frontend (React Native/Expo)

#### Push Notification Service (`lib/services/notifications/PushNotificationService.ts`)

- Handles FCM token registration
- Sets up notification channels for Android
- Manages notification permissions
- Registers tokens with Firebase backend

#### Notification Handler (`lib/services/notifications/NotificationHandler.tsx`)

- Handles incoming notifications
- Routes users to appropriate screens based on notification type
- Manages foreground and background notification behavior

#### Updated Notification Service (`lib/services/database/NotificationService.ts`)

- Extended to support push notifications
- Automatically sends push notifications when creating in-app notifications
- Includes navigation data for better user experience

## Usage

### Creating Notifications

```typescript
// Simple notification with push
await firestoreService.notifications.createNotification(userId, {
  title: "Test Notification",
  message: "This is a test message",
  type: "account",
  priority: "medium",
  data: {
    action: "view_profile"
  }
});

// Notification without push
await firestoreService.notifications.createNotification(userId, {
  title: "Silent Update",
  message: "Data updated",
  type: "points",
  priority: "low"
}, {
  sendPush: false
});

// Data-only message
await firestoreService.notifications.createNotification(userId, {
  title: "Background Update",
  message: "Processing...",
  type: "program",
  priority: "low"
}, {
  sendPush: true,
  pushMessageType: 'data'
});
```

### Helper Methods

The notification service includes helper methods that automatically include navigation data:

```typescript
// Welcome notification
await firestoreService.notifications.createWelcomeNotification(userId, userName);

// Program joined notification
await firestoreService.notifications.createProgramJoinedNotification(
  userId, 
  programName, 
  startDate, 
  programId
);

// Setup reminder
await firestoreService.notifications.createSetupReminderNotification(
  userId, 
  programName, 
  programId
);
```

## Notification Types and Navigation

| Type | Navigation Behavior |
|------|-------------------|
| `account` | User Profile or Integrations |
| `program` | Program Details or Discover |
| `reminder` | Progress screen or specific program |
| `points` | User Profile (points display) |

## Data Structure

### Notification Document

```typescript
interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'account' | 'program' | 'points' | 'reminder';
  priority: 'low' | 'medium' | 'high';
  time: string;
  read: boolean;
  // FCM-specific fields
  data?: Record<string, string>;
  imageUrl?: string;
  pushSent?: boolean;
  pushSentAt?: string;
}
```

### FCM Token Document

```typescript
interface FcmToken {
  id: string;
  token: string;
  deviceInfo: {
    platform: string;
    model?: string;
    osVersion?: string;
    appVersion?: string;
  };
  createdAt: string;
  updatedAt: string;
}
```

## Testing

### Test Screen

Navigate to `/TestNotifications` to access the test interface:

1. **Individual Tests**: Test specific notification types
2. **Run All Tests**: Comprehensive test suite
3. **Token Registration**: Test FCM token registration

### Available Tests

- Simple Push Notification
- Program Notification
- Reminder Notification
- High Priority Notification
- Data-Only Notification
- Direct FCM Call
- Token Registration

### Manual Testing

1. Open the app and ensure notifications are enabled
2. Navigate to Settings > Push Notifications > Test Push Notifications
3. Run individual tests or the full test suite
4. Check that notifications appear both in-app and as system notifications

## Configuration

### App Configuration (`app.json`)

```json
{
  "plugins": [
    [
      "expo-notifications",
      {
        "icon": "./assets/images/notification-icon.png",
        "color": "#FFCC00",
        "defaultChannel": "default"
      }
    ]
  ]
}
```

### Notification Channels (Android)

- `accustom-account`: Account notifications
- `accustom-program`: Program notifications
- `accustom-points`: Points notifications
- `accustom-reminder`: Reminder notifications
- `accustom-default`: Default notifications

## Deployment

### Backend Deployment

```bash
cd backend
npm run deploy
```

### Frontend Build

```bash
# Development build
npx expo start

# Production build
npx expo build
```

## Troubleshooting

### Common Issues

1. **Notifications not appearing**: Check permissions and FCM token registration
2. **Navigation not working**: Verify notification data includes correct action/navigation info
3. **Token registration failing**: Check Firebase project configuration and user authentication

### Debug Steps

1. Check console logs for FCM token registration
2. Verify notifications are created in Firestore
3. Test with the built-in test screen
4. Check Firebase Cloud Functions logs

### Logs to Monitor

- FCM token registration success/failure
- Push notification send results
- Navigation action handling
- Permission request results

## Security Considerations

- FCM tokens are stored securely in Firestore
- Invalid tokens are automatically cleaned up
- User authentication is required for all notification operations
- Notification data is validated before sending

## Future Enhancements

- Rich notifications with images
- Notification scheduling
- User preference management
- Analytics and delivery tracking
- A/B testing for notification content
