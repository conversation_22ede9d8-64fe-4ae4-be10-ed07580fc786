import { Timestamp, FieldValue } from 'firebase/firestore';
import { ProgramPreferences } from '@/shared/types/customStake';

// Base interfaces
export interface BaseDocument {
  id?: string;
  createdAt?: Timestamp | string;
  updatedAt?: Timestamp | string;
}

// User related interfaces
export interface User extends BaseDocument {
  fname: string;
  lname: string;
  email: string;
  dateOfBirth?: string;
  gender?: string;
  streak: number;
  active_program?: boolean;
}

export interface UserProgram extends BaseDocument {
  programId: string;
  isArchived?: boolean;
}

// Analytics related interfaces
export interface UserAnalytics extends BaseDocument {
  eventType: string;
  priority: string;
  timestamp: string;
  source: string;
  metadata: Record<string, any>;
  processingTime?: number;
  success: boolean;
}

export interface UserAnalyticsDaily extends BaseDocument {
  date: string;
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsByPriority: Record<string, number>;
  uniqueUsersCount: number;
  averageProcessingTime: number;
  successRate: number;
  errors: number;
  lastUpdated: Timestamp | string;
}

export interface UserLogin extends BaseDocument {
  loginTime: string;
  platform: string;
  deviceInfo?: {
    platform: string;
    version: string;
  };
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  failureReason?: string;
}

// Program related interfaces
export interface Program extends BaseDocument {
  name: string;
  description: string;
  category: string;
  status: 'upcoming' | 'active' | 'ended';
  startDate: string;
  endDate: string;
  betAmount: number;
  defaultLives: number;
  maxLivePurchase: number;
  participantsCount: number;
  duration: number;
  faq?: Array<{ question: string; answer: string }>;
  imageUrl?: string;
  registrationsOpen?: boolean;
  moderator?: string;
  headline?: string;
}

// Participant related interfaces
export interface Participant extends BaseDocument {
  userId: string;
  fname: string;
  signedUpAt: string;
  paymentDone: boolean;
  setupStatus: boolean;
  setupVar: string;
  livesLeft: number;
  livesPurchaseLeft: number;
  disqualified: boolean;
  disqualifyReason?: string;
  timezone: string; // IANA timezone identifier (e.g., "Australia/Melbourne")

  // Program preferences and settings
  programPreferences?: ProgramPreferences;

  // Individual program status fields - each user has their own program timeline
  personalProgramStatus: 'upcoming' | 'ongoing' | 'ended';
  personalStartDate: string; // ISO date when program starts for this user in their timezone
  personalEndDate: string;   // ISO date when program ends for this user in their timezone
  personalCurrentDay: number; // Current day number for this user (1-based)
  lastDayCheckup?: string;    // ISO date of last daily checkup (optional for backward compatibility)
}

// Submission related interfaces
export interface Submission extends BaseDocument {
  status: 'upcoming' | 'submitted' | 'bailed' | 'not_submitted';
  attachment?: string;
  timestamp?: string;
}

// Commit submission interface for individual day submissions
export interface CommitSubmission extends BaseDocument {
  attachment: string;
  status: 'upcoming' | 'submitted' | 'bailed' | 'not_submitted';
  timestamp: string;
}

// Notification related interfaces
export interface Notification extends BaseDocument {
  title: string;
  message: string;
  type: 'account' | 'program' | 'points' | 'reminder';
  priority: 'low' | 'medium' | 'high';
  time: string;
  read: boolean;
}

// Integration related interfaces
export interface Integration extends BaseDocument {
  provider: 'github' | 'strava' | 'fitbit' | 'google_fit' | 'apple_health';
  accessToken: string;
  tokenType?: string;
  scope?: string;
  refreshToken?: string;
  expiresAt?: string;
  user: {
    id: string | number;
    login?: string;
    name?: string;
    email?: string;
    avatar_url?: string;
    [key: string]: any; // Allow additional provider-specific user data
  };
  isActive: boolean;
  lastSyncAt?: string;
  metadata?: Record<string, any>; // Provider-specific metadata
}

// Chat related interfaces
export interface ChatMessage extends BaseDocument {
  text: string;
  userId: string;
  userName: string;
  timestamp: Timestamp | FieldValue;
}

// Dispute related interfaces
export interface Dispute extends BaseDocument {
  email: string;
  program: string;
  programName: string;
  subject: string;
  explanation: string;
  status: 'Pending' | 'Resolved' | 'Rejected';
}

// Transaction related interfaces
export interface Transaction extends BaseDocument {
  programId: string;
  programName: string;
  type: 'in' | 'out';
  amount: number;
  status: 'completed' | 'pending' | 'payment_done' | 'failed';
  description: string;
  timestamp: string;
  metadata?: {
    betAmount?: number;
    winnings?: number;
    refund?: number;
    totalPayout?: number;
    paymentMethod?: string;
    paymentIntentId?: string;
  };
}

// Query options
export interface QueryOptions {
  limit?: number;
  orderBy?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  where?: Array<{
    field: string;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'array-contains' | 'in' | 'array-contains-any';
    value: any;
  }>;
}

// Service response types
export interface ServiceResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface BatchServiceResponse {
  success: boolean;
  results: Array<{ success: boolean; error?: string }>;
  error?: string;
}

// Real-time subscription callback types
export type SubscriptionCallback<T> = (data: T[]) => void;
export type DocumentSubscriptionCallback<T> = (data: T | null) => void;
export type UnsubscribeFunction = () => void;

// Database operation types
export type DatabaseOperation = 'create' | 'read' | 'update' | 'delete' | 'query';

// Error types
export interface DatabaseError {
  operation: DatabaseOperation;
  collection: string;
  documentId?: string;
  originalError: Error;
  timestamp: Date;
}

// Batch operation types
export interface BatchOperation {
  type: 'set' | 'update' | 'delete';
  collection: string;
  documentId?: string;
  data?: any;
}

// Statistics interfaces
export interface ProgramStats {
  totalParticipants: number;
  activeParticipants: number;
  disqualifiedParticipants: number;
  totalSubmissions: number;
  completionRate: number;
  totalPool: number;
  distributedAmount: number;
}

export interface UserStats {
  totalPrograms: number;
  activePrograms: number;
  completedPrograms: number;
  totalPoints: number;
  winnings: number;
}

// Commit related interfaces
export interface Commit extends BaseDocument {
  // Core commitment details
  userId: string;
  title: string; // Renamed from 'commitment' for clarity
  description?: string; // Optional detailed description
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  setupStatus: boolean; // Setup completion status

  // Schedule configuration
  schedule: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'once';
    duration?: number; // Number of days/weeks/months (replaces weekLength)
    startDate: string;
    endDate?: string; // Calculated or explicit end date
    timesPerWeek?: number; // For weekly frequency: how many submissions per week
    timesPerMonth?: number; // For monthly frequency: how many submissions per month
    deadline?: {
      type: 'midnight' | 'before' | 'after' | 'between';
      time?: string; // For 'before' or 'after'
      startTime?: string; // For 'between'
      endTime?: string; // For 'between'
    };
  };

  // Evidence requirements
  evidence: {
    type: 'photo' | 'video' | 'video-timelapse' | 'camera-only' | 'gps-checkin' | 'gps-avoid' | 'strava' | 'screen-time' | 'honor' | 'github';
    config?: {
      location?: { name: string; lat: number; lng: number }; // For GPS evidence
      appName?: string; // For screen-time evidence
      activityType?: string; // For Strava evidence
      repository?: string; // For GitHub evidence
    };
  };

  // Financial stake
  stake: {
    amount: number; // Amount per missed report
    destination: 'charity' | 'anti-charity' | 'platform' | 'none';
    totalAtRisk: number; // Total potential loss
    paymentRequired: boolean; // Whether payment was processed
  };

  // Accountability
  accountability: {
    referee: 'honor' | string; // Honor system or specific person
    reminderChannels?: string[]; // ['app', 'email', 'sms']
    strictnessLevel?: 'lenient' | 'reasonable' | 'strict' | 'no-grace';
  };
}

export interface UserCommit extends BaseDocument {
  commitId: string;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  progress: {
    totalReports: number; // Total reports expected
    completedReports: number; // Reports successfully submitted
    missedReports: number; // Reports missed/failed
    currentStreak: number; // Current consecutive successful reports
    longestStreak: number; // Best streak achieved
    lastSubmissionDate?: string; // Date of last successful submission
    nextDueDate?: string; // When next report is due
  };
  financial: {
    totalLost: number; // Amount lost due to missed reports
    totalAtRisk: number; // Total amount that could be lost
  };
}

// Collection paths
export const COLLECTIONS = {
  USERS: 'users',
  PROGRAMS: 'programs',
  DISPUTES: 'disputes',
  EVENTS: 'events',
  COMMITS: 'commits',
} as const;

// Subcollection paths
export const SUBCOLLECTIONS = {
  USER_PROGRAMS: 'programs',
  USER_NOTIFICATIONS: 'notifications',
  USER_TRANSACTIONS: 'transactions',
  USER_ANALYTICS: 'analytics',
  USER_ANALYTICS_DAILY: 'analytics_daily',
  USER_LOGINS: 'logins',
  USER_COMMITS: 'commits',
  USER_INTEGRATIONS: 'integrations',
  PROGRAM_PARTICIPANTS: 'participants',
  PROGRAM_CHAT: 'chat',
  PARTICIPANT_SUBMISSIONS: 'submissions',
  COMMIT_SUBMISSIONS: 'submissions',
} as const;
