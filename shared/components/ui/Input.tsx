import React, { useState } from 'react';
import { TextInput, View, Text, StyleSheet, TextInputProps, ViewStyle } from 'react-native';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  variant?: 'default' | 'floating';
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  containerStyle,
  variant = 'default',
  style,
  onFocus,
  onBlur,
  ...props
}) => {
  const { colors, isDark, designSystem } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const styles = createStyles(designSystem);

  const handleFocus = (e: any) => {
    setIsFocused(true);
    onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    onBlur?.(e);
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && variant === 'default' && (
        <Text style={[styles.label, { color: colors.text }]}>
          {label}
        </Text>
      )}
      
      <View style={[
        styles.inputContainer,
        designSystem.shadows.sm,
        {
          backgroundColor: colors.glassBackground,
          borderColor: isFocused ? colors.primary : colors.glassBorder,
          shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
        }
      ]}>
        <TextInput
          {...props}
          style={[
            styles.input,
            {
              color: colors.text,
              fontFamily: 'MontserratRegular',
            },
            style
          ]}
          placeholderTextColor={colors.textMuted}
          onFocus={handleFocus}
          onBlur={handleBlur}
        />
        
        {label && variant === 'floating' && (
          <Text style={[
            styles.floatingLabel,
            {
              color: isFocused ? colors.primary : colors.textMuted,
              transform: [
                {
                  translateY: isFocused || props.value ? -8 : 8
                },
                {
                  scale: isFocused || props.value ? 0.85 : 1
                }
              ]
            }
          ]}>
            {label}
          </Text>
        )}
      </View>
      
      {error && (
        <Text style={[styles.error, { color: colors.error }]}>
          {error}
        </Text>
      )}
    </View>
  );
};

const createStyles = (designSystem: typeof import('@/shared/contexts/ThemeContext').DesignSystem) => StyleSheet.create({
  container: {
    marginBottom: designSystem.spacing.md,
  },
  label: {
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: 'MontserratMedium',
    marginBottom: designSystem.spacing.sm / 2,
    letterSpacing: designSystem.typography.letterSpacing.normal,
  },
  inputContainer: {
    borderRadius: designSystem.borderRadius.md,
    borderWidth: 1.5,
    paddingHorizontal: designSystem.spacing.lg,
    paddingVertical: designSystem.spacing.sm + 2,
    position: 'relative',
  },
  input: {
    fontSize: designSystem.typography.fontSize.md + 1,
    minHeight: 18,
  },
  floatingLabel: {
    position: 'absolute',
    left: designSystem.spacing.lg,
    top: designSystem.spacing.lg,
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: 'MontserratRegular',
    pointerEvents: 'none',
  },
  error: {
    fontSize: designSystem.typography.fontSize.sm,
    fontFamily: 'MontserratRegular',
    marginTop: designSystem.spacing.xs,
    marginLeft: designSystem.spacing.xs,
  },
});
