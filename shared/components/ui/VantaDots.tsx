import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, View, Animated } from 'react-native';
import { WebView } from 'react-native-webview';

interface VantaDotsBackgroundProps {
  screenKey?: string;
}

export const VantaDotsBackground: React.FC<VantaDotsBackgroundProps> = ({ screenKey = 'default' }) => {
  const webViewRef = useRef<WebView>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  // Delay WebView rendering to prevent white flash
  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRender(true);
    }, 100); // Small delay to let the screen render first

    return () => clearTimeout(timer);
  }, []);

  // Force WebView to reload after it's rendered
  useEffect(() => {
    if (shouldRender) {
      const timer = setTimeout(() => {
        if (webViewRef.current) {
          webViewRef.current.reload();
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [shouldRender]);

  // Fade in animation when loaded
  useEffect(() => {
    if (isLoaded) {
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isLoaded, fadeAnim]);

  const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100vh;
            overflow: hidden;
            background: transparent !important;
            background-color: transparent !important;
        }
        #vanta-bg {
            width: 100%;
            height: 100vh;
        }
        canvas {
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
        }
    </style>
</head>
<body>
    <div id="vanta-bg"></div>

    <script>
    // Simple dots animation without external dependencies
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    document.getElementById('vanta-bg').appendChild(canvas);

    let width = window.innerWidth;
    let height = window.innerHeight;
    canvas.width = width;
    canvas.height = height;

    const dots = [];
    const numDots = 100;
    let mouseX = width / 2;
    let mouseY = height / 2;

    // Create dots
    for (let i = 0; i < numDots; i++) {
      dots.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * 0.5,
        vy: (Math.random() - 0.5) * 0.5,
        size: Math.random() * 3 + 1
      });
    }

    function animate() {
      ctx.clearRect(0, 0, width, height);

      dots.forEach(dot => {
        // Move towards mouse
        const dx = mouseX - dot.x;
        const dy = mouseY - dot.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 100) {
          dot.vx += dx * 0.0001;
          dot.vy += dy * 0.0001;
        }

        dot.x += dot.vx;
        dot.y += dot.vy;

        // Bounce off edges
        if (dot.x < 0 || dot.x > width) dot.vx *= -1;
        if (dot.y < 0 || dot.y > height) dot.vy *= -1;

        // Draw dot
        ctx.fillStyle = '#FFD700';
        ctx.beginPath();
        ctx.arc(dot.x, dot.y, dot.size, 0, Math.PI * 2);
        ctx.fill();
      });

      requestAnimationFrame(animate);
    }

    // Mouse tracking
    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX;
      mouseY = e.clientY;
    });

    // Touch tracking
    document.addEventListener('touchmove', (e) => {
      if (e.touches.length > 0) {
        mouseX = e.touches[0].clientX;
        mouseY = e.touches[0].clientY;
      }
    });

    // Resize handler
    window.addEventListener('resize', () => {
      width = window.innerWidth;
      height = window.innerHeight;
      canvas.width = width;
      canvas.height = height;
    });

    animate();

    // Send a message to React Native to confirm animation started
    setTimeout(() => {
      if (window.ReactNativeWebView) {
        window.ReactNativeWebView.postMessage('VantaDots animation initialized');
      }
    }, 1000);
    </script>
</body>
</html>
  `;

  return (
    <View style={styles.container}>
      {shouldRender && (
        <Animated.View style={[styles.webviewContainer, { opacity: fadeAnim }]}>
          <WebView
            key={`vanta-dots-${screenKey}`}
            ref={webViewRef}
            source={{ html: htmlContent }}
            style={styles.webview}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={false}
            pointerEvents="none"
            originWhitelist={['*']}
            mixedContentMode="compatibility"
            allowsInlineMediaPlayback={true}
            mediaPlaybackRequiresUserAction={false}
            androidLayerType="hardware"
            cacheEnabled={false}
            incognito={true}
            onMessage={(event) => {
              if (event.nativeEvent.data === 'VantaDots animation initialized') {
                setIsLoaded(true);
              }
            }}
            onError={() => {}}
            onLoad={() => {
              // Fallback: show after 600ms even if message doesn't arrive
              setTimeout(() => setIsLoaded(true), 600);
            }}
            onLoadStart={() => {}}
            onLoadEnd={() => {}}
            renderError={() => <View />}
          />
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    zIndex: -1, // Behind content
  },
  webviewContainer: {
    flex: 1,
  },
  webview: {
    flex: 1,
    backgroundColor: 'transparent',
  },
});
