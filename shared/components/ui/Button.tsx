import React from 'react';
import { TouchableOpacity, Text, ViewStyle, TextStyle, StyleSheet, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface ButtonProps {
  title: string;
  onPress: () => void;
  style?: ViewStyle;
  textStyle?: TextStyle;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'small' | 'medium' | 'large';
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  style,
  textStyle,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'medium'
}) => {
  const { colors, isDark, designSystem } = useTheme();

  const getButtonColors = () => {
    switch (variant) {
      case 'primary':
        return {
          gradient: [colors.primary, colors.warning] as const, // Use theme colors instead of hardcoded
          textColor: colors.background, // Use theme color for contrast
          shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
        };
      case 'secondary':
        return {
          gradient: [colors.glassBackground, colors.glassBackgroundSecondary] as const,
          textColor: colors.text,
          shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
        };
      case 'ghost':
        return {
          gradient: ['transparent', 'transparent'] as const,
          textColor: colors.primary,
          shadowColor: 'transparent',
        };
      default:
        return {
          gradient: [colors.primary, colors.warning] as const, // Use theme colors instead of hardcoded
          textColor: colors.background, // Use theme color for contrast
          shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
        };
    }
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: designSystem.spacing.sm,
          paddingHorizontal: designSystem.spacing.lg,
          fontSize: designSystem.typography.fontSize.md,
          borderRadius: designSystem.borderRadius.md,
        };
      case 'medium':
        return {
          paddingVertical: designSystem.spacing.md,
          paddingHorizontal: designSystem.spacing.xxl,
          fontSize: designSystem.typography.fontSize.lg,
          borderRadius: designSystem.borderRadius.lg,
        };
      case 'large':
        return {
          paddingVertical: designSystem.spacing.md,
          paddingHorizontal: designSystem.spacing.xxxl,
          fontSize: designSystem.typography.fontSize.xl,
          borderRadius: designSystem.borderRadius.xl,
        };
      default:
        return {
          paddingVertical: designSystem.spacing.md,
          paddingHorizontal: designSystem.spacing.xxl,
          fontSize: designSystem.typography.fontSize.lg,
          borderRadius: designSystem.borderRadius.lg,
        };
    }
  };

  const buttonColors = getButtonColors();
  const sizeStyles = getSizeStyles();

  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
      style={[
        styles.button,
        designSystem.shadows.lg,
        {
          borderRadius: sizeStyles.borderRadius,
          shadowColor: buttonColors.shadowColor,
          opacity: disabled ? designSystem.opacity.disabled : designSystem.opacity.full,
        },
        style
      ]}
    >
      <LinearGradient
        colors={buttonColors.gradient}
        style={[
          styles.gradient,
          {
            paddingVertical: sizeStyles.paddingVertical,
            paddingHorizontal: sizeStyles.paddingHorizontal,
            borderRadius: sizeStyles.borderRadius,
          }
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        {loading ? (
          <ActivityIndicator size="small" color={buttonColors.textColor} />
        ) : (
          <Text
            style={[
              styles.buttonText,
              {
                color: buttonColors.textColor,
                fontSize: sizeStyles.fontSize,
                fontFamily: 'MontserratBold',
              },
              textStyle
            ]}
          >
            {title}
          </Text>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    // Shadow styles are now applied via designSystem.shadows.lg
  },
  gradient: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    textAlign: 'center',
    letterSpacing: 0.5,
  },
});
