import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface BaseModalProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  showCloseButton?: boolean;
  animationType?: 'none' | 'slide' | 'fade';
  transparent?: boolean;
  noPadding?: boolean;
}

export const BaseModal: React.FC<BaseModalProps> = ({
  visible,
  onClose,
  title,
  children,
  showCloseButton = true,
  animationType = 'fade',
  transparent = true,
  noPadding = false,
}) => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem);

  return (
    <Modal
      visible={visible}
      transparent={transparent}
      animationType={animationType}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          {(title || showCloseButton) && (
            <View style={styles.modalHeader}>
              {title && (
                <Text style={styles.modalTitle}>{title}</Text>
              )}
              {showCloseButton && (
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={onClose}
                >
                  <MaterialIcons name="close" size={24} color={colors.text} />
                </TouchableOpacity>
              )}
            </View>
          )}
          
          <View style={[styles.modalContent, noPadding && styles.modalContentNoPadding, { flex: 1 }]}>
            {children}
          </View>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: `rgba(0, 0, 0, ${designSystem.opacity.overlay})`,
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: colors.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    width: '100%',
    height: '85%',
    overflow: 'hidden',
    flexDirection: 'column',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: designSystem.spacing.xl,
    paddingTop: designSystem.spacing.lg,
    paddingBottom: designSystem.spacing.md,
    backgroundColor: 'transparent',
  },
  modalTitle: {
    fontSize: designSystem.typography.fontSize.xl,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    marginRight: designSystem.spacing.lg,
  },
  closeButton: {
    padding: designSystem.spacing.xs,
    borderRadius: 20,
    backgroundColor: colors.surface,
  },
  modalContent: {
    flex: 1,
  },
  modalContentNoPadding: {
    padding: 0,
  },
});
