/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * These colors work in conjunction with the ThemeContext for comprehensive theming support.
 */

const tintColorLight = '#D4AF37'; // Classic gold - rich and luxurious
const tintColorDark = '#FFEB3B'; // Bright yellow - perfect for dark backgrounds

export const Colors = {
  light: {
    // Basic colors for compatibility with existing useThemeColor hook
    text: '#1A1A1A', // Clean dark gray
    background: '#FAFAFA', // Clean light gray with warmth
    tint: tintColorLight,
    icon: '#687076', // Clean gray icons
    tabIconDefault: '#687076',
    tabIconSelected: tintColorLight,

    // Extended color palette
    surface: '#FFFFFF', // Pure white
    card: '#FFFFFF',
    header: '#FFFFFF',
    textSecondary: '#2D2D2D', // Darker gray
    textMuted: '#6B7280', // Cool gray
    primary: '#D4AF37', // Classic gold - rich and luxurious
    border: '#E5E7EB', // Clean light border
    separator: '#F3F4F6', // Very light separator
    success: '#10B981', // Modern green
    warning: '#F59E0B', // Amber warning
    error: '#EF4444', // Clean red
    info: '#3B82F6', // Modern blue
  },
  dark: {
    // Basic colors for compatibility with existing useThemeColor hook
    text: '#ECEDEE',
    background: '#000000',
    tint: tintColorDark,
    icon: '#9BA1A6',
    tabIconDefault: '#9BA1A6',
    tabIconSelected: tintColorDark,

    // Extended color palette
    surface: '#1F1F1F',
    card: '#2A2A2A',
    header: '#1e1e1e',
    textSecondary: '#ECEDEE',
    textMuted: '#aaaaaa',
    primary: '#FFEB3B',
    border: '#333333',
    separator: '#444444',
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#E53935',
    info: '#1E88E5',
  },
};

