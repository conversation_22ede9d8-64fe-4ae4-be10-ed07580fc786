/**
 * Tests for responsive font utilities
 */

import { getResponsiveFontSize, ResponsiveFontSizes } from '../responsiveFonts';

// Mock Dimensions
jest.mock('react-native', () => ({
  Dimensions: {
    get: jest.fn(() => ({ width: 375, height: 667 })), // iPhone 6/7/8 dimensions
  },
  Platform: {
    OS: 'ios',
  },
}));

describe('ResponsiveFonts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getResponsiveFontSize', () => {
    it('should return base font size for reference device width', () => {
      const { Dimensions } = require('react-native');
      Dimensions.get.mockReturnValue({ width: 375, height: 667 });
      
      const fontSize = getResponsiveFontSize(28);
      expect(fontSize).toBe(28);
    });

    it('should scale up for larger screens', () => {
      const { Dimensions } = require('react-native');
      Dimensions.get.mockReturnValue({ width: 414, height: 896 }); // iPhone 11 Pro Max
      
      const fontSize = getResponsiveFontSize(28);
      expect(fontSize).toBeGreaterThan(28);
    });

    it('should scale down for smaller screens', () => {
      const { Dimensions } = require('react-native');
      Dimensions.get.mockReturnValue({ width: 320, height: 568 }); // iPhone SE
      
      const fontSize = getResponsiveFontSize(28);
      expect(fontSize).toBeLessThan(28);
    });

    it('should respect minimum scale factor', () => {
      const { Dimensions } = require('react-native');
      Dimensions.get.mockReturnValue({ width: 200, height: 400 }); // Very small screen
      
      const fontSize = getResponsiveFontSize(28, 0.8, 1.3);
      expect(fontSize).toBe(Math.round(28 * 0.8)); // Should be clamped to minimum
    });

    it('should respect maximum scale factor', () => {
      const { Dimensions } = require('react-native');
      Dimensions.get.mockReturnValue({ width: 600, height: 800 }); // Very large screen
      
      const fontSize = getResponsiveFontSize(28, 0.8, 1.3);
      expect(fontSize).toBe(Math.round(28 * 1.3)); // Should be clamped to maximum
    });

    it('should return fixed size for web platform', () => {
      const { Platform } = require('react-native');
      Platform.OS = 'web';
      
      const fontSize = getResponsiveFontSize(28);
      expect(fontSize).toBe(28);
    });
  });

  describe('ResponsiveFontSizes', () => {
    it('should provide consistent font sizes for different use cases', () => {
      const headerLarge = ResponsiveFontSizes.headerLarge();
      const headerMedium = ResponsiveFontSizes.headerMedium();
      const headerSmall = ResponsiveFontSizes.headerSmall();
      
      expect(headerLarge).toBeGreaterThan(headerMedium);
      expect(headerMedium).toBeGreaterThan(headerSmall);
    });

    it('should provide appropriate sizes for body text', () => {
      const bodyLarge = ResponsiveFontSizes.bodyLarge();
      const bodyMedium = ResponsiveFontSizes.bodyMedium();
      const bodySmall = ResponsiveFontSizes.bodySmall();
      
      expect(bodyLarge).toBeGreaterThan(bodyMedium);
      expect(bodyMedium).toBeGreaterThan(bodySmall);
    });
  });
});
