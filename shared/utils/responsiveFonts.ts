/**
 * Responsive font sizing utilities
 * Provides consistent font scaling across different devices and screen sizes
 */

import { Dimensions, Platform } from 'react-native';

// Base screen width for scaling calculations (iPhone 6/7/8 width)
const BASE_WIDTH = 375;

// Web container max width (matches _layout.tsx)
const WEB_MAX_WIDTH = 480;

/**
 * Calculate responsive font size based on screen dimensions
 * @param baseFontSize - The base font size for the reference device
 * @param minScale - Minimum scale factor (default: 0.8)
 * @param maxScale - Maximum scale factor (default: 1.3)
 * @returns Responsive font size
 */
export const getResponsiveFontSize = (
  baseFontSize: number,
  minScale: number = 0.8,
  maxScale: number = 1.3
): number => {
  const { width } = Dimensions.get('window');
  
  if (Platform.OS === 'web') {
    // Web: Use fixed size based on container max width
    return baseFontSize;
  }
  
  // Mobile: Scale based on screen width
  const scaleFactor = width / BASE_WIDTH;
  
  // Clamp the scale factor to prevent too small or too large text
  const clampedScale = Math.max(minScale, Math.min(scaleFactor, maxScale));
  
  return Math.round(baseFontSize * clampedScale);
};

/**
 * Predefined responsive font sizes for common use cases
 */
export const ResponsiveFontSizes = {
  // Header sizes - much more conservative scaling
  headerLarge: () => getResponsiveFontSize(24, 0.9, 1.1),
  headerMedium: () => getResponsiveFontSize(20, 0.9, 1.1),
  headerSmall: () => getResponsiveFontSize(18, 0.9, 1.1),

  // Page title sizes
  pageTitle: () => getResponsiveFontSize(18, 0.9, 1.1),
  sectionTitle: () => getResponsiveFontSize(16, 0.9, 1.05),
  
  // Body text sizes
  bodyLarge: () => getResponsiveFontSize(16, 0.9, 1.05),
  bodyMedium: () => getResponsiveFontSize(14, 0.9, 1.05),
  bodySmall: () => getResponsiveFontSize(12, 0.9, 1.05),

  // Button text sizes
  buttonLarge: () => getResponsiveFontSize(18, 0.9, 1.05),
  buttonMedium: () => getResponsiveFontSize(16, 0.9, 1.05),
  buttonSmall: () => getResponsiveFontSize(14, 0.9, 1.05),
};

/**
 * Hook for responsive font sizing with dimension change listener
 * @param getFontSize - Function that returns the desired font size
 * @returns Current responsive font size
 */
export const useResponsiveFontSize = (getFontSize: () => number) => {
  const [fontSize, setFontSize] = React.useState(getFontSize());
  
  React.useEffect(() => {
    const subscription = Dimensions.addEventListener('change', () => {
      setFontSize(getFontSize());
    });

    return () => subscription?.remove();
  }, [getFontSize]);
  
  return fontSize;
};

// Import React for the hook
import React from 'react';
