export interface PremiumFeature {
  id: string;
  icon: string;
  title: string;
  description: string;
  category: 'communication' | 'customization' | 'analytics' | 'support' | 'flexibility';
  isPremium: boolean;
  pricing?: {
    type: 'subscription' | 'per-use';
    amount: number;
    currency: string;
    unit?: string;
  };
}

export const PREMIUM_FEATURES: PremiumFeature[] = [
  {
    id: 'sms-reminders',
    icon: 'sms',
    title: 'SMS Reminders',
    description: 'Never miss a commitment with text alerts',
    category: 'communication',
    isPremium: true,
  },
  {
    id: 'whatsapp-notifications',
    icon: 'chat',
    title: 'WhatsApp Notifications',
    description: 'Get reminders on your favorite messaging app',
    category: 'communication',
    isPremium: true,
  },
  {
    id: 'motivational-notes',
    icon: 'edit',
    title: 'Motivational Notes',
    description: 'Add personal motivation to keep you going',
    category: 'customization',
    isPremium: true,
  },
  {
    id: 'grace-days',
    icon: 'favorite',
    title: 'Grace Days',
    description: 'Get flexibility with grace days when you need them',
    category: 'flexibility',
    isPremium: true,
    pricing: {
      type: 'per-use',
      amount: 2,
      currency: 'USD',
      unit: 'per grace day',
    },
  },
  {
    id: 'custom-notification-times',
    icon: 'schedule',
    title: 'Custom Notification Times',
    description: 'Set reminders at your perfect times',
    category: 'customization',
    isPremium: true,
  },
  {
    id: 'advanced-analytics',
    icon: 'analytics',
    title: 'Advanced Analytics',
    description: 'Deep insights into your progress and patterns',
    category: 'analytics',
    isPremium: true,
  },
  {
    id: 'priority-support',
    icon: 'support-agent',
    title: 'Priority Support',
    description: 'Get help faster when you need it',
    category: 'support',
    isPremium: true,
  },
  {
    id: 'calendar-integration',
    icon: 'event',
    title: 'Calendar Integration',
    description: 'Sync commitments with your calendar',
    category: 'customization',
    isPremium: true,
  },
  {
    id: 'lenient-strictness',
    icon: 'sentiment-satisfied',
    title: 'Lenient Evaluation',
    description: 'Flexible approach with multiple chances',
    category: 'flexibility',
    isPremium: true,
  },
  {
    id: 'no-grace-strictness',
    icon: 'block',
    title: 'No Grace Mode',
    description: 'Zero tolerance - ultimate accountability',
    category: 'flexibility',
    isPremium: true,
  },
];

export const SUBSCRIPTION_PRICING = {
  monthly: {
    amount: 15,
    currency: 'USD',
    interval: 'month',
  },
  yearly: {
    amount: 150,
    currency: 'USD',
    interval: 'year',
    savings: 30, // $30 savings compared to monthly
  },
};

export const GRACE_DAY_PRICING = {
  amount: 2,
  currency: 'USD',
  maxPerProgram: 5,
  description: 'Grace days provide flexibility when life gets in the way',
};

// Helper functions
export const getPremiumFeaturesByCategory = (category: PremiumFeature['category']) => {
  return PREMIUM_FEATURES.filter(feature => feature.category === category);
};

export const getPremiumFeatureById = (id: string) => {
  return PREMIUM_FEATURES.find(feature => feature.id === id);
};

export const getSubscriptionFeatures = () => {
  return PREMIUM_FEATURES.filter(feature => !feature.pricing || feature.pricing.type === 'subscription');
};

export const getPerUseFeatures = () => {
  return PREMIUM_FEATURES.filter(feature => feature.pricing?.type === 'per-use');
};
