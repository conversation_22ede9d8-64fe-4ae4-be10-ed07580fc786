// src/CommonInterfaces.ts
export interface Program {
  id: string;
  name: string;
  description?: string;
  duration?: number | string; // Allow both number and string to handle database inconsistencies
  startDate?: string;
  endDate?: string;
  betAmount?: number;
  participantsCount?: number;
  moderator?: string;
  defaultLives?: number;
  maxLivePurchase?: number;
  status?: "upcoming" | "ongoing" | "ended" | "disqualified";
  category?: string;
  setupStatus?: boolean | null;
  headline?: string;
  registrationsOpen?: boolean;
  createdAt?: string;
  createdBy?: string;
  image?: {
    url: string;
    alt: string;
  };
};

// interface Program {
//   id: string;
//   name: string;
//   description: string;
//   duration: number;
//   startDate: string;
//   endDate: string;
//   betAmount: number;
//   participantsCount: number;
//   registrationsOpen: boolean;
//   status: "upcoming" | "ongoing" | "completed";
//   category: keyof typeof categoryIcons;
//   moderator: string;
//   defaultLives: number;
//   maxLivePurchase: number;
//   headline: string;
// }

export interface DayTile {
  day: number;
  date: string;
  status: string;
}

export interface ParticipantList {
  id: string;
  borderColor: string;
  fname: string;
  status: string;
  livesLeft: number;
  livesPurchaseLeft: number;
  disqualified?: boolean;
}

// Calendar-based progress system interfaces
export interface CalendarDate {
  date: string; // YYYY-MM-DD format
  day: number;
  month: number;
  year: number;
  isToday: boolean;
  isCurrentMonth: boolean;
}

export interface ProgramTimelineData {
  programId: string;
  programName: string;
  startDate: string;
  endDate: string;
  status: "upcoming" | "ongoing" | "ended" | "disqualified";
  setupStatus: boolean | null;
  category: string;
  betAmount?: number;
  currentDay?: number;
  totalDays?: number;
  needsAttention: boolean;
  attentionReason?: 'setup' | 'disqualified' | 'ended';
}

export interface CalendarDayData extends CalendarDate {
  programs: ProgramTimelineData[];
  hasPrograms: boolean;
  needsAttention: boolean;
  dayStatuses: { [programId: string]: DayTile };
}

export interface CalendarTimelineProps {
  programs: Program[];
  commits?: any[]; // Using any[] for now to avoid circular import issues
  onDateSelect: (date: string, dayData: CalendarDayData) => void;
  onProgramAttention: (program: ProgramTimelineData) => void;
  selectedDate?: string;
}
