import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import { Ionicons, MaterialCommunityIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { useTheme } from '@/shared/contexts/ThemeContext';

const TabIcon = ({ name, library, size, color }: { 
  name: string; 
  library: 'Ionicons' | 'MaterialCommunityIcons'; 
  size: number; 
  color: string; 
}) => {
  if (library === 'Ionicons') {
    return <Ionicons name={name as any} size={size} color={color} />;
  }
  return <MaterialCommunityIcons name={name as any} size={size} color={color} />;
};

export function CustomTabBar({ state, descriptors, navigation }: BottomTabBarProps) {
  const { colors, isDark } = useTheme();

  const tabConfig = [
    {
      name: 'index',
      label: 'Pool',
      iconActive: 'people',
      iconInactive: 'people-outline',
      library: 'Ionicons' as const,
    },
    {
      name: 'commits',
      label: 'Commits',
      iconActive: 'target',
      iconInactive: 'target-variant',
      library: 'MaterialCommunityIcons' as const,
    },
    {
      name: 'progress',
      label: 'Progress',
      iconActive: 'lightning-bolt',
      iconInactive: 'lightning-bolt-outline',
      library: 'MaterialCommunityIcons' as const,
    },
    {
      name: 'points',
      label: 'Points',
      iconActive: 'star-four-points',
      iconInactive: 'star-four-points-outline',
      library: 'MaterialCommunityIcons' as const,
    },
    {
      name: 'profile',
      label: 'Settings',
      iconActive: 'settings',
      iconInactive: 'settings-outline',
      library: 'Ionicons' as const,
    },
  ];

  return (
    <LinearGradient
      colors={
        isDark
          ? ['#1a1a1a', '#151515', '#0f0f0f']
          : ['#ffffff', '#fafafa', '#f5f5f5']
      }
      style={[
        styles.tabBar,
        {
          borderTopColor: colors.border,
        }
      ]}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;
        const config = tabConfig.find(tab => tab.name === route.name);

        if (!config) return null;

        const onPress = () => {
          // Only provide haptic feedback if actually switching tabs
          if (!isFocused && Platform.OS === 'ios') {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          }

          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        const onLongPress = () => {
          navigation.emit({
            type: 'tabLongPress',
            target: route.key,
          });
        };

        return (
          <TouchableOpacity
            key={route.key}
            accessibilityRole="button"
            accessibilityState={isFocused ? { selected: true } : {}}
            accessibilityLabel={options.tabBarAccessibilityLabel}
            testID={(options as any).tabBarTestID}
            onPress={onPress}
            onLongPress={onLongPress}
            style={[
              styles.tabButton,
              isFocused && styles.tabButtonActive,
            ]}
            activeOpacity={0.7}
          >
            <View style={styles.iconContainer}>
              <TabIcon
                name={isFocused ? config.iconActive : config.iconInactive}
                library={config.library}
                size={isFocused ? 26 : 22}
                color={isFocused ? colors.primary : colors.tabIconDefault}
              />
            </View>
            <Text
              style={[
                styles.tabLabel,
                {
                  color: isFocused ? colors.primary : colors.tabIconDefault,
                  fontFamily: isFocused ? 'MontserratBold' : 'MontserratRegular',
                  fontSize: isFocused ? 11 : 10,
                }
              ]}
            >
              {config.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  tabBar: {
    flexDirection: 'row',
    height: Platform.OS === 'ios' ? 85 : 65,
    paddingBottom: Platform.OS === 'ios' ? 25 : 8,
    paddingTop: 8,
    paddingHorizontal: 8,
    borderTopWidth: 1,
    elevation: 8,
    boxShadow: '0 -2px 8px rgba(0, 0, 0, 0.1)',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 6,
    paddingHorizontal: 4,
  },
  tabButtonActive: {
    // Clean active state without transforms
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
    minHeight: 32,
    // transition not supported in React Native
  },
  tabLabel: {
    fontSize: 10,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    marginTop: 2,
  },
});
