import React from 'react';
import { View, ActivityIndicator, SafeAreaView, StatusBar } from 'react-native';
import { useTheme } from '@/shared/contexts/ThemeContext';

/**
 * Simple form skeleton that matches the basic layout structure
 * without trying to replicate every detail
 */
export const SimpleFormSkeleton: React.FC = () => {
  const { colors, isDark } = useTheme();

  return (
    <SafeAreaView style={{
      flex: 1,
      backgroundColor: colors.background,
    }}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
        translucent={false}
      />
      <View style={{
        flex: 1,
        backgroundColor: colors.background,
      }}>
        {/* Header area - exact match to CustomStake */}
      <View style={{
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingTop: 12,
        paddingBottom: 12,
        backgroundColor: colors.background,
      }}>
        {/* Back button with exact padding */}
        <View style={{
          padding: 8,
          borderRadius: 12,
          backgroundColor: colors.surface,
        }}>
          <View style={{
            width: 24,
            height: 24,
            backgroundColor: colors.border,
            borderRadius: 4,
          }} />
        </View>

        {/* Title with exact positioning */}
        <View style={{
          flex: 1,
          alignItems: 'center',
          marginLeft: -44, // Exact match to CustomStake
        }}>
          <View style={{
            width: 140,
            height: 20,
            backgroundColor: colors.surface,
            borderRadius: 4,
          }} />
        </View>

        {/* Spacer */}
        <View style={{ width: 44 }} />
      </View>

      {/* Progress bar area - exact match to ProgressBar component */}
      <View style={{
        backgroundColor: colors.background,
        paddingHorizontal: 20,
        paddingVertical: 8,
        alignItems: 'center',
      }}>
        <View style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          {/* Step circles and connectors */}
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: colors.surface }} />
          <View style={{ width: 40, height: 2, backgroundColor: colors.surface, marginHorizontal: 4 }} />
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: colors.surface }} />
          <View style={{ width: 40, height: 2, backgroundColor: colors.surface, marginHorizontal: 4 }} />
          <View style={{ width: 12, height: 12, borderRadius: 6, backgroundColor: colors.surface }} />
        </View>
      </View>

      {/* Main content area */}
      <View style={{
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>

      {/* Bottom button area */}
      <View style={{
        paddingHorizontal: 24,
        paddingVertical: 24,
        backgroundColor: colors.surface,
        borderTopWidth: 2,
        borderTopColor: colors.border,
      }}>
        <View style={{
          width: '100%',
          height: 54,
          backgroundColor: colors.border,
          borderRadius: 12,
        }} />
      </View>
      </View>
    </SafeAreaView>
  );
};
