import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Dimensions,
} from 'react-native';
import * as Location from 'expo-location';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';

// Conditional import for expo-maps (only available in development builds)
let Map: any = null;
let Marker: any = null;
let mapsAvailable = false;

try {
  const expoMaps = require('expo-maps');
  Map = expoMaps.Map;
  Marker = expoMaps.Marker;
  mapsAvailable = true;
} catch (error) {
  // expo-maps not available, using fallback
  mapsAvailable = false;
}

interface LocationData {
  title: string;
  address: string;
  latitude: number;
  longitude: number;
}

interface InlineLocationMapProps {
  locationData?: LocationData;
  onLocationSelect: (location: LocationData) => void;
  onOpenFullPicker: () => void;
}

interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

const { width } = Dimensions.get('window');

const InlineLocationMap: React.FC<InlineLocationMapProps> = ({
  locationData,
  onLocationSelect,
  onOpenFullPicker,
}) => {
  const { colors } = useTheme();
  const [region, setRegion] = useState<MapRegion>({
    latitude: locationData?.latitude || 37.78825,
    longitude: locationData?.longitude || -122.4324,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  const [manualAddress, setManualAddress] = useState(locationData?.address || '');

  useEffect(() => {
    if (locationData) {
      setRegion({
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
      setManualAddress(locationData.address);
    }
  }, [locationData]);

  const getAddressFromCoordinates = async (latitude: number, longitude: number): Promise<string> => {
    try {
      const result = await Location.reverseGeocodeAsync({ latitude, longitude });
      if (result.length > 0) {
        const address = result[0];
        return `${address.street || ''} ${address.city || ''} ${address.region || ''} ${address.postalCode || ''}`.trim();
      }
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    } catch (error) {
      console.error('Error getting address:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  };

  const handleMapPress = async (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    
    const address = await getAddressFromCoordinates(latitude, longitude);
    
    const newLocation: LocationData = {
      title: 'Selected Location',
      address,
      latitude,
      longitude,
    };

    onLocationSelect(newLocation);
  };

  const handleManualAddressChange = (text: string) => {
    setManualAddress(text);
    
    // Create a location object with manual address
    const newLocation: LocationData = {
      title: 'Manual Location',
      address: text,
      latitude: 0,
      longitude: 0,
    };

    onLocationSelect(newLocation);
  };

  if (!locationData) {
    // Show "Tap to add location" state
    return (
      <TouchableOpacity style={styles.placeholderContainer} onPress={onOpenFullPicker}>
        <MaterialIcons name="add-location" size={32} color={colors.textMuted} />
        <Text style={[styles.placeholderText, { color: colors.textMuted }]}>
          Tap to add location
        </Text>
      </TouchableOpacity>
    );
  }

  // Show selected location with inline map or fallback
  return (
    <View style={styles.container}>
      {/* Selected Location Display */}
      <TouchableOpacity 
        style={[styles.locationHeader, { backgroundColor: colors.surface, borderColor: colors.border }]}
        onPress={onOpenFullPicker}
      >
        <View style={styles.locationInfo}>
          <MaterialIcons name="location-on" size={20} color={colors.primary} />
          <View style={styles.locationText}>
            <Text style={[styles.locationTitle, { color: colors.text }]}>
              {locationData.title}
            </Text>
            <Text style={[styles.locationAddress, { color: colors.textMuted }]} numberOfLines={2}>
              {locationData.address}
            </Text>
          </View>
        </View>
        <MaterialIcons name="edit" size={16} color={colors.textMuted} />
      </TouchableOpacity>

      {/* Inline Map or Fallback */}
      <View style={styles.mapContainer}>
        {mapsAvailable && Map && locationData.latitude !== 0 && locationData.longitude !== 0 ? (
          <Map
            style={styles.map}
            initialRegion={region}
            onPress={handleMapPress}
            scrollEnabled={false}
            zoomEnabled={false}
            rotateEnabled={false}
            pitchEnabled={false}
          >
            <Marker
              coordinate={{
                latitude: locationData.latitude,
                longitude: locationData.longitude,
              }}
              title={locationData.title}
              description={locationData.address}
            />
          </Map>
        ) : (
          <View style={[styles.fallbackMap, { backgroundColor: colors.surface, borderColor: colors.border }]}>
            <MaterialIcons name="map" size={32} color={colors.textMuted} />
            <Text style={[styles.fallbackText, { color: colors.textMuted }]}>
              Map preview not available
            </Text>
            <Text style={[styles.fallbackSubtext, { color: colors.textMuted }]}>
              Tap above to edit location
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  placeholderContainer: {
    height: 120,
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
  },
  placeholderText: {
    fontSize: 16,
    marginTop: 8,
    fontWeight: '500',
  },
  locationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 8,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationText: {
    marginLeft: 8,
    flex: 1,
  },
  locationTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  locationAddress: {
    fontSize: 12,
    lineHeight: 16,
  },
  mapContainer: {
    height: 120,
    borderRadius: 8,
    overflow: 'hidden',
  },
  map: {
    flex: 1,
  },
  fallbackMap: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: 8,
  },
  fallbackText: {
    fontSize: 14,
    marginTop: 8,
    fontWeight: '500',
  },
  fallbackSubtext: {
    fontSize: 12,
    marginTop: 4,
  },
});

export default InlineLocationMap;
