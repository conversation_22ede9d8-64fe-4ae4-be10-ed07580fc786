import { firestoreService } from "../../../lib/services/database";
import {
  updateLname,
  updateToken,
  updateFname,
  updateId } from "../../../lib/utils/variables"

interface FetchUserDataResult {
  success: boolean;
  error?: string;
}

const fetchUserData = async (email: string): Promise<FetchUserDataResult> => {
  const result = await firestoreService.users.getUserById(email);

  if (result.success && result.data) {
    const userData = result.data;
    updateToken(true);
    updateFname(userData.fname);
    updateLname(userData.lname);
    updateId(userData.email);

    return { success: true };
  } else {
    if (result.error) {
      console.error("Error fetching user data from Firestore:", result.error);
    }
    return {
      success: false,
      error: "User document not found in Firestore. Create a new user document?"
    };
  }
};

export default fetchUserData;
