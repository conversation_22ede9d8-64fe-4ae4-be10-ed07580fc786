import React from "react";
import { View, Text, ActivityIndicator, StyleSheet, Dimensions } from "react-native";
import { useTheme } from "@/shared/contexts/ThemeContext";

const { width, height } = Dimensions.get('window');

type StreakAndPercentileProps = {
  userPoints: number | null;
  percentile: number | null;
  loading: boolean;
};

const StreakAndPercentile: React.FC<StreakAndPercentileProps> = ({
  userPoints,
  percentile,
  loading,
}) => {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors, designSystem, isDark);
  const getPercentileDisplay = () => {
    if (loading) {
      return "Loading...";
    }
    if (percentile !== null && percentile !== undefined) {
      return `${percentile}%`;
    }
    return "N/A";
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <View style={[styles.pointsPercentileContainer, { backgroundColor: colors.surface }]}>
      <View style={styles.streakSection}>
        <Text style={[styles.sectionTitle, { color: colors.primary }]}>Points</Text>
        <Text style={[styles.streakValue, { color: colors.text }]}>
          {userPoints !== null ? `${userPoints} 🌟` : "0 🌟"}
        </Text>
      </View>
      <View style={[styles.separator, { backgroundColor: colors.primary }]} />
      <View style={styles.percentileSection}>
        <Text style={[styles.sectionTitle, { color: colors.primary }]}>Percentile</Text>
        <Text style={[styles.percentileValue, { color: colors.text }]}>
          {getPercentileDisplay()}
        </Text>
      </View>
    </View>
  );
};

const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  loadingContainer: {
    padding: 20,
    margin: 10,
    alignItems: "center",
  },
  pointsPercentileContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 1,
    paddingHorizontal: 10,
    marginVertical: 2,
    marginTop: 4,
    marginHorizontal: 10,
    backgroundColor: colors.card,
    borderRadius: 10,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  streakSection: {
    alignItems: "center",
    flex: 1,
    padding: 5,
  },
  sectionTitle: {
    color: colors.text,
    fontSize: 18,
    fontFamily: "MontserratBold",
    marginBottom: 5,
    textAlign: "center",
  },
  streakValue: {
    fontSize: 36,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  separator: {
    width: 1,
    backgroundColor: colors.border,
    height: "80%",
    opacity: 0.6,
  },
  percentileSection: {
    alignItems: "center",
    flex: 1,
    padding: 5,
  },
  percentileValue: {
    fontSize: 36,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
});

export default StreakAndPercentile;
