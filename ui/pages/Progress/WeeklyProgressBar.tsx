import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface WeeklyProgressBarProps {
  submittedCount: number;
  totalRequired: number;
  todaySubmissionExists: boolean;
  canSubmitToday: boolean;
  style?: any;
}

export const WeeklyProgressBar: React.FC<WeeklyProgressBarProps> = ({
  submittedCount,
  totalRequired,
  todaySubmissionExists,
  canSubmitToday,
  style
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const progressPercentage = (submittedCount / totalRequired) * 100;
  const isComplete = submittedCount === totalRequired;

  const getStatusIcon = () => {
    if (isComplete) {
      return <MaterialIcons name="check-circle" size={16} color={colors.success || '#4CAF50'} />;
    } else if (todaySubmissionExists) {
      return <MaterialIcons name="schedule" size={16} color={colors.primary} />;
    } else if (canSubmitToday) {
      return <MaterialIcons name="play-circle-outline" size={16} color={colors.primary} />;
    } else {
      return <MaterialIcons name="pause-circle-outline" size={16} color={colors.textMuted} />;
    }
  };

  const getStatusText = () => {
    if (isComplete) {
      return 'Week Complete! 🏆';
    } else if (todaySubmissionExists) {
      return 'Come back tomorrow! 📅';
    } else if (canSubmitToday) {
      const remaining = totalRequired - submittedCount;
      return remaining === 1 ? 'Final submission! 🎯' : `${remaining} more to go! 💪`;
    } else {
      return 'Check back later';
    }
  };

  return (
    <View style={[styles.container, style]}>
      <View style={styles.headerRow}>
        <View style={styles.statusContainer}>
          {getStatusIcon()}
          <Text style={styles.statusText}>{getStatusText()}</Text>
        </View>
        <Text style={styles.countText}>
          {submittedCount}/{totalRequired}
        </Text>
      </View>
      
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarBackground}>
          <View 
            style={[
              styles.progressBarFill, 
              { 
                width: `${progressPercentage}%`,
                backgroundColor: isComplete 
                  ? colors.success || '#4CAF50'
                  : colors.primary
              }
            ]} 
          />
        </View>
        
        {/* Progress segments */}
        <View style={styles.segmentsContainer}>
          {Array.from({ length: totalRequired }, (_, index) => (
            <View
              key={index}
              style={[
                styles.segment,
                {
                  backgroundColor: index < submittedCount 
                    ? (isComplete ? colors.success || '#4CAF50' : colors.primary)
                    : 'transparent',
                  borderColor: index < submittedCount 
                    ? (isComplete ? colors.success || '#4CAF50' : colors.primary)
                    : colors.border,
                }
              ]}
            />
          ))}
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    paddingVertical: 8,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  countText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.primary,
  },
  progressBarContainer: {
    position: 'relative',
    height: 8,
  },
  progressBarBackground: {
    height: 8,
    backgroundColor: colors.surface,
    borderRadius: 4,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.border,
  },
  progressBarFill: {
    height: '100%',
    borderRadius: 4,
  },
  segmentsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 8,
    flexDirection: 'row',
    gap: 2,
  },
  segment: {
    flex: 1,
    height: 8,
    borderRadius: 2,
    borderWidth: 1,
  },
});
