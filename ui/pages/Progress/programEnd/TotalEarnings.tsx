import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator, Animated, Modal } from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { firestoreService } from '@/lib/services/database';
import { Transaction } from '@/lib/services/database/types';
import { ErrorModal } from '@/shared/components/modals';

interface TotalEarningsProps {
  betAmount: number;
  winnings: number;
  programId: string;
  programName: string;
  currentUserId: string;
}

const TotalEarnings = ({
  betAmount,
  winnings,
  programId,
  programName,
  currentUserId
}: TotalEarningsProps): React.ReactElement => {
  const { colors, designSystem } = useTheme();
  const styles = createStyles(colors, designSystem);

  // Ensure we have valid numbers
  const safeBetAmount = typeof betAmount === 'number' ? betAmount : 0;
  const safeWinnings = typeof winnings === 'number' ? winnings : 0;
  const totalEarnings = safeBetAmount + safeWinnings;

  // Cash out state
  const [loading, setLoading] = useState(false);
  const [cashOutTransaction, setCashOutTransaction] = useState<Transaction | null>(null);
  const [checkingStatus, setCheckingStatus] = useState(true);
  const [pulseAnim] = useState(new Animated.Value(1));
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);

  // Modal state for replacing Alert dialogs
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });

  // Check cash out status on mount
  useEffect(() => {
    const checkCashOutStatus = async () => {
      if (totalEarnings <= 0) {
        setCheckingStatus(false);
        return;
      }

      try {
        const result = await firestoreService.transactions.getCashOutTransaction(
          currentUserId,
          programId
        );

        if (result.success && result.data) {
          setCashOutTransaction(result.data);
        }
      } catch (error) {
        console.error('Error checking cash out status:', error);
      } finally {
        setCheckingStatus(false);
      }
    };

    checkCashOutStatus();
  }, [currentUserId, programId, totalEarnings]);

  // Pulse animation for cash out button
  useEffect(() => {
    if (totalEarnings > 0 && !cashOutTransaction) {
      const pulse = () => {
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]).start(() => pulse());
      };
      pulse();
    }
  }, [totalEarnings, cashOutTransaction, pulseAnim]);

  const handleCashOut = async () => {
    if (totalEarnings <= 0) {
      setErrorModalData({
        title: 'No Earnings',
        message: 'You have no earnings to cash out from this program.'
      });
      setErrorModalVisible(true);
      return;
    }

    setShowConfirmModal(true);
  };

  const processCashOut = async () => {
    setShowConfirmModal(false);
    setLoading(true);

    try {
      const result = await firestoreService.transactions.createCashOutTransaction(
        currentUserId,
        programId,
        programName,
        totalEarnings,
        {
          betAmount: safeBetAmount,
          winnings: safeWinnings,
          refund: safeBetAmount,
        }
      );

      if (result.success) {
        // Refresh the transaction status
        const updatedResult = await firestoreService.transactions.getCashOutTransaction(
          currentUserId,
          programId
        );

        if (updatedResult.success && updatedResult.data) {
          setCashOutTransaction(updatedResult.data);
        }

        setShowSuccessModal(true);
      } else {
        setErrorModalData({
          title: 'Error',
          message: result.error || 'Failed to initiate cash out. Please try again.'
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error('Error processing cash out:', error);
      setErrorModalData({
        title: 'Error',
        message: 'Failed to process cash out. Please try again.'
      });
      setErrorModalVisible(true);
    } finally {
      setLoading(false);
    }
  };

  const getStatusDisplay = () => {
    if (checkingStatus) {
      return (
        <View style={styles.statusContainer}>
          <ActivityIndicator size="small" color={colors.primary || '#FFEB3B'} />
          <Text style={styles.statusText}>Checking status...</Text>
        </View>
      );
    }

    if (!cashOutTransaction) {
      if (totalEarnings <= 0) {
        return null;
      }

      return (
        <Animated.View style={[styles.cashOutContainer, { transform: [{ scale: pulseAnim }] }]}>
          <View style={styles.cashOutButtonWrapper}>
            <TouchableOpacity
              style={[styles.cashOutButton, loading && styles.disabledButton]}
              onPress={handleCashOut}
              disabled={loading}
            >
            {loading ? (
              <ActivityIndicator size="small" color="#000000" />
            ) : (
              <>
                <MaterialIcons name="account-balance-wallet" size={16} color="#000000" />
                <Text style={styles.cashOutButtonText}>Cash Out</Text>
              </>
            )}
            </TouchableOpacity>
          </View>
        </Animated.View>
      );
    }

    // Show status based on transaction state
    switch (cashOutTransaction.status) {
      case 'pending':
        return (
          <View style={[styles.statusContainer, styles.pendingStatus]}>
            <MaterialIcons name="schedule" size={20} color="#FF9800" />
            <View style={styles.pendingTextContainer}>
              <Text style={[styles.statusText, { color: '#FF9800' }]}>
                💳 Payment Processing
              </Text>
              <Text style={[styles.processingSubtext, { color: '#FF9800' }]}>
                Typically completes within 3-4 business days
              </Text>
            </View>
          </View>
        );
      case 'payment_done':
        return (
          <View style={[styles.statusContainer, styles.successStatus]}>
            <MaterialIcons name="check-circle" size={20} color="#4CAF50" />
            <Text style={[styles.statusText, { color: '#4CAF50' }]}>
              ✅ Payment Completed!
            </Text>
          </View>
        );
      case 'failed':
        return (
          <View style={[styles.statusContainer, styles.errorStatus]}>
            <MaterialIcons name="error" size={20} color="#F44336" />
            <Text style={[styles.statusText, { color: '#F44336' }]}>
              ❌ Payment Failed - Contact Support
            </Text>
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Your Total Earnings</Text>
      <View style={styles.earningsSummary}>
        <View style={styles.earningsRow}>
          <View style={styles.earningsLabelContainer}>
            <Text style={styles.earningsLabel}>Your Refund</Text>
            <Text style={styles.earningsSubtext}>
              (initial bet returned)
            </Text>
          </View>
          <Text style={styles.earningsValue}>${safeBetAmount.toFixed(2)}</Text>
        </View>
        <View style={styles.earningsRow}>
          <View style={styles.earningsLabelContainer}>
            <Text style={styles.earningsLabel}>Your Winnings</Text>
            <Text style={styles.earningsSubtext}>
              (from eliminated players)
            </Text>
          </View>
          <Text style={styles.earningsValue}>
            ${safeWinnings.toFixed(2)}
          </Text>
        </View>
        <View style={[styles.earningsRow, styles.totalRow]}>
          <View style={styles.totalLabelContainer}>
            <Text style={styles.totalLabel}>TOTAL PAYOUT</Text>
            <MaterialIcons name="emoji-events" size={16} color={colors.primary || '#FFEB3B'} />
          </View>
          <Text style={styles.totalValue}>${totalEarnings.toFixed(2)}</Text>
        </View>

        {/* Cash Out Section */}
        {getStatusDisplay()}
      </View>

      {/* Confirmation Modal */}
      <Modal
        visible={showConfirmModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowConfirmModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <MaterialIcons name="account-balance-wallet" size={32} color="#D4AF37" />
              <Text style={styles.modalTitle}>💰 Cash Out Confirmation</Text>
            </View>

            <Text style={styles.modalMessage}>
              Congratulations! You're about to cash out{' '}
              <Text style={styles.highlightAmount}>${totalEarnings.toFixed(2)}</Text>{' '}
              from {programName}.
            </Text>

            <View style={styles.breakdownContainer}>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>• Refund:</Text>
                <Text style={styles.breakdownValue}>${safeBetAmount.toFixed(2)}</Text>
              </View>
              <View style={styles.breakdownRow}>
                <Text style={styles.breakdownLabel}>• Winnings:</Text>
                <Text style={styles.breakdownValue}>${safeWinnings.toFixed(2)}</Text>
              </View>
            </View>

            <Text style={styles.confirmQuestion}>Proceed with cash out?</Text>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowConfirmModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.modalButton,
                  {
                    backgroundColor: '#D4AF37',
                    paddingVertical: 12,
                    paddingHorizontal: 16,
                    borderRadius: 8,
                    alignItems: 'center',
                    justifyContent: 'center',
                    borderWidth: 1,
                    borderColor: '#FFD700',
                  }
                ]}
                onPress={processCashOut}
                disabled={loading}
              >
                {loading ? (
                  <ActivityIndicator size="small" color="#FFFFFF" />
                ) : (
                  <Text style={{
                    fontSize: 16,
                    fontWeight: 'bold',
                    color: '#000000',
                    textAlign: 'center',
                  }}>
                    💸 Cash Out
                  </Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowSuccessModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <MaterialIcons name="check-circle" size={32} color="#4CAF50" />
              <Text style={styles.modalTitle}>🎉 Cash Out Initiated!</Text>
            </View>

            <Text style={styles.modalMessage}>
              Your cash out request has been submitted successfully! 💰
            </Text>

            <Text style={styles.processingInfo}>
              Your payment will be processed within 3-5 business days.
              You'll receive a confirmation email shortly.
            </Text>

            <TouchableOpacity
              style={[
                styles.modalButton,
                {
                  width: '100%',
                  backgroundColor: '#D4AF37',
                  paddingVertical: 12,
                  paddingHorizontal: 16,
                  borderRadius: 8,
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderWidth: 1,
                  borderColor: '#FFD700',
                }
              ]}
              onPress={() => setShowSuccessModal(false)}
            >
              <Text style={{
                fontSize: 16,
                fontWeight: 'bold',
                color: '#000000',
                textAlign: 'center',
              }}>
                Awesome!
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />
    </View>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  container: {
    width: "100%",
    padding: designSystem.spacing.sm,
  },
  title: {
    color: colors.primary,
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginBottom: designSystem.spacing.sm,
    textAlign: "center",
  },
  earningsSummary: {
    backgroundColor: colors.card,
    borderRadius: designSystem.borderRadius.md,
    padding: designSystem.spacing.sm + 2,
    width: "100%",
    ...designSystem.shadows.xl,
    shadowColor: colors.primary + '40',
    borderWidth: 1,
    borderColor: colors.border,
  },
  earningsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: designSystem.spacing.sm / 2,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  earningsLabelContainer: {
    flex: 1,
  },
  earningsLabel: {
    color: colors.text,
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: "MontserratBold",
  },
  earningsSubtext: {
    color: colors.textMuted,
    fontSize: designSystem.typography.fontSize.xs + 1,
    fontFamily: "MontserratRegular",
    marginTop: designSystem.spacing.xs / 4,
  },
  earningsValue: {
    color: colors.text,
    fontSize: 16,
    fontFamily: "MontserratBold",
    minWidth: 80,
    textAlign: "right",
  },
  totalRow: {
    borderBottomWidth: 0,
    marginTop: 4,
    paddingTop: 4,
    backgroundColor: colors.surface || '#F5F5F5',
    borderRadius: 8,
    padding: 8,
  },
  totalLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  totalLabel: {
    color: colors.primary || '#FFEB3B',
    fontSize: 15,
    fontFamily: "MontserratBold",
  },
  totalValue: {
    color: colors.primary || '#FFEB3B',
    fontSize: 18,
    fontFamily: "MontserratBold",
    minWidth: 80,
    textAlign: "right",
  },
  // Cash out styles
  cashOutContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.border || '#E0E0E0',
  },
  cashOutButtonWrapper: {
    alignSelf: 'center', // Center the button
    width: '60%', // Make it smaller width
  },
  cashOutButton: {
    backgroundColor: '#D4AF37', // Rich gold color
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10, // Reduced from 16
    paddingHorizontal: 16, // Reduced from 24
    borderRadius: 6, // Slightly smaller radius
    elevation: 3, // Reduced shadow
    boxShadow: '0 2px 3px rgba(184, 134, 11, 0.25)', // Reduced shadow
    gap: 6, // Reduced gap
    borderWidth: 1, // Thinner border
    borderColor: '#FFD700',
  },
  disabledButton: {
    opacity: 0.6,
    backgroundColor: '#9E9E9E',
    borderColor: '#757575',
  },
  cashOutButtonText: {
    color: '#000000',
    fontSize: 14, // Reduced from 17
    fontFamily: "MontserratBold",
    letterSpacing: 0.3, // Reduced letter spacing
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginTop: 12,
    gap: 8,
  },
  pendingStatus: {
    backgroundColor: '#FFF3E0',
    borderWidth: 1,
    borderColor: '#FFB74D',
  },
  successStatus: {
    backgroundColor: '#E8F5E8',
    borderWidth: 1,
    borderColor: '#81C784',
  },
  errorStatus: {
    backgroundColor: '#FFEBEE',
    borderWidth: 1,
    borderColor: '#E57373',
  },
  statusText: {
    fontSize: 14,
    fontFamily: "MontserratBold",
    textAlign: 'center',
  },
  pendingTextContainer: {
    alignItems: 'center',
    flex: 1,
  },
  processingSubtext: {
    fontSize: 11,
    fontFamily: "MontserratRegular",
    textAlign: 'center',
    marginTop: 2,
    opacity: 0.8,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    elevation: 10,
    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.3)',
  },
  modalHeader: {
    alignItems: 'center',
    marginBottom: 20,
    gap: 8,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  highlightAmount: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: '#D4AF37',
  },
  breakdownContainer: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  breakdownLabel: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
  },
  breakdownValue: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  confirmQuestion: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: colors.border,
  },
  confirmButton: {
    backgroundColor: '#D4AF37',
    borderWidth: 1,
    borderColor: '#FFD700',
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.textMuted,
  },
  confirmButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: '#000000',
    textAlign: 'center',
    // Ensure text is visible with explicit styling
  },
  processingInfo: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
});

export default TotalEarnings;
