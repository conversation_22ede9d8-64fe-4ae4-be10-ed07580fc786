import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { ProgramTimelineData, Program } from '@/shared/types/CommonInterface';
import CategorySetup from './CategorySetup';

interface AttentionModalProps {
  visible: boolean;
  onClose: () => void;
  program: ProgramTimelineData | null;
  fullProgramData?: Program;
  onSetupComplete?: (program: Program) => void;
  onArchiveProgram?: (programId: string) => void;
  isArchiving?: boolean;
}

export const AttentionModal: React.FC<AttentionModalProps> = ({
  visible,
  onClose,
  program,
  fullProgramData,
  onSetupComplete,
  onArchiveProgram,
  isArchiving = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  if (!program) return null;

  const renderSetupContent = () => {
    if (!fullProgramData) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading program details...</Text>
        </View>
      );
    }

    return (
      <View style={styles.setupContainer}>
        <Text style={styles.setupTitle}>Account Setup Required</Text>
        <Text style={styles.setupDescription}>
          Connect your account to unlock this challenge and become eligible for all features.
        </Text>
        
        <CategorySetup
          category={fullProgramData.category}
          selectedProgram={fullProgramData}
          updateSetupStatus={(updatedSetupStatus) => {
            if (updatedSetupStatus && onSetupComplete) {
              onSetupComplete({
                ...fullProgramData,
                setupStatus: updatedSetupStatus,
              });
            }
          }}
          updateSelectedProgram={(updatedProgram) => {
            if (onSetupComplete) {
              onSetupComplete(updatedProgram);
            }
          }}
          updateSignedUpProgram={() => {
            // This callback is handled at the parent level
          }}
        />
      </View>
    );
  };

  const renderDisqualifiedContent = () => (
    <View style={styles.contentContainer}>
      <MaterialIcons name="block" size={48} color={colors.error} style={styles.icon} />
      <Text style={styles.title}>Program Disqualified</Text>
      <Text style={styles.description}>
        You've been disqualified from "{program.programName}". 
        This program is no longer active for your account.
      </Text>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.archiveButton, { opacity: isArchiving ? 0.6 : 1 }]}
          onPress={() => onArchiveProgram?.(program.programId)}
          disabled={isArchiving}
        >
          {isArchiving ? (
            <ActivityIndicator size={20} color={colors.primary} />
          ) : (
            <MaterialIcons name="archive" size={20} color={colors.primary} />
          )}
          <Text style={styles.archiveButtonText}>
            {isArchiving ? 'Archiving...' : 'Archive Program'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEndedContent = () => (
    <View style={styles.contentContainer}>
      <MaterialIcons name="flag" size={48} color={colors.success} style={styles.icon} />
      <Text style={styles.title}>Program Completed</Text>
      <Text style={styles.description}>
        "{program.programName}" has ended. Check your results and see how you performed!
      </Text>
      
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.archiveButton, { opacity: isArchiving ? 0.6 : 1 }]}
          onPress={() => onArchiveProgram?.(program.programId)}
          disabled={isArchiving}
        >
          {isArchiving ? (
            <ActivityIndicator size={20} color={colors.primary} />
          ) : (
            <MaterialIcons name="archive" size={20} color={colors.primary} />
          )}
          <Text style={styles.archiveButtonText}>
            {isArchiving ? 'Archiving...' : 'Archive Program'}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderContent = () => {
    switch (program.attentionReason) {
      case 'setup':
        return renderSetupContent();
      case 'disqualified':
        return renderDisqualifiedContent();
      case 'ended':
        return renderEndedContent();
      default:
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.title}>Attention Required</Text>
            <Text style={styles.description}>
              This program needs your attention.
            </Text>
          </View>
        );
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>{program.programName}</Text>
            <TouchableOpacity
              style={styles.closeButton}
              onPress={onClose}
            >
              <MaterialIcons name="close" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          
          <ScrollView 
            style={styles.modalContent}
            showsVerticalScrollIndicator={false}
          >
            {renderContent()}
          </ScrollView>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    maxHeight: '80%',
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
    marginRight: 16,
  },
  closeButton: {
    padding: 4,
  },
  modalContent: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    alignItems: 'center',
  },
  icon: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  setupContainer: {
    padding: 20,
  },
  setupTitle: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  setupDescription: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 24,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 12,
  },
  actionButtons: {
    width: '100%',
    gap: 12,
  },
  archiveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 20,
    gap: 8,
  },
  archiveButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratMedium',
    color: colors.primary,
  },
});
