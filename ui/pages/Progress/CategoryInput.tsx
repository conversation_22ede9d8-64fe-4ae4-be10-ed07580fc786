import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  ActivityIndicator,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { firestoreService } from "../../../lib/services/database";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { VerificationInput } from "../../verification/VerificationInput";
import { ErrorModal, SuccessModal } from "@/shared/components/modals";
import { githubService, GitHubRepository } from "@/lib/services/githubService";
import {
  getVerificationConfig,
  VerificationData,
  VerificationResult,
  deserializeVerificationData,
  serializeVerificationData
} from "../../../shared/types/verification";

// Countdown Timer Component
const CountdownTimer: React.FC<{ colors: any }> = ({ colors }) => {
  const [timeLeft, setTimeLeft] = useState<string>("");

  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      const midnight = new Date();
      midnight.setHours(24, 0, 0, 0); // Next midnight

      const diff = midnight.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeLeft("00:00:00");
        return;
      }

      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const styles = StyleSheet.create({
    countdownContainer: {
      backgroundColor: colors.surface,
      paddingHorizontal: 12,
      paddingVertical: 10,
      borderRadius: 8,
      marginTop: 8,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: colors.warning + '30',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 8,
    },
    countdownText: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
    },
    countdownTime: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: colors.warning,
    },
  });

  return (
    <View style={styles.countdownContainer}>
      <Text style={styles.countdownText}>Time left to submit:</Text>
      <Text style={styles.countdownTime}>{timeLeft}</Text>
    </View>
  );
};

interface CategoryInputProps {
  program_id: string;
  category: string | undefined;
  permissionGranted: boolean | undefined;
  livesLeft: number;
  livesPurchaseLeft: number;
  day: number;
  user_id: string;
  onSubmissionSuccess: () => any;
  requestPermission: () => any;
  isLoading: boolean;
  // Add a refresh trigger to force re-checking of existing submissions
  refreshTrigger?: number;
}

const CategoryInput: React.FC<CategoryInputProps> = ({
  program_id,
  category,
  day,
  permissionGranted,
  livesLeft,
  livesPurchaseLeft,
  user_id,
  onSubmissionSuccess,
  isLoading,
  refreshTrigger = 0,
}) => {
  const { colors } = useTheme();

  // State for verification component
  const [initialVerificationData, setInitialVerificationData] = useState<VerificationData | null>(null);
  const [checkingExistingSubmission, setCheckingExistingSubmission] = useState<boolean>(true);
  const [isVerificationValid, setIsVerificationValid] = useState<boolean>(false);

  // Bailout state
  const [isBailingOut, setIsBailingOut] = useState(false);
  const [bailOutSuccessMessage, setBailOutSuccessMessage] = useState<string | null>(null);

  // Modal states for replacing Alert dialogs
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '' });

  // Check for existing submission on mount and when dependencies change
  useEffect(() => {
    const checkExistingSubmission = async () => {
      if (!program_id || !user_id || !day || !category) {
        setCheckingExistingSubmission(false);
        return;
      }

      setCheckingExistingSubmission(true);

      try {
        const dayX = `Day ${day}`;

        const existingSubmission = await firestoreService.submissions.getSubmission(
          program_id,
          user_id,
          dayX
        );

        if (existingSubmission.success && existingSubmission.data) {
          const submissionData = existingSubmission.data;

          // Try to deserialize verification data from attachment
          if (submissionData.attachment && typeof submissionData.attachment === 'string') {
            const verificationData = deserializeVerificationData(submissionData.attachment);
            if (verificationData) {
              setInitialVerificationData(verificationData);
            } else {
              // Handle legacy data format
              const config = getVerificationConfig(category);
              if (config.type === 'text') {
                setInitialVerificationData({
                  type: 'text',
                  textValue: submissionData.attachment,
                  timestamp: submissionData.timestamp || new Date().toISOString(),
                });
              } else if (config.type === 'camera') {
                setInitialVerificationData({
                  type: 'camera',
                  images: [submissionData.attachment],
                  timestamp: submissionData.timestamp || new Date().toISOString(),
                });
              } else if (config.type === 'camera+gps') {
                // Try to parse gym data
                try {
                  const gymData = JSON.parse(submissionData.attachment);
                  setInitialVerificationData({
                    type: 'camera+gps',
                    images: gymData.imageUrls || (gymData.imageUrl ? [gymData.imageUrl] : []),
                    location: gymData.gpsCoordinates || null,
                    timestamp: gymData.timestamp || new Date().toISOString(),
                  });
                } catch (error) {
                  console.error("Error parsing legacy gym data:", error);
                  setInitialVerificationData(null);
                }
              }
            }
          } else {
            setInitialVerificationData(null);
          }
        } else {
          setInitialVerificationData(null);
        }
      } catch (error) {
        // Error checking existing submission
        setInitialVerificationData(null);
      } finally {
        setCheckingExistingSubmission(false);
      }
    };

    checkExistingSubmission();
  }, [program_id, user_id, day, category, refreshTrigger]);

  // Modal state
  const [graceDayModalVisible, setGraceDayModalVisible] = useState(false);
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [purchaseCount, setPurchaseCount] = useState(1);
  const [showPurchaseDropdown, setShowPurchaseDropdown] = useState(false);

  // Handle verification submission
  const handleVerificationSubmit = async (data: VerificationData): Promise<VerificationResult> => {
    if (!category) {
      return { success: false, error: "Category is required" };
    }

    try {
      const dayX = `Day ${day}`;
      const serializedData = serializeVerificationData(data);

      // Update submission using centralized service
      const result = await firestoreService.submissions.updateSubmissionStatus(
        program_id,
        user_id,
        dayX,
        "submitted",
        serializedData
      );

      if (!result.success) {
        return { success: false, error: result.error || "Failed to update submission" };
      }

      // Trigger success callback
      onSubmissionSuccess();

      return { success: true, data };
    } catch (error) {
      console.error("Error during verification submission:", error);
      return { success: false, error: "An error occurred during submission" };
    }
  };



  const handleUseGraceDay = async () => {
    if (livesLeft <= 0) {
      setErrorModalData({
        title: "No Grace Days Left",
        message: "You have no grace days left. Please purchase more to use a grace day!"
      });
      setErrorModalVisible(true);
      return;
    }

    setIsBailingOut(true);
    const dayX = `Day ${day}`;

    try {
      // Get user email from AsyncStorage or your auth system
      const userEmail = await AsyncStorage.getItem('userEmail');

      // Use centralized service for grace day usage
      const result = await firestoreService.bailOutForDay(user_id, program_id, dayX);

      if (result.success) {
        // Create notification using user's email
        if (userEmail) {
          await firestoreService.notifications.createNotification(userEmail, {
            title: "Grace Day Used",
            message: `You've used a grace day for Day ${day}. ${livesLeft - 1} grace days remaining. Stay consistent tomorrow!`,
            type: "program",
            priority: "high"
          });
        }
      } else {
        throw new Error(result.error || "Grace day usage failed");
      }

      // Instead of alerting, set a success message to be shown in the modal
      setBailOutSuccessMessage(
        "You have used a grace day. Your streak is safe for today!"
      );
    } catch (error) {
      console.error("Error during grace day usage:", error);
      setErrorModalData({
        title: "Grace Day Failed",
        message: "An error occurred while using grace day."
      });
      setErrorModalVisible(true);
    } finally {
      setIsBailingOut(false);
    }
  };

  const handlePurchaseCountChange = (increment: boolean) => {
    if (increment) {
      if (purchaseCount < livesPurchaseLeft) {
        setPurchaseCount(purchaseCount + 1);
      }
    } else {
      if (purchaseCount > 1) {
        setPurchaseCount(purchaseCount - 1);
      }
    }
  };

  const handlePurchaseGraceDays = async () => {
    setIsPurchasing(true);

    try {
      const result = await firestoreService.participants.purchaseLives(
        program_id,
        user_id,
        purchaseCount
      );

      if (result.success) {
        setGraceDayModalVisible(false);
        onSubmissionSuccess();
      } else {
        setErrorModalData({
          title: "Error",
          message: result.error || "Failed to process purchase"
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error("Error during purchase:", error);
      setErrorModalData({
        title: "Error",
        message: "Failed to process purchase. Please try again."
      });
      setErrorModalVisible(true);
    } finally {
      setIsPurchasing(false);
      setPurchaseCount(1); // Reset counter after purchase
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.headerRow}>
        <Text style={styles.title}>
          {category === "journals" || category === "affirmations" || category === "writing"
            ? `Submit your ${
                category === "journals" ? "Daily Journal" :
                category === "affirmations" ? "Daily Affirmation" :
                category === "writing" ? "Daily Writing" : "Daily Submission"
              }`
            : category === "gym"
            ? "Submit your Gym Workout"
            : category === "cardio"
            ? "Submit your Strava link"
            : category === "coding"
            ? "GitHub Commit Verification"
            : "Submit your link"}
        </Text>
        <View style={styles.buttonGroup}>
          <TouchableOpacity
            style={[
              styles.graceDayButton,
              (livesLeft === 0 && livesPurchaseLeft === 0 || isLoading) && styles.disabled
            ]}
            onPress={() => setGraceDayModalVisible(true)}
            disabled={livesLeft === 0 && livesPurchaseLeft === 0 || isLoading}
          >
            <Text style={[
              styles.graceDayButtonText,
              isLoading && styles.disabledText
            ]}>
              Use Grace Day ({livesLeft})
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Countdown Timer - only show for ongoing unsubmitted programs */}
      {!checkingExistingSubmission && !initialVerificationData && category && (
        <CountdownTimer colors={colors} />
      )}

      {checkingExistingSubmission ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : category ? (
        <VerificationInput
          config={getVerificationConfig(category)}
          programId={program_id}
          userId={user_id}
          day={day}
          onSubmit={handleVerificationSubmit}
          onValidationChange={setIsVerificationValid}
          initialData={initialVerificationData}
          disabled={isLoading}
        />
      ) : null}

      <Modal
        transparent={true}
        visible={graceDayModalVisible}
        onRequestClose={() => setGraceDayModalVisible(false)}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={() => setGraceDayModalVisible(false)}
        >
          <View
            style={styles.modalContainer}
            onStartShouldSetResponder={() => true}
            onTouchEnd={(e) => {
              e.stopPropagation();
            }}
          >
            <View style={styles.modalContent}>
              {isBailingOut ? (
                <ActivityIndicator size="large" color={colors.primary} />
              ) : bailOutSuccessMessage ? (
                <>
                  <Text style={styles.modalTitle}>Grace Day Used!</Text>
                  <Text style={styles.modalMessage}>{bailOutSuccessMessage}</Text>
                  <TouchableOpacity
                    style={[styles.modalButton, styles.confirmButton]}
                    onPress={() => {
                      setGraceDayModalVisible(false);
                      setBailOutSuccessMessage(null);
                      onSubmissionSuccess();
                    }}
                  >
                    <Text style={styles.confirmButtonText}>OK</Text>
                  </TouchableOpacity>
                </>
              ) : (
                <>
                  <Text style={styles.modalTitle}>Grace Day Options</Text>

                  {/* Status Cards */}
                  <View style={styles.statusContainer}>
                    <View style={styles.statusCard}>
                      <Text style={styles.statusNumber}>{livesLeft}</Text>
                      <Text style={styles.statusLabel}>Current</Text>
                    </View>
                    <View style={styles.statusCard}>
                      <Text style={styles.statusNumber}>{livesPurchaseLeft}</Text>
                      <Text style={styles.statusLabel}>Available</Text>
                    </View>
                  </View>

                  {/* Use Grace Day Section - only show if user has grace days */}
                  {livesLeft > 0 && (
                    <View style={styles.actionSection}>
                      <View style={styles.actionHeader}>
                        <MaterialIcons name="today" size={20} color={colors.primary} />
                        <Text style={styles.actionTitle}>Use Grace Day</Text>
                      </View>
                      <Text style={styles.actionDescription}>
                        Skip today's submission and use one of your grace days
                      </Text>
                      <TouchableOpacity
                        style={[
                          styles.primaryActionButton,
                          isBailingOut && styles.disabled,
                        ]}
                        onPress={handleUseGraceDay}
                        disabled={isBailingOut}
                      >
                        {isBailingOut ? (
                          <ActivityIndicator size="small" color={colors.background} />
                        ) : (
                          <Text style={styles.primaryActionButtonText}>Use Grace Day</Text>
                        )}
                      </TouchableOpacity>
                    </View>
                  )}

                  {/* Purchase Grace Days Section - only show if purchase available */}
                  {livesPurchaseLeft > 0 && (
                    <View style={styles.actionSection}>
                      <TouchableOpacity
                        style={styles.dropdownHeader}
                        onPress={() => setShowPurchaseDropdown(!showPurchaseDropdown)}
                      >
                        <View style={styles.actionHeader}>
                          <MaterialIcons name="shopping-cart" size={20} color={colors.primary} />
                          <Text style={styles.actionTitle}>Purchase Grace Days</Text>
                        </View>
                        <MaterialIcons
                          name={showPurchaseDropdown ? "expand-less" : "expand-more"}
                          size={24}
                          color={colors.primary}
                        />
                      </TouchableOpacity>

                      {showPurchaseDropdown && (
                        <View style={styles.dropdownContent}>
                          <Text style={styles.actionDescription}>
                            Get additional grace days for this program
                          </Text>

                          <View style={styles.quantitySelector}>
                            <Text style={styles.quantityLabel}>Quantity:</Text>
                            <View style={styles.counterContainer}>
                              <TouchableOpacity
                                style={[
                                  styles.counterButton,
                                  purchaseCount <= 1 && styles.disabled
                                ]}
                                onPress={(e) => {
                                  e.stopPropagation();
                                  handlePurchaseCountChange(false);
                                }}
                                disabled={purchaseCount <= 1 || isPurchasing}
                              >
                                <Text style={styles.counterButtonText}>-</Text>
                              </TouchableOpacity>

                              <Text style={styles.counterText}>{purchaseCount}</Text>

                              <TouchableOpacity
                                style={[
                                  styles.counterButton,
                                  purchaseCount >= livesPurchaseLeft && styles.disabled
                                ]}
                                onPress={(e) => {
                                  e.stopPropagation();
                                  handlePurchaseCountChange(true);
                                }}
                                disabled={purchaseCount >= livesPurchaseLeft || isPurchasing}
                              >
                                <Text style={styles.counterButtonText}>+</Text>
                              </TouchableOpacity>
                            </View>
                          </View>

                          <TouchableOpacity
                            style={[styles.primaryActionButton, styles.purchaseActionButton]}
                            onPress={(e) => {
                              e.stopPropagation();
                              handlePurchaseGraceDays();
                            }}
                            disabled={isPurchasing}
                          >
                            {isPurchasing ? (
                              <ActivityIndicator size="small" color={colors.background} />
                            ) : (
                              <Text style={styles.primaryActionButtonText}>
                                Purchase {purchaseCount} Grace Day{purchaseCount > 1 ? 's' : ''}
                              </Text>
                            )}
                          </TouchableOpacity>
                        </View>
                      )}
                    </View>
                  )}

                  {/* Main Cancel Button - always visible */}
                  <View style={styles.cancelButtonContainer}>
                    <TouchableOpacity
                      style={styles.cancelOnlyButton}
                      onPress={() => {
                        setGraceDayModalVisible(false);
                        setPurchaseCount(1);
                        setShowPurchaseDropdown(false);
                      }}
                      disabled={isPurchasing || isBailingOut}
                    >
                      <Text style={styles.cancelOnlyButtonText}>Cancel</Text>
                    </TouchableOpacity>
                  </View>
                </>
              )}
            </View>
          </View>
        </TouchableOpacity>
      </Modal>

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
      />

    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: colors.card,
    borderRadius: 10,
    marginTop: 10,
  },
  headerRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  title: {
    fontSize: 14,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  buttonGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  purchaseButton: {
    backgroundColor: colors.surface,
    padding: 8,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  graceDayButton: {
    backgroundColor: colors.background,
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
  graceDayButtonText: {
    color: colors.warning,
    fontSize: 12,
  },
  loadingContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 40,
    backgroundColor: colors.surface,
    borderRadius: 12,
    marginBottom: 15,
  },
  loadingText: {
    color: colors.textMuted,
    fontSize: 14,
    fontFamily: "Montserrat",
    marginTop: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: '95%',
    maxWidth: 400,
  },
  modalContent: {
    backgroundColor: colors.card,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    width: '95%',
    maxWidth: 400,
    margin: 20,
  },
  modalTitle: {
    fontSize: 20,
    color: colors.primary,
    fontFamily: 'MontserratBold',
    marginBottom: 12,
    textAlign: 'center',
  },
  modalMessage: {
    fontSize: 16,
    color: colors.text,
    fontFamily: 'MontserratRegular',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    gap: 16,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120,
  },
  confirmButton: {
    backgroundColor: colors.primary,
  },
  cancelButton: {
    backgroundColor: colors.surface,
  },
  confirmButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  cancelButtonText: {
    color: colors.text,
    fontSize: 16,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  disabled: {
    opacity: 0.5,
    backgroundColor: colors.surface,
  },
  disabledText: {
    color: colors.textMuted,
  },
  disabledInput: {
    backgroundColor: colors.surface,
    color: colors.textMuted,
  },
  disabledButton: {
    opacity: 0.5,
    backgroundColor: colors.surface,
  },
  modalButtonText: {
    color: colors.text,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  useGraceDayButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  counterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    gap: 20,
  },
  counterButton: {
    backgroundColor: colors.primary,
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  counterButtonText: {
    fontSize: 24,
    color: colors.background,
    fontFamily: 'MontserratBold',
  },
  counterText: {
    fontSize: 24,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  sectionDivider: {
    marginVertical: 15,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  sectionTitle: {
    fontSize: 16,
    color: colors.primary,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  useGraceDayButton: {
    backgroundColor: colors.surface,
    marginBottom: 10,
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: colors.surface,
    borderRadius: 8,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  dropdownHeaderText: {
    fontSize: 16,
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  dropdownContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    backgroundColor: colors.surface,
    borderRadius: 8,
    marginTop: -8,
    borderWidth: 1,
    borderColor: colors.border,
    borderTopWidth: 0,
  },
  dropdownButtonContainer: {
    marginTop: 12,
    alignItems: 'center',
  },
  dropdownButton: {
    flex: 0,
    minWidth: 120,
    maxWidth: 200,
  },
  // Refined modal styles
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginVertical: 16,
    gap: 12,
  },
  statusCard: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statusNumber: {
    fontSize: 24,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 4,
  },
  statusLabel: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  actionSection: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: colors.border,
  },
  actionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.primary,
  },
  actionDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    marginTop: 8,
    marginBottom: 16,
    lineHeight: 20,
  },
  primaryActionButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 12,
  },
  primaryActionButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  purchaseActionButton: {
    backgroundColor: colors.success || colors.primary,
  },
  quantitySelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  quantityLabel: {
    fontSize: 14,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  cancelButtonContainer: {
    marginTop: 16,
    alignItems: 'center',
  },
  cancelOnlyButton: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderWidth: 1,
    borderColor: colors.border,
  },
  cancelOnlyButtonText: {
    color: colors.text,
    fontSize: 16,
    fontFamily: 'MontserratMedium',
  },
});

export default CategoryInput;