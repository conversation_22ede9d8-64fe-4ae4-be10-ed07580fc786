import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Animated,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import {
  CalendarTimelineProps,
  CalendarDayData,
  ProgramTimelineData
} from '@/shared/types/CommonInterface';
import { Commit } from '@/lib/services/database';
import {
  generateCalendarWeeks,
  getMonthName,
  navigateWeek,
  formatDateForDisplay
} from '@/lib/utils/calendarUtils';

// Platform-specific timeline dimensions
const getTimelineDimensions = () => {
  if (Platform.OS === 'web') {
    // Web-specific dimensions
    return {
      CALENDAR_PADDING: 20,
      DAY_SIZE: 65, // Fixed width for web
      COMPACT_DAY_HEIGHT: 42, // Reduced from 50 to decrease vertical padding
      EXPANDED_DAY_HEIGHT: 55, // Reduced from 65 to decrease vertical padding
    };
  } else {
    // Mobile dimensions
    const { width: SCREEN_WIDTH } = Dimensions.get('window');
    const CALENDAR_PADDING = 20;
    const DAY_SIZE = (SCREEN_WIDTH - CALENDAR_PADDING * 2) / 7;

    return {
      CALENDAR_PADDING,
      DAY_SIZE,
      COMPACT_DAY_HEIGHT: 50, // Reduced from 60 to decrease vertical padding
      EXPANDED_DAY_HEIGHT: 65, // Reduced from 80 to decrease vertical padding
    };
  }
};

const { CALENDAR_PADDING, DAY_SIZE, COMPACT_DAY_HEIGHT, EXPANDED_DAY_HEIGHT } = getTimelineDimensions();

// Program color palette for bands - theme-aware colors
const getProgramColors = (isDark: boolean) => {
  if (isDark) {
    // Bright colors for dark mode
    return [
      '#FF6B6B', // Red
      '#4ECDC4', // Teal
      '#45B7D1', // Blue
      '#96CEB4', // Green
      '#FFEAA7', // Yellow
      '#DDA0DD', // Plum
      '#98D8C8', // Mint
      '#F7DC6F', // Light Yellow
      '#BB8FCE', // Light Purple
      '#85C1E9', // Light Blue
      '#F8C471', // Orange
      '#82E0AA', // Light Green
    ];
  } else {
    // Darker, more saturated colors for light mode
    return [
      '#E53E3E', // Darker Red
      '#319795', // Darker Teal
      '#3182CE', // Darker Blue
      '#38A169', // Darker Green
      '#D69E2E', // Darker Yellow
      '#B794F6', // Darker Plum
      '#4FD1C7', // Darker Mint
      '#ECC94B', // Darker Light Yellow
      '#9F7AEA', // Darker Purple
      '#4299E1', // Darker Light Blue
      '#ED8936', // Darker Orange
      '#48BB78', // Darker Light Green
    ];
  }
};



// Helper function to get consistent color for a program
const getProgramBandColor = (programId: string, isDark: boolean): string => {
  // Create a simple hash from program ID to ensure consistent colors
  let hash = 0;
  for (let i = 0; i < programId.length; i++) {
    const char = programId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  const programColors = getProgramColors(isDark);
  const index = Math.abs(hash) % programColors.length;
  return programColors[index];
};

// Interface for program bands
interface ProgramBand {
  programId: string;
  programName: string;
  color: string;
  startDate: string;
  endDate: string;
  status: 'upcoming' | 'ongoing' | 'ended' | 'disqualified';
  needsAttention: boolean;
}

// Interface for commit bands
interface CommitBand {
  commitId: string;
  commitTitle: string;
  color: string;
  startDate: string;
  endDate: string;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  frequency: 'daily' | 'weekly' | 'monthly' | 'once';
}

// Helper function to generate program bands from programs
const generateProgramBands = (programs: ProgramTimelineData[], isDark: boolean): ProgramBand[] => {
  if (!programs || !Array.isArray(programs)) {
    return [];
  }

  return programs
    .filter(program => program && program.startDate && program.endDate)
    .map(program => ({
      programId: program.programId,
      programName: program.programName,
      color: getProgramBandColor(program.programId, isDark),
      startDate: program.startDate,
      endDate: program.endDate,
      status: program.status,
      needsAttention: program.needsAttention,
    }));
};

// Helper function to get consistent color for a commit
const getCommitBandColor = (commitId: string, isDark: boolean): string => {
  // Create a simple hash from commit ID to ensure consistent colors
  let hash = 0;
  for (let i = 0; i < commitId.length; i++) {
    const char = commitId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }

  // Use different color palette for commits to distinguish from programs
  const commitColors = isDark
    ? ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']
    : ['#E74C3C', '#16A085', '#3498DB', '#27AE60', '#F39C12', '#9B59B6', '#1ABC9C', '#F1C40F'];

  const index = Math.abs(hash) % commitColors.length;
  return commitColors[index];
};

// Helper function to generate commit bands from commits
const generateCommitBands = (commits: Commit[], isDark: boolean): CommitBand[] => {
  if (!commits || !Array.isArray(commits)) {
    return [];
  }

  return commits
    .filter(commit => commit && commit.schedule?.startDate && (commit.schedule?.endDate || commit.schedule?.duration))
    .map(commit => {
      let endDate = commit.schedule.endDate;

      // Calculate end date if not provided
      if (!endDate && commit.schedule.startDate && commit.schedule.duration) {
        const start = new Date(commit.schedule.startDate);
        const duration = commit.schedule.duration;

        switch (commit.schedule.frequency) {
          case 'daily':
            endDate = new Date(start.getTime() + (duration - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            break;
          case 'weekly':
            endDate = new Date(start.getTime() + (duration * 7 - 1) * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
            break;
          case 'monthly':
            const endMonth = new Date(start);
            endMonth.setMonth(endMonth.getMonth() + duration);
            endMonth.setDate(endMonth.getDate() - 1);
            endDate = endMonth.toISOString().split('T')[0];
            break;
          case 'once':
            endDate = commit.schedule.startDate;
            break;
        }
      }

      return {
        commitId: commit.id || '',
        commitTitle: commit.title || 'Untitled Commit',
        color: getCommitBandColor(commit.id || '', isDark),
        startDate: commit.schedule.startDate,
        endDate: endDate || commit.schedule.startDate,
        status: commit.status,
        frequency: commit.schedule.frequency,
      };
    });
};

// Algorithm: Interval Scheduling with Consistent Stack Assignment
// This solves the problem of maintaining visual consistency across calendar cells
// Similar to "Meeting Rooms II" but with global stack position assignment

interface ProgramInterval {
  programId: string;
  startDate: Date;
  endDate: Date;
  stackPosition?: number;
}

// Step 1: Assign consistent stack positions using interval scheduling algorithm
const assignGlobalStackPositions = (bands: ProgramBand[]): Map<string, number> => {
  if (!bands || bands.length === 0) return new Map();

  // Convert to intervals and sort by start date, then by end date
  const intervals: ProgramInterval[] = bands
    .map(band => ({
      programId: band.programId,
      startDate: new Date(band.startDate + 'T00:00:00'),
      endDate: new Date(band.endDate + 'T00:00:00'),
    }))
    .sort((a, b) => {
      if (a.startDate.getTime() !== b.startDate.getTime()) {
        return a.startDate.getTime() - b.startDate.getTime();
      }
      return a.endDate.getTime() - b.endDate.getTime();
    });

  // Greedy algorithm: Use minimum number of stacks
  const stacks: Date[] = []; // Each stack tracks the end time of its last program
  const stackAssignments = new Map<string, number>();

  for (const interval of intervals) {
    // Find the first available stack (earliest ending time that doesn't conflict)
    let assignedStack = -1;

    for (let i = 0; i < stacks.length; i++) {
      if (stacks[i] < interval.startDate) {
        assignedStack = i;
        stacks[i] = interval.endDate;
        break;
      }
    }

    // If no available stack found, create a new one
    if (assignedStack === -1) {
      assignedStack = stacks.length;
      stacks.push(interval.endDate);
    }

    stackAssignments.set(interval.programId, assignedStack);
  }

  return stackAssignments;
};

// Step 2: Get consistent stack position for a program (now O(1) lookup)
const getProgramStackPosition = (programId: string, stackAssignments: Map<string, number>): number => {
  return stackAssignments.get(programId) ?? 0;
};

// Helper function to render connected program bands across the calendar
const renderConnectedProgramBand = (
  band: ProgramBand,
  calendarData: CalendarDayData[],
  bandIndex: number,
  allBands: ProgramBand[],
  isExpanded: boolean,
  stackAssignments: Map<string, number>,
  isDark: boolean
) => {
  if (!band || !band.startDate || !band.endDate || !calendarData || !Array.isArray(calendarData)) {
    return null;
  }

  const bandStart = new Date(band.startDate + 'T00:00:00');
  const bandEnd = new Date(band.endDate + 'T00:00:00');
  const bandElements: JSX.Element[] = [];

  // Group calendar data into weeks (rows of 7)
  const weeks: CalendarDayData[][] = [];
  for (let i = 0; i < calendarData.length; i += 7) {
    weeks.push(calendarData.slice(i, i + 7));
  }

  const dayHeight = isExpanded ? EXPANDED_DAY_HEIGHT : COMPACT_DAY_HEIGHT;

  weeks.forEach((week, weekIndex) => {
    week.forEach((day, dayIndex) => {
      const dayDate = new Date(day.date + 'T00:00:00');
      const isInBand = dayDate >= bandStart && dayDate <= bandEnd && day.isCurrentMonth;

      if (isInBand) {
        // Use global stack assignment for consistent positioning
        const thisProgramStackPosition = getProgramStackPosition(band.programId, stackAssignments);

        // Get programs active on this specific date for calculating total height needed
        const activeProgramsOnThisDay = allBands.filter(b => {
          if (!b || !b.startDate || !b.endDate) return false;
          const bStart = new Date(b.startDate + 'T00:00:00');
          const bEnd = new Date(b.endDate + 'T00:00:00');
          return dayDate >= bStart && dayDate <= bEnd && day.isCurrentMonth;
        });

        const totalProgramsOnThisDay = activeProgramsOnThisDay.length;

        if (totalProgramsOnThisDay > 0) {
          // Calculate band dimensions
          const availableHeight = dayHeight * 0.6; // Use 60% of day height for bands
          const bandHeight = Math.max(availableHeight / totalProgramsOnThisDay, 8); // Minimum 8px height
          const totalBandsHeight = bandHeight * totalProgramsOnThisDay;
          const startOffset = (dayHeight - totalBandsHeight) / 2 + 20; // Offset from day number

          // Determine if this is first or last cell in the band for border radius
          const isFirstInBand = dayIndex === 0 || !week[dayIndex - 1] ||
            new Date(week[dayIndex - 1].date + 'T00:00:00') < bandStart;
          const isLastInBand = dayIndex === 6 || !week[dayIndex + 1] ||
            new Date(week[dayIndex + 1].date + 'T00:00:00') > bandEnd;

          bandElements.push(
            <View
              key={`${band.programId}-${weekIndex}-${dayIndex}`}
              style={{
                position: 'absolute',
                left: `${(dayIndex / 7) * 100}%`,
                width: `${(1 / 7) * 100}%`,
                height: bandHeight,
                backgroundColor: band.color + (isDark ? '30' : '25'), // Slightly less opacity in light mode
                borderWidth: isDark ? 0.5 : 1, // Thicker border in light mode for better visibility
                borderColor: band.color + (isDark ? '60' : '80'), // More opaque border in light mode
                top: weekIndex * dayHeight + startOffset + (thisProgramStackPosition * bandHeight),
                zIndex: 1,
                // Border radius only on first and last cells
                borderTopLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 4) : 0,
                borderBottomLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 4) : 0,
                borderTopRightRadius: isLastInBand ? Math.min(bandHeight / 3, 4) : 0,
                borderBottomRightRadius: isLastInBand ? Math.min(bandHeight / 3, 4) : 0,
                // Minimal vertical gaps between stacks
                marginTop: thisProgramStackPosition > 0 ? 1 : 0,
              }}
            />
          );
        }
      }
    });
  });

  return bandElements;
};

// Helper function to render connected commit bands with dotted styling
const renderConnectedCommitBand = (
  band: CommitBand,
  calendarData: CalendarDayData[],
  bandIndex: number,
  allBands: CommitBand[],
  isExpanded: boolean,
  stackAssignments: Map<string, number>,
  isDark: boolean
) => {
  if (!band || !band.startDate || !band.endDate || !calendarData || !Array.isArray(calendarData)) {
    return null;
  }

  const bandStart = new Date(band.startDate + 'T00:00:00');
  const bandEnd = new Date(band.endDate + 'T00:00:00');
  const bandElements: JSX.Element[] = [];

  // Group calendar data into weeks (rows of 7)
  const weeks: CalendarDayData[][] = [];
  for (let i = 0; i < calendarData.length; i += 7) {
    weeks.push(calendarData.slice(i, i + 7));
  }

  const dayHeight = isExpanded ? EXPANDED_DAY_HEIGHT : COMPACT_DAY_HEIGHT;

  weeks.forEach((week, weekIndex) => {
    week.forEach((day, dayIndex) => {
      const dayDate = new Date(day.date + 'T00:00:00');
      const isInBand = dayDate >= bandStart && dayDate <= bandEnd && day.isCurrentMonth;

      if (isInBand) {
        // Use global stack assignment for consistent positioning
        const thisCommitStackPosition = getCommitStackPosition(band.commitId, stackAssignments);

        // Get commits active on this specific date for calculating total height needed
        const activeCommitsOnThisDay = allBands.filter(b => {
          if (!b || !b.startDate || !b.endDate) return false;
          const bStart = new Date(b.startDate + 'T00:00:00');
          const bEnd = new Date(b.endDate + 'T00:00:00');
          return dayDate >= bStart && dayDate <= bEnd && day.isCurrentMonth;
        });

        const totalCommitsOnThisDay = activeCommitsOnThisDay.length;

        if (totalCommitsOnThisDay > 0) {
          // Calculate band dimensions - commits use bottom 40% of day height
          const availableHeight = dayHeight * 0.4; // Use bottom 40% for commits
          const bandHeight = Math.max(availableHeight / totalCommitsOnThisDay, 6); // Minimum 6px height
          const totalBandsHeight = bandHeight * totalCommitsOnThisDay;
          const startOffset = dayHeight - totalBandsHeight - 5; // Position at bottom with 5px margin

          // Determine if this is first or last cell in the band for border radius
          const isFirstInBand = dayIndex === 0 || !week[dayIndex - 1] ||
            new Date(week[dayIndex - 1].date + 'T00:00:00') < bandStart;
          const isLastInBand = dayIndex === 6 || !week[dayIndex + 1] ||
            new Date(week[dayIndex + 1].date + 'T00:00:00') > bandEnd;

          bandElements.push(
            <View
              key={`${band.commitId}-${weekIndex}-${dayIndex}`}
              style={{
                position: 'absolute',
                left: `${(dayIndex / 7) * 100}%`,
                width: `${(1 / 7) * 100}%`,
                height: bandHeight,
                backgroundColor: band.color + (isDark ? '20' : '15'), // More transparent than programs
                borderWidth: 1,
                borderColor: band.color + (isDark ? '80' : '90'),
                borderStyle: 'dashed', // Dotted/dashed style for commits
                top: weekIndex * dayHeight + startOffset + (thisCommitStackPosition * bandHeight),
                zIndex: 0, // Below programs
                // Border radius only on first and last cells
                borderTopLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 3) : 0,
                borderBottomLeftRadius: isFirstInBand ? Math.min(bandHeight / 3, 3) : 0,
                borderTopRightRadius: isLastInBand ? Math.min(bandHeight / 3, 3) : 0,
                borderBottomRightRadius: isLastInBand ? Math.min(bandHeight / 3, 3) : 0,
                // Minimal vertical gaps between stacks
                marginTop: thisCommitStackPosition > 0 ? 1 : 0,
              }}
            />
          );
        }
      }
    });
  });

  return bandElements;
};

// Helper function to get commit stack position
const getCommitStackPosition = (commitId: string, stackAssignments: Map<string, number>): number => {
  return stackAssignments.get(commitId) || 0;
};

const CalendarTimelineComponent: React.FC<CalendarTimelineProps> = ({
  programs,
  commits = [],
  onDateSelect,
  onProgramAttention,
  selectedDate,
}) => {
  const { colors, isDark } = useTheme();
  const styles = createStyles(colors);

  const [currentDate, setCurrentDate] = useState(new Date());
  const [isExpanded, setIsExpanded] = useState(false);
  const [weeksToShow, setWeeksToShow] = useState(1); // 1 or 2 weeks
  
  const calendarData = useMemo(() => {
    // Filter programs that have valid dates
    const validPrograms = programs.filter(p => p.startDate && (p.endDate || p.duration));

    if (isExpanded) {
      // Show full month when expanded
      return generateCalendarWeeks(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        validPrograms,
        6 // Show full month (up to 6 weeks)
      );
    } else {
      // Show limited weeks when compact
      return generateCalendarWeeks(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        validPrograms,
        weeksToShow,
        currentDate // Start from current week
      );
    }
  }, [currentDate, programs, isExpanded, weeksToShow]);

  // Generate program bands
  const programBands = useMemo(() => {
    const validPrograms = programs.filter(p => p.startDate && (p.endDate || p.duration));
    return generateProgramBands(validPrograms as any, isDark);
  }, [programs, isDark]);

  // Generate commit bands
  const commitBands = useMemo(() => {
    const validCommits = commits.filter(c => c && c.schedule?.startDate);
    return generateCommitBands(validCommits as Commit[], isDark);
  }, [commits, isDark]);

  // Generate global stack assignments for consistent visual lines (programs only)
  const stackAssignments = useMemo(() => {
    return assignGlobalStackPositions(programBands);
  }, [programBands]);

  // Generate separate stack assignments for commits
  const commitStackAssignments = useMemo(() => {
    return assignGlobalStackPositions(commitBands.map(cb => ({
      programId: cb.commitId,
      startDate: cb.startDate,
      endDate: cb.endDate,
      programName: cb.commitTitle,
      color: cb.color,
      status: cb.status as any,
      needsAttention: false
    })));
  }, [commitBands]);

  const hasValidPrograms = programs.some(p => p.startDate && (p.endDate || p.duration));

  const handlePrevWeek = useCallback(() => {
    setCurrentDate(navigateWeek(currentDate, 'prev'));
  }, [currentDate]);

  const handleNextWeek = useCallback(() => {
    setCurrentDate(navigateWeek(currentDate, 'next'));
  }, [currentDate]);

  const handleDatePress = useCallback((dayData: CalendarDayData) => {
    if (dayData.hasPrograms) {
      // Only pass the first program for single program display
      const singleProgramDayData = {
        ...dayData,
        programs: dayData.programs.length > 0 ? [dayData.programs[0]] : []
      };
      onDateSelect(dayData.date, singleProgramDayData);
    }
  }, [onDateSelect]);

  const handleExpandToggle = useCallback(() => {
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const handleWeeksToggle = useCallback(() => {
    setWeeksToShow(weeksToShow === 1 ? 2 : 1);
  }, [weeksToShow]);


  


  const renderDayStatus = (dayData: CalendarDayData) => {
    if (!dayData.hasPrograms || dayData.programs.length === 0) return null;

    // Show status indicator for the first program only
    const program = dayData.programs[0];
    let statusIcon = '📅';

    if (program.needsAttention) {
      statusIcon = '⚠️';
    } else {
      switch (program.status) {
        case 'ongoing': statusIcon = '🔥'; break;
        case 'upcoming': statusIcon = '⏳'; break;
        case 'ended': statusIcon = '✅'; break;
        case 'disqualified': statusIcon = '❌'; break;
        default: statusIcon = '📅';
      }
    }

    return (
      <View style={styles.dayStatus}>
        <Text style={styles.dayStatusText}>
          {statusIcon}
        </Text>
      </View>
    );
  };

  const renderCalendarDay = (dayData: CalendarDayData, index: number) => {
    const isSelected = selectedDate === dayData.date;
    const hasPrograms = dayData.hasPrograms;
    const needsAttention = dayData.needsAttention;

    // Calculate position for border styling
    const isLastRow = index >= calendarData.length - 7;
    const isFirstCol = index % 7 === 0;
    const isLastCol = index % 7 === 6;

    return (
      <TouchableOpacity
        key={dayData.date}
        style={[
          styles.dayContainer,
          { height: isExpanded ? EXPANDED_DAY_HEIGHT : COMPACT_DAY_HEIGHT },
          !dayData.isCurrentMonth && styles.dayContainerInactive,
          isSelected && styles.dayContainerSelected,
          dayData.isToday && styles.dayContainerToday,
          hasPrograms && styles.dayContainerWithPrograms,
          needsAttention && styles.dayContainerAttention,
          isLastRow && styles.dayContainerLastRow,
          isLastCol && styles.dayContainerLastCol,
          isFirstCol && styles.dayContainerFirstCol,
        ]}
        onPress={() => handleDatePress(dayData)}
        disabled={!hasPrograms}
      >
        {/* Day Number */}
        <View style={styles.dayHeader}>
          <Text
            style={[
              styles.dayText,
              !dayData.isCurrentMonth && styles.dayTextInactive,
              isSelected && styles.dayTextSelected,
              dayData.isToday && styles.dayTextToday,
              needsAttention && styles.dayTextAttention,
            ]}
          >
            {dayData.day}
          </Text>
          {!isExpanded && renderDayStatus(dayData)}
        </View>
      </TouchableOpacity>
    );
  };
  
  if (!hasValidPrograms) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <MaterialIcons name="event-busy" size={48} color={colors.textMuted} />
          <Text style={styles.emptyStateTitle}>No Programs with Dates</Text>
          <Text style={styles.emptyStateDescription}>
            Programs need start and end dates to appear in the calendar view.
          </Text>
        </View>
      </View>
    );
  }



  return (
    <View style={styles.container}>
      {/* Calendar Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.navButton}
          onPress={handlePrevWeek}
        >
          <MaterialIcons name="chevron-left" size={24} color={colors.text} />
        </TouchableOpacity>

        <View style={styles.headerCenter}>
          <Text style={styles.monthTitle}>
            {getMonthName(currentDate.getMonth())} {currentDate.getFullYear()}
          </Text>
          {!isExpanded && (
            <Text style={styles.weekInfo}>
              {weeksToShow === 1 ? 'This Week' : 'Next 2 Weeks'}
            </Text>
          )}
        </View>

        <TouchableOpacity
          style={styles.navButton}
          onPress={handleNextWeek}
        >
          <MaterialIcons name="chevron-right" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      {/* Calendar Controls */}
      <View style={styles.controls}>
        {!isExpanded && (
          <TouchableOpacity
            style={styles.controlButton}
            onPress={handleWeeksToggle}
          >
            <Text style={styles.controlButtonText}>
              {weeksToShow === 1 ? '2 Weeks' : '1 Week'}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, styles.expandButton]}
          onPress={handleExpandToggle}
        >
          <MaterialIcons
            name={isExpanded ? "expand-less" : "expand-more"}
            size={20}
            color={colors.text}
          />
          <Text style={styles.controlButtonText}>
            {isExpanded ? 'Collapse' : 'Expand'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Day Headers */}
      <View style={styles.dayHeaders}>
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
          <View
            key={day}
            style={[
              styles.dayHeaderCell,
              index === 0 && styles.dayHeaderCellFirst,
              index === 6 && styles.dayHeaderCellLast,
            ]}
          >
            <Text style={styles.dayHeaderText}>{day}</Text>
          </View>
        ))}
      </View>

      {/* Calendar Grid with Program Bands */}
      <View style={styles.calendarContainer}>
        {/* Connected program bands layer */}
        <View style={styles.bandsLayer}>
          {/* Render commit bands first (below programs) */}
          {commitBands && Array.isArray(commitBands) && commitBands.map((band, bandIndex) => {
            return renderConnectedCommitBand(band, calendarData, bandIndex, commitBands, isExpanded, commitStackAssignments, isDark);
          })}

          {/* Render program bands on top */}
          {programBands && Array.isArray(programBands) && programBands.map((band, bandIndex) => {
            return renderConnectedProgramBand(band, calendarData, bandIndex, programBands, isExpanded, stackAssignments, isDark);
          })}
        </View>

        {/* Calendar grid */}
        <View style={styles.calendarGrid}>
          {calendarData.map((dayData, index) => renderCalendarDay(dayData, index))}
        </View>
      </View>



      {/* Legend - only show when expanded */}
      {isExpanded && (
        <View style={styles.legend}>
          <View style={styles.legendItem}>
            <Text style={styles.legendEmoji}>🔥</Text>
            <Text style={styles.legendText}>Ongoing</Text>
          </View>
          <View style={styles.legendItem}>
            <Text style={styles.legendEmoji}>⏳</Text>
            <Text style={styles.legendText}>Upcoming</Text>
          </View>
          <View style={styles.legendItem}>
            <Text style={styles.legendEmoji}>✅</Text>
            <Text style={styles.legendText}>Completed</Text>
          </View>
          <View style={styles.legendItem}>
            <Text style={styles.legendEmoji}>⚠️</Text>
            <Text style={styles.legendText}>Needs Attention</Text>
          </View>
        </View>
      )}
    </View>
  );
};

// Export optimized component with React.memo for better performance
export const CalendarTimeline = React.memo(CalendarTimelineComponent, (prevProps, nextProps) => {
  // Custom comparison function for better memoization
  return (
    prevProps.programs === nextProps.programs &&
    prevProps.commits === nextProps.commits &&
    prevProps.selectedDate === nextProps.selectedDate &&
    prevProps.onDateSelect === nextProps.onDateSelect &&
    prevProps.onProgramAttention === nextProps.onProgramAttention
  );
});

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  headerCenter: {
    alignItems: 'center',
    flex: 1,
  },
  navButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: colors.background,
  },
  monthTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  weekInfo: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 2,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: colors.background,
    gap: 4,
  },
  expandButton: {
    backgroundColor: colors.primary + '20',
  },
  controlButtonText: {
    fontSize: 12,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  dayHeaders: {
    flexDirection: 'row',
    marginBottom: 0,
  },
  dayHeaderCell: {
    width: DAY_SIZE,
    paddingVertical: 8,
    backgroundColor: colors.surface,
    borderRightWidth: 1,
    borderBottomWidth: 1,
    borderTopWidth: 1,
    borderColor: colors.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dayHeaderText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
  },
  dayHeaderCellFirst: {
    borderTopLeftRadius: 8,
    borderLeftWidth: 1,
  },
  dayHeaderCellLast: {
    borderTopRightRadius: 8,
  },
  calendarContainer: {
    position: 'relative',
    width: '100%',
  },
  bandsLayer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: 8,
    borderTopWidth: 0, // Remove top border since day headers have it
    zIndex: 2,
  },
  dayContainer: {
    width: DAY_SIZE,
    height: COMPACT_DAY_HEIGHT, // Default to compact height
    borderRightWidth: 1,
    borderBottomWidth: 1,
    // Web-specific improvements
    ...Platform.select({
      web: {
        cursor: 'pointer',
        transition: 'background-color 0.2s ease',
        userSelect: 'none',
      },
    }),
    borderColor: colors.border,
    padding: 4,
    backgroundColor: colors.background,
    justifyContent: 'flex-start',
  },
  dayContainerInactive: {
    opacity: 0.4,
    backgroundColor: colors.surface,
  },
  dayContainerSelected: {
    backgroundColor: colors.primary + '20', // Light primary color
    borderColor: colors.primary,
    borderWidth: 2,
  },
  dayContainerToday: {
    backgroundColor: colors.primary + '10',
    borderColor: colors.primary,
    borderWidth: 2,
  },
  dayContainerWithPrograms: {
    backgroundColor: colors.surface,
  },
  dayContainerAttention: {
    backgroundColor: colors.error + '10',
    borderColor: colors.error || '#ff4444',
    borderWidth: 2,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
    width: '100%',
  },
  dayText: {
    fontSize: 14,
    fontFamily: 'MontserratMedium',
    color: colors.text,
  },
  dayTextInactive: {
    color: colors.textMuted,
  },
  dayTextSelected: {
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  dayTextToday: {
    color: colors.primary,
    fontFamily: 'MontserratBold',
  },
  dayTextAttention: {
    color: colors.error || '#ff4444',
    fontFamily: 'MontserratBold',
  },
  dayStatus: {
    flexDirection: 'row',
  },
  dayStatusText: {
    fontSize: 10,
  },
  morePrograms: {
    backgroundColor: colors.textMuted,
    paddingHorizontal: 4,
    paddingVertical: 2,
    borderRadius: 4,
    minHeight: 16,
    justifyContent: 'center',
  },
  moreProgramsText: {
    fontSize: 8,
    fontFamily: 'MontserratMedium',
    color: colors.background,
    textAlign: 'center',
  },
  dayContainerLastRow: {
    borderBottomWidth: 1,
  },
  dayContainerLastCol: {
    borderRightWidth: 0,
  },
  dayContainerFirstCol: {
    borderLeftWidth: 1,
  },

  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: colors.border,
    flexWrap: 'wrap',
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: '20%',
  },
  legendEmoji: {
    fontSize: 12,
    marginRight: 4,
  },
  legendText: {
    fontSize: 10,
    color: colors.textMuted,
    fontFamily: 'MontserratRegular',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});
