import React, { useMemo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
} from "react-native";
import { FlashList } from "@shopify/flash-list";
import { useRouter } from "expo-router";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { MaterialIcons } from '@expo/vector-icons';

interface Participant {
  id: string;
  fname: string;
  status: string;
  borderColor: string;
  livesLeft: number;
}

interface MoneyStats {
  activeCount: number;
  activeMoney: number;
  disqualifiedCount: number;
  disqualifiedMoney: number;
  userMoneyBack: number;
  userShare: number;
}

interface ParticipantsStatusDashboardProps {
  participants: Participant[];
  currentUserId: string;
  today: boolean;
  moneyStats?: MoneyStats | null;
}

const getInitials = (name: string): string => {
  const parts = name.trim().split(" ");
  return parts.length === 1
    ? parts[0].charAt(0).toUpperCase()
    : (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
};

const getStatusColor = (status: string, colors: any) => {
  switch (status.toLowerCase()) {
    case "submitted":
    case "bailed":
      return colors.success; // Green

    case "upcoming":
    case "not submitted yet":
      return colors.primary; // Yellow
    default:
      return colors.primary; // Default yellow
  }
};

const ParticipantsStatusDashboard: React.FC<
  ParticipantsStatusDashboardProps
> = ({ participants, currentUserId, today, moneyStats }) => {
  const router = useRouter();
  const { colors } = useTheme();
  const styles = createStyles(colors);

  // Fix summary counts to check for "upcoming" status
  const submittedCount = participants.filter(
    (p) => p.status.toLowerCase() === "verified"
  ).length;

  const bailedCount = participants.filter(
    (p) => p.status.toLowerCase() === "bailed"
  ).length;

  const notSubmittedCount = participants.filter(
    (p) => p.status.toLowerCase() === "not_submitted" // Changed from "not submitted yet" to "upcoming"
  ).length;



  // Modal state for viewing full participant list with filtering
  const [modalVisible, setModalVisible] = React.useState(false);
  const [statusFilter, setStatusFilter] = React.useState("All");

  const filters = ["All", "Submitted", "Bailed", "Not Submitted Yet"];

  // Update filtered participants logic to handle "Not Submitted Yet" filter
  const filteredParticipants = useMemo(() => {
    if (statusFilter === "All") return participants;

    if (statusFilter === "Not Submitted Yet") {
      return participants.filter((p) => p.status.toLowerCase() === "upcoming");
    }

    return participants.filter(
      (p) => p.status.toLowerCase() === statusFilter.toLowerCase()
    );
  }, [participants, statusFilter]);

  const renderParticipant = ({ item }: { item: Participant }) => (
    <View style={styles.participantRow}>
      <View style={[styles.circle, { borderColor: item.borderColor }]}>
        <Text style={styles.circleText}>{getInitials(item.fname)}</Text>
      </View>
      <TouchableOpacity
        style={styles.nameButton}
        onPress={() => router.push(`/UserProfile?userId=${item.id}`)}
      >
        <Text style={styles.participantName}>{item.fname}</Text>
      </TouchableOpacity>
      <Text style={styles.participantStatus}>
        {item.status === "upcoming"
          ? today
            ? "Not Submitted Yet"
            : "Not Submitted"
          : item.status}
      </Text>
    </View>
  );

  return (
    <View style={styles.dashboardContainer}>
      {/* Unified Dashboard */}
      <View style={styles.unifiedContainer}>
        {/* Header with Title and View All */}
        <View style={styles.headerRow}>
          <View style={styles.titleContainer}>
            <MaterialIcons name="analytics" size={20} color={colors.primary} />
            <Text style={styles.sectionTitle}>Today's Stats</Text>
          </View>
          <TouchableOpacity
            style={styles.viewAllButton}
            onPress={() => setModalVisible(true)}
          >
            <MaterialIcons name="people" size={14} color={colors.primary} />
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>

        {/* Participant Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, styles.submittedCard]}>
            <View style={styles.statIconContainer}>
              <MaterialIcons name="check-circle" size={16} color="#4CAF50" />
            </View>
            <Text style={styles.statNumber}>{submittedCount}</Text>
            <Text style={styles.statText}>Done</Text>
          </View>

          <View style={[styles.statCard, styles.bailedCard]}>
            <View style={styles.statIconContainer}>
              <MaterialIcons name="shield" size={16} color="#FF9800" />
            </View>
            <Text style={styles.statNumber}>{bailedCount}</Text>
            <Text style={styles.statText}>Bailed</Text>
          </View>

          <View style={[styles.statCard, styles.pendingCard]}>
            <View style={styles.statIconContainer}>
              <MaterialIcons name="schedule" size={16} color={colors.primary} />
            </View>
            <Text style={styles.statNumber}>{notSubmittedCount}</Text>
            <Text style={styles.statText}>{today ? "Pending" : "Missing"}</Text>
          </View>
        </View>

        {/* Money Overview Section */}
        {moneyStats && (
          <View style={styles.moneySection}>
            <View style={styles.prizePoolHeader}>
              <MaterialIcons name="monetization-on" size={18} color="#FFD700" />
              <Text style={styles.prizePoolTitle}>Prize Pool</Text>
            </View>

            {/* Pool Stats Row */}
            <View style={styles.poolStatsRow}>
              <View style={styles.poolStatCard}>
                <View style={styles.poolStatHeader}>
                  <MaterialIcons name="people" size={14} color="#4CAF50" />
                  <Text style={styles.poolStatLabel}>Active</Text>
                </View>
                <Text style={styles.poolStatValue}>{moneyStats.activeCount}</Text>
                <Text style={styles.poolStatMoney}>${moneyStats.activeMoney}</Text>
              </View>

              <View style={styles.poolStatCard}>
                <View style={styles.poolStatHeader}>
                  <MaterialIcons name="person-off" size={14} color="#F44336" />
                  <Text style={styles.poolStatLabel}>Out</Text>
                </View>
                <Text style={styles.poolStatValue}>{moneyStats.disqualifiedCount}</Text>
                <Text style={styles.poolStatMoney}>${moneyStats.disqualifiedMoney}</Text>
              </View>
            </View>

            {/* Potential Winnings */}
            <View style={styles.winningsContainer}>
              <View style={styles.winningsHeader}>
                <MaterialIcons name="emoji-events" size={16} color="#FFD700" />
                <Text style={styles.winningsTitle}>Your Potential Winnings</Text>
              </View>
              <View style={styles.winningsGrid}>
                <View style={styles.winningsItem}>
                  <Text style={styles.winningsLabel}>Money Back</Text>
                  <Text style={styles.winningsValue}>${moneyStats.userMoneyBack}</Text>
                </View>
                <Text style={styles.plusSign}>+</Text>
                <View style={styles.winningsItem}>
                  <Text style={styles.winningsLabel}>Bonus Share</Text>
                  <Text style={styles.winningsValue}>${moneyStats.userShare.toFixed(2)}</Text>
                </View>
              </View>
            </View>
          </View>
        )}
      </View>

      {/* Modal for Participants List with Filtering */}
      <Modal
        visible={modalVisible}
        animationType="slide"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalContainer}>
          <Text style={styles.modalTitle}>Participants</Text>
          <View style={styles.filterContainer}>
            {filters.map((filter) => (
              <TouchableOpacity
                key={filter}
                style={[
                  styles.filterButton,
                  filter === statusFilter && styles.activeFilterButton,
                ]}
                onPress={() => setStatusFilter(filter)}
              >
                <Text
                  style={[
                    styles.filterButtonText,
                    filter === statusFilter && styles.activeFilterButtonText,
                  ]}
                >
                  {filter}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
          <FlashList
            data={filteredParticipants}
            keyExtractor={(item) => item.id}
            renderItem={renderParticipant}
            estimatedItemSize={50}
            contentContainerStyle={styles.modalListContainer}
          />
          <TouchableOpacity
            style={styles.modalCloseButton}
            onPress={() => setModalVisible(false)}
          >
            <Text style={styles.modalCloseButtonText}>Close</Text>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  dashboardContainer: {
    backgroundColor: colors.background,
    outline: 'none',
  },
  unifiedContainer: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 12,
    marginTop: 4,
    marginBottom: 2,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },

  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: colors.primary + '15',
    borderRadius: 8,
    gap: 4,
    outline: 'none',
    borderWidth: 1,
    borderColor: colors.primary + '30',
  },
  viewAllText: {
    fontSize: 11,
    color: colors.primary,
    fontFamily: 'MontserratSemiBold',
  },
  statsGrid: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 10,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: 10,
    padding: 10,
    alignItems: "center",
    gap: 4,
    borderWidth: 1,
    borderColor: colors.border + '40',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  statIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 8,
    backgroundColor: colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 1,
  },
  submittedCard: {
    borderColor: '#4CAF50' + '40',
    backgroundColor: '#4CAF50' + '10',
  },
  bailedCard: {
    borderColor: '#FF9800' + '40',
    backgroundColor: '#FF9800' + '10',
  },
  pendingCard: {
    borderColor: colors.primary + '40',
    backgroundColor: colors.primary + '10',
  },
  statNumber: {
    fontSize: 20,
    color: colors.text,
    fontFamily: 'MontserratBold',
  },
  statText: {
    fontSize: 10,
    color: colors.textSecondary,
    fontFamily: 'MontserratSemiBold',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  // Money Overview Styles
  moneySection: {
    borderTopWidth: 1,
    borderTopColor: '#FFD700' + '30',
    paddingTop: 10,
    marginTop: 2,
  },
  prizePoolHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 8,
  },
  prizePoolTitle: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  poolStatsRow: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 10,
  },
  poolStatCard: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border + '30',
  },
  poolStatHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 4,
  },
  poolStatLabel: {
    fontSize: 10,
    color: colors.textSecondary,
    fontFamily: 'MontserratSemiBold',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  poolStatValue: {
    fontSize: 18,
    color: colors.text,
    fontFamily: 'MontserratBold',
    marginBottom: 1,
  },
  poolStatMoney: {
    fontSize: 11,
    color: colors.textMuted,
    fontFamily: 'MontserratMedium',
  },
  winningsContainer: {
    backgroundColor: '#FFD700' + '15',
    borderRadius: 10,
    padding: 10,
    borderWidth: 2,
    borderColor: '#FFD700' + '40',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  winningsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    marginBottom: 8,
  },
  winningsTitle: {
    fontSize: 12,
    color: colors.text,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
  winningsGrid: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
  },
  winningsItem: {
    alignItems: 'center',
    gap: 2,
  },
  winningsLabel: {
    fontSize: 9,
    color: colors.textSecondary,
    fontFamily: 'MontserratSemiBold',
    textAlign: 'center',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  winningsValue: {
    fontSize: 16,
    color: colors.text,
    fontFamily: 'MontserratBold',
  },
  plusSign: {
    fontSize: 18,
    color: '#FFD700',
    fontFamily: 'MontserratBold',
    marginHorizontal: 8,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.background,
    padding: 16,
  },
  modalTitle: {
    fontSize: 22,
    color: colors.primary,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 10,
  },
  filterContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: 10,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: colors.surface,
    outline: 'none',
  },
  activeFilterButton: {
    backgroundColor: colors.primary,
  },
  filterButtonText: {
    color: colors.text,
    fontSize: 14,
  },
  activeFilterButtonText: {
    color: colors.background,
    fontWeight: "bold",
  },
  modalListContainer: {
    paddingBottom: 20,
  },
  participantRow: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  circle: {
    width: 40,
    height: 40,
    borderRadius: 4,
    borderWidth: 2,
    backgroundColor: colors.surface,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  circleText: {
    color: colors.text,
    fontSize: 10,
    fontWeight: "bold",
  },
  nameButton: {
    flex: 1,
    outline: 'none',
  },
  participantName: {
    color: colors.text,
    fontSize: 14,
    flex: 1,
  },
  participantStatus: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: "bold",
  },
  modalCloseButton: {
    backgroundColor: colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: "center",
    alignSelf: "center",
    marginTop: 20,
    outline: 'none',
  },
  modalCloseButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default ParticipantsStatusDashboard;
