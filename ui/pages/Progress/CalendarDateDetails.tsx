import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { 
  CalendarDayData, 
  ProgramTimelineData, 
  Program,
  DayTile,
  ParticipantList } from '@/shared/types/CommonInterface';
import { formatDateForDisplay, calculateProgramCurrentDayWithTimezone } from '@/lib/utils/calendarUtils';
import { getUserTimezone } from '@/lib/utils/timezoneUtils';
import { ContentArea } from './ContentArea';
import ParticipantsStatusDashboard from './ParticipantsStatusDashboard';
import { InfoButton } from './InfoButton';

interface CalendarDateDetailsProps {
  selectedDate: string;
  dayData: CalendarDayData;
  userId: string;
  onProgramSelect: (program: ProgramTimelineData) => void;
  onProgramAttention: (program: ProgramTimelineData) => void;
  // Data fetching props
  programsData?: { [programId: string]: Program };
  userDaysData?: { [programId: string]: DayTile[] };
  participantsData?: { [programId: string]: ParticipantList[] };
  loading?: boolean;
  submissionTriggered?: boolean;
  onSubmissionTrigger?: () => void;
}

export const CalendarDateDetails: React.FC<CalendarDateDetailsProps> = ({
  selectedDate,
  dayData,
  userId,
  onProgramSelect,
  onProgramAttention,
  programsData = {},
  userDaysData = {},
  participantsData = {},
  loading = false,
  submissionTriggered = false,
  onSubmissionTrigger,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  // Always use the first program since we only display one program at a time
  const selectedProgram = dayData.programs.length > 0 ? dayData.programs[0] : null;

  // No need for useEffect or handleProgramSelect since we only show one program

  const calculateMoneyStats = (program: ProgramTimelineData) => {
    const participants = participantsData[program.programId] || [];
    if (!participants.length) return null;

    const betAmount = program.betAmount || 0;
    const activeParticipants = participants.filter((p) => p.livesLeft >= 0);
    const eliminatedParticipants = participants.filter((p) => p.livesLeft < 0);

    return {
      activeCount: activeParticipants.length,
      activeMoney: activeParticipants.length * betAmount,
      disqualifiedCount: eliminatedParticipants.length,
      disqualifiedMoney: eliminatedParticipants.length * betAmount,
      userMoneyBack: betAmount,
      userShare:
        eliminatedParticipants.length > 0
          ? (betAmount * eliminatedParticipants.length) / activeParticipants.length
          : 0,
    };
  };

  // No program tabs needed since we only show one program at a time

  const renderProgramContent = () => {
    if (!selectedProgram) return null;

    const fullProgram = programsData[selectedProgram.programId];
    const userDays = userDaysData[selectedProgram.programId] || [];
    const participants = participantsData[selectedProgram.programId] || [];
    const userTimezone = getUserTimezone();
    const currentDay = calculateProgramCurrentDayWithTimezone(
      selectedProgram.startDate,
      userTimezone,
      (fullProgram?.status || 'ongoing') as 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified'
    );
    const selectedDay = currentDay; // For now, we'll use current day

    if (selectedProgram.needsAttention) {
      return (
        <View style={styles.attentionContainer}>
          <MaterialIcons name="warning" size={32} color={colors.error} />
          <Text style={styles.attentionTitle}>Attention Required</Text>
          <Text style={styles.attentionDescription}>
            This program needs your attention. Tap to resolve.
          </Text>
          <TouchableOpacity
            style={styles.attentionButton}
            onPress={() => onProgramAttention(selectedProgram)}
          >
            <Text style={styles.attentionButtonText}>Resolve Issue</Text>
          </TouchableOpacity>
        </View>
      );
    }

    if (!fullProgram) {
      return (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading program details...</Text>
        </View>
      );
    }

    return (
      <ScrollView 
        style={styles.programContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Program Info */}
        <View style={styles.programInfo}>
          <Text style={styles.programTitle}>{selectedProgram.programName}</Text>
          <Text style={styles.dayProgressText}>
            Day {currentDay} of {selectedProgram.totalDays || 'N/A'}
          </Text>
        </View>

        {/* Content Area */}
        {fullProgram.status === 'ongoing' && (
          <View style={styles.section}>
            <ContentArea
              userId={userId}
              selectedProgram={fullProgram}
              selectedDay={selectedDay}
              currentDay={currentDay}
              days={userDays}
              participantsList={participants}
              submissionTriggered={submissionTriggered}
              loadingStatuses={[loading]}
              submissionTrigger={onSubmissionTrigger || (() => {})}
            />
          </View>
        )}

        {/* Unified Dashboard */}
        {participants.length > 0 && (
          <View style={styles.section}>
            <ParticipantsStatusDashboard
              participants={participants}
              currentUserId={userId}
              today={true}
              moneyStats={calculateMoneyStats(selectedProgram)}
            />
          </View>
        )}

        {/* Info Button */}
        <InfoButton selectedProgramId={selectedProgram.programId} />
      </ScrollView>
    );
  };

  if (dayData.programs.length === 0) {
    return (
      <View style={styles.container}>
        <Text style={styles.noDataText}>No programs on this date</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Date Header */}
      <View style={styles.dateHeader}>
        <Text style={styles.dateTitle}>{formatDateForDisplay(selectedDate)}</Text>
        {selectedProgram && (
          <Text style={styles.programName}>
            {selectedProgram.programName}
          </Text>
        )}
      </View>

      {/* Program Content */}
      {renderProgramContent()}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  dateHeader: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    alignItems: 'center',
  },
  dateTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
  },
  programName: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.primary,
    marginTop: 4,
  },

  programContent: {
    flex: 1,
  },
  programInfo: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    alignItems: 'center',
  },
  programTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
  },
  dayProgressText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    marginTop: 6,
  },
  section: {
    marginBottom: 8,
  },
  attentionContainer: {
    backgroundColor: colors.surface,
    padding: 24,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 8,
  },
  attentionTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.error,
    marginTop: 8,
    marginBottom: 4,
  },
  attentionDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  attentionButton: {
    backgroundColor: colors.error,
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  attentionButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.background,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 12,
  },
  noDataText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
    padding: 40,
  },
});
