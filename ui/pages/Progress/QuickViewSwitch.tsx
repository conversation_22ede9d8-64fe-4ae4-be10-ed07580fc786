import React, { useRef, useEffect } from 'react';
import {
  View,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Text,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface QuickViewSwitchProps {
  useCalendarView: boolean;
  onToggle: () => void;
  style?: any;
}

export const QuickViewSwitch: React.FC<QuickViewSwitchProps> = ({
  useCalendarView,
  onToggle,
  style,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const rotateAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Rotation animation
    Animated.timing(rotateAnim, {
      toValue: useCalendarView ? 0 : 1,
      duration: 400,
      useNativeDriver: true,
    }).start();

    // Glow effect animation
    Animated.timing(glowAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      Animated.timing(glowAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }).start();
    });
  }, [useCalendarView, rotateAnim, glowAnim]);

  // Continuous pulse animation
  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.05,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, [pulseAnim]);

  const handlePress = () => {
    // Enhanced button press animation with bounce
    Animated.sequence([
      Animated.parallel([
        Animated.timing(scaleAnim, {
          toValue: 0.85,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]),
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1.1,
          tension: 300,
          friction: 4,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 300,
        friction: 6,
        useNativeDriver: true,
      }),
    ]).start();

    onToggle();
  };

  const rotation = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  const glowOpacity = glowAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 0.8],
  });

  return (
    <Animated.View
      style={[
        styles.container,
        style,
        {
          transform: [
            { scale: Animated.multiply(scaleAnim, pulseAnim) },
            { rotate: rotation },
          ],
        },
      ]}
    >
      {/* Glow Effect Background */}
      <Animated.View
        style={[
          styles.glowEffect,
          {
            opacity: glowOpacity,
            backgroundColor: colors.primary,
          },
        ]}
      />
      <TouchableOpacity
        style={styles.button}
        onPress={handlePress}
        activeOpacity={0.9}
      >
        <Animated.View
          style={{
            transform: [
              {
                scale: rotateAnim.interpolate({
                  inputRange: [0, 0.5, 1],
                  outputRange: [1, 0.8, 1],
                }),
              },
            ],
          }}
        >
          <MaterialIcons
            name={useCalendarView ? "view-list" : "calendar-today"}
            size={20} // Reduced from 26
            color={colors.background}
          />
        </Animated.View>

        {/* Ripple Effect */}
        <Animated.View
          style={[
            styles.ripple,
            {
              opacity: glowAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 0.3],
              }),
              transform: [
                {
                  scale: glowAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.5, 2],
                  }),
                },
              ],
            },
          ]}
        />
      </TouchableOpacity>
    </Animated.View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 85, // Closer to message button with reduced gap
    right: 20,
    alignItems: 'center',
    zIndex: 1000,
  },
  glowEffect: {
    position: 'absolute',
    width: 60, // Reduced from 80
    height: 60, // Reduced from 80
    borderRadius: 30, // Reduced from 40
    top: -8, // Adjusted from -12
    left: -8, // Adjusted from -12
    zIndex: -1,
  },
  button: {
    width: 44, // Reduced from 56
    height: 44, // Reduced from 56
    borderRadius: 22, // Reduced from 28
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 }, // Reduced shadow
    shadowOpacity: 0.3, // Reduced shadow opacity
    shadowRadius: 8, // Reduced shadow radius
    elevation: 8, // Reduced elevation
    position: 'relative',
    overflow: 'hidden',
  },
  ripple: {
    position: 'absolute',
    width: 44, // Reduced from 56
    height: 44, // Reduced from 56
    borderRadius: 22, // Reduced from 28
    backgroundColor: colors.background,
    top: 0,
    left: 0,
  },
});
