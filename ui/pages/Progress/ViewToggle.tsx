import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ViewToggleProps {
  useCalendarView: boolean;
  onToggle: (useCalendar: boolean) => void;
  style?: any;
}

export const ViewToggle: React.FC<ViewToggleProps> = ({
  useCalendarView,
  onToggle,
  style,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);
  
  const slideAnim = useRef(new Animated.Value(useCalendarView ? 0 : 1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    Animated.spring(slideAnim, {
      toValue: useCalendarView ? 0 : 1,
      useNativeDriver: false,
      tension: 100,
      friction: 8,
    }).start();
  }, [useCalendarView, slideAnim]);

  const handleToggle = (useCalendar: boolean) => {
    // Add haptic feedback
    const animatePress = () => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
    };

    animatePress();
    onToggle(useCalendar);
  };

  // Simplified without gesture handling for now
  // Can be enhanced later with react-native-gesture-handler if needed

  return (
    <Animated.View style={[styles.container, style, { transform: [{ scale: scaleAnim }] }]}>
      <View style={styles.toggleContainer}>
          {/* Animated Background Indicator */}
          <Animated.View
            style={[
              styles.indicator,
              {
                left: slideAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [2, '50%'] as any,
                }),
              },
            ]}
          />

          {/* Calendar Button */}
          <TouchableOpacity
            style={[styles.toggleButton, useCalendarView && styles.toggleButtonActive]}
            onPress={() => handleToggle(true)}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="calendar-today"
              size={16}
              color={useCalendarView ? colors.background : colors.textMuted}
            />
            <Text
              style={[
                styles.toggleButtonText,
                useCalendarView && styles.toggleButtonTextActive,
              ]}
            >
              Calendar
            </Text>
          </TouchableOpacity>

          {/* List Button */}
          <TouchableOpacity
            style={[styles.toggleButton, !useCalendarView && styles.toggleButtonActive]}
            onPress={() => handleToggle(false)}
            activeOpacity={0.7}
          >
            <MaterialIcons
              name="view-list"
              size={16}
              color={!useCalendarView ? colors.background : colors.textMuted}
            />
            <Text
              style={[
                styles.toggleButtonText,
                !useCalendarView && styles.toggleButtonTextActive,
              ]}
            >
              List
            </Text>
          </TouchableOpacity>
        </View>

      {/* Quick Access Hint */}
      <View style={styles.hintContainer}>
        <Text style={styles.hintText}>Tap to switch views</Text>
      </View>
    </Animated.View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    alignItems: 'center',
    marginBottom: 10,
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 3,
    position: 'relative',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    minWidth: 180,
  },
  indicator: {
    position: 'absolute',
    top: 3,
    width: '48%',
    height: '85%',
    backgroundColor: colors.primary,
    borderRadius: 10,
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.4,
    shadowRadius: 4,
    elevation: 4,
  },
  toggleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 10,
    gap: 4,
    flex: 1,
    justifyContent: 'center',
    zIndex: 2,
  },
  toggleButtonActive: {
    // Active styles handled by indicator background
  },
  toggleButtonText: {
    fontSize: 12,
    fontFamily: 'MontserratSemiBold',
    color: colors.textMuted,
  },
  toggleButtonTextActive: {
    color: colors.background,
    fontFamily: 'MontserratBold',
  },
  hintContainer: {
    marginTop: 4,
    opacity: 0.6,
  },
  hintText: {
    fontSize: 10,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    textAlign: 'center',
  },
});
