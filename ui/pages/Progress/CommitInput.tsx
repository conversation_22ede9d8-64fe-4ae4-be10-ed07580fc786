import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { VerificationInput } from '@/ui/verification/VerificationInput';
import { getCommitVerificationConfig } from '@/shared/types/verification';
import { firestoreService } from '@/lib/services/database';
import CommitSetup from './CommitSetup';
import type {
  VerificationData,
  VerificationResult
} from '@/shared/types/verification';
import { Commit } from '@/lib/services/database/types';
import { WeeklyProgressIndicator } from './WeeklyProgressIndicator';
import { getGitHubIntegrationStatus } from '@/lib/utils/integrationHelpers';
import { useFocusEffect } from 'expo-router';
import { InfoButton } from './InfoButton';

// Commit Countdown Timer Component
const CommitCountdownTimer: React.FC<{ commit: Commit; colors: any }> = ({ commit, colors }) => {
  const [timeLeft, setTimeLeft] = useState<string>("");
  const [isOverdue, setIsOverdue] = useState<boolean>(false);

  useEffect(() => {
    const updateCountdown = () => {
      const now = new Date();
      let targetTime: Date;

      // Get deadline configuration
      const deadline = commit.schedule.deadline;

      if (!deadline || deadline.type === 'midnight') {
        // Default to midnight
        targetTime = new Date();
        targetTime.setHours(24, 0, 0, 0); // Next midnight
      } else if (deadline.type === 'before' && deadline.time) {
        // Before specific time
        targetTime = new Date();
        const [hours, minutes] = deadline.time.split(':').map(Number);
        targetTime.setHours(hours, minutes, 0, 0);

        // If time has passed today, set for tomorrow
        if (targetTime <= now) {
          targetTime.setDate(targetTime.getDate() + 1);
        }
      } else if (deadline.type === 'after' && deadline.time) {
        // After specific time - show countdown until end of day if we're past the start time
        targetTime = new Date();
        const [hours, minutes] = deadline.time.split(':').map(Number);
        const startTime = new Date();
        startTime.setHours(hours, minutes, 0, 0);

        if (now >= startTime) {
          // We're in the submission window, show countdown to end of day
          targetTime.setHours(24, 0, 0, 0); // Next midnight
        } else {
          // We're before the submission window, show countdown to start time
          targetTime = startTime;
        }
      } else if (deadline.type === 'between' && deadline.endTime) {
        // Between times - show countdown until end time
        targetTime = new Date();
        const [hours, minutes] = deadline.endTime.split(':').map(Number);
        targetTime.setHours(hours, minutes, 0, 0);

        // If time has passed today, set for tomorrow
        if (targetTime <= now) {
          targetTime.setDate(targetTime.getDate() + 1);
        }
      } else {
        // Fallback to midnight
        targetTime = new Date();
        targetTime.setHours(24, 0, 0, 0);
      }

      const diff = targetTime.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeLeft("00:00:00");
        setIsOverdue(true);
        return;
      }

      setIsOverdue(false);
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      setTimeLeft(
        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );
    };

    // Update immediately
    updateCountdown();

    // Update every second
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [commit.schedule.deadline]);

  const getDeadlineText = () => {
    const deadline = commit.schedule.deadline;
    const now = new Date();

    if (!deadline || deadline.type === 'midnight') {
      return 'Time left to submit (by midnight):';
    } else if (deadline.type === 'before' && deadline.time) {
      return `Time left to submit (before ${deadline.time}):`;
    } else if (deadline.type === 'after' && deadline.time) {
      const [hours, minutes] = deadline.time.split(':').map(Number);
      const startTime = new Date();
      startTime.setHours(hours, minutes, 0, 0);

      if (now >= startTime) {
        return `Time left to submit (window open until midnight):`;
      } else {
        return `Time until submission window opens (after ${deadline.time}):`;
      }
    } else if (deadline.type === 'between' && deadline.startTime && deadline.endTime) {
      return `Time left to submit (by ${deadline.endTime}):`;
    }
    return 'Time left to submit:';
  };

  const styles = StyleSheet.create({
    countdownContainer: {
      backgroundColor: colors.surface,
      paddingHorizontal: 12,
      paddingVertical: 10,
      borderRadius: 8,
      marginTop: 8,
      alignItems: 'center',
      borderWidth: 1,
      borderColor: isOverdue ? colors.error + '30' : colors.warning + '30',
      flexDirection: 'row',
      justifyContent: 'center',
      gap: 8,
    },
    countdownText: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
    },
    countdownTime: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: isOverdue ? colors.error : colors.warning,
    },
  });

  return (
    <View style={styles.countdownContainer}>
      <Text style={styles.countdownText}>{getDeadlineText()}</Text>
      <Text style={styles.countdownTime}>{timeLeft}</Text>
    </View>
  );
};

interface CommitInputProps {
  commit: Commit;
  userId: string;
  onSubmissionSuccess: () => void;
  onCommitUpdate?: (updatedCommit: Commit) => void;
  isLoading?: boolean;
  refreshTrigger?: number;
  selectedDate?: string; // Optional selected date in YYYY-MM-DD format, defaults to today
}

const CommitInput: React.FC<CommitInputProps> = ({
  commit,
  userId,
  onSubmissionSuccess,
  onCommitUpdate,
  isLoading = false,
  refreshTrigger = 0,
  selectedDate,
}) => {
  const { colors, designSystem, isDark } = useTheme();
  const styles = createStyles(colors);

  // State for setup and verification
  const [currentCommit, setCurrentCommit] = useState<Commit>(commit);
  const [initialVerificationData, setInitialVerificationData] = useState<VerificationData | null>(null);
  const [checkingExistingSubmission, setCheckingExistingSubmission] = useState<boolean>(true);
  const [isVerificationValid, setIsVerificationValid] = useState<boolean>(false);
  const [currentDaySubmission, setCurrentDaySubmission] = useState<string>('');
  const [submissionStatus, setSubmissionStatus] = useState<'upcoming' | 'submitted' | 'verified'>('upcoming');
  const [checkingGitHubIntegration, setCheckingGitHubIntegration] = useState<boolean>(false);

  // Weekly mode specific state
  const [weeklyStatus, setWeeklyStatus] = useState<{
    submittedCount: number;
    pendingCount: number;
    canSubmit: boolean;
    canSubmitToday: boolean;
    nextSubmissionId: string | null;
    todaySubmissionExists: boolean;
  } | null>(null);

  // Update current commit when prop changes
  useEffect(() => {
    setCurrentCommit(commit);
  }, [commit]);

  // Clear verification data when commit changes to prevent data leakage between commits
  useEffect(() => {
    setInitialVerificationData(null);
    setSubmissionStatus('upcoming');
    setCheckingExistingSubmission(true);
  }, [commit.id]);

  // Helper function to check if selected date is in the future
  const isFutureDate = (): boolean => {
    const targetDateStr = selectedDate || new Date().toISOString().split('T')[0];

    // Use the same date comparison logic as the calendar
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const targetDateObj = new Date(targetDateStr + 'T00:00:00');
    targetDateObj.setHours(0, 0, 0, 0);

    return targetDateObj > today;
  };

  // Calculate current day for the commit based on selected date or today
  const getCurrentDay = (): string => {
    const startDate = new Date(commit.schedule.startDate);
    // Use selected date if provided, otherwise use today
    const targetDate = selectedDate ? new Date(selectedDate) : new Date();
    targetDate.setHours(0, 0, 0, 0);
    startDate.setHours(0, 0, 0, 0);

    if (commit.schedule.frequency === 'once') {
      return 'Day 1';
    } else if (commit.schedule.frequency === 'daily') {
      const daysDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      return `Day ${Math.max(1, daysDiff + 1)}`;
    } else if (commit.schedule.frequency === 'weekly') {
      const weeksDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 7));
      return `Week ${Math.max(1, weeksDiff + 1)}`;
    } else if (commit.schedule.frequency === 'monthly') {
      const monthsDiff = Math.floor((targetDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));
      return `Month ${Math.max(1, monthsDiff + 1)}`;
    }
    return 'Day 1';
  };



  // Handle setup completion
  const handleSetupComplete = (updatedCommit: Commit) => {
    setCurrentCommit(updatedCommit);
    onCommitUpdate?.(updatedCommit);
  };

  // Check for existing submission on mount and when dependencies change
  useEffect(() => {
    const checkExistingSubmission = async () => {
      if (!currentCommit.id || !userId) {
        setCheckingExistingSubmission(false);
        return;
      }

      setCheckingExistingSubmission(true);
      const daySubmission = getCurrentDay();
      setCurrentDaySubmission(daySubmission);

      // Check if the selected date is in the future using the helper function
      if (isFutureDate()) {
        setSubmissionStatus('submitted'); // Show as submitted to hide submission options
        setInitialVerificationData(null);
        setCheckingExistingSubmission(false);
        return;
      }

      try {
        if (currentCommit.schedule.frequency === 'weekly') {
          // Handle weekly mode with multiple submissions per week
          const timesPerWeek = currentCommit.schedule.timesPerWeek || 1;
          // Pass the target date (selected date or today) to check submissions for that specific date
          const targetDate = selectedDate || new Date().toISOString().split('T')[0];
          const weeklyStatusResult = await firestoreService.commits.getWeeklySubmissionStatus(
            currentCommit.id,
            daySubmission,
            timesPerWeek,
            targetDate
          );

          if (weeklyStatusResult.success && weeklyStatusResult.data) {
            const status = weeklyStatusResult.data;
            setWeeklyStatus(status);

            // Check submission status based on daily and weekly limits
            if (!status.canSubmit) {
              // All weekly submissions are done
              setSubmissionStatus('submitted');
            } else if (status.todaySubmissionExists) {
              // Already submitted today, but can submit more this week
              setSubmissionStatus('submitted');
            } else {
              // Can submit today
              setSubmissionStatus('upcoming');
            }

            // Set initial data only from submissions made on the target date
            const targetDate = selectedDate || new Date().toISOString().split('T')[0];
            const targetDateSubmission = status.submissions
              .filter(s => s.status === 'submitted' && s.timestamp && s.timestamp.split('T')[0] === targetDate)
              .sort((a, b) => new Date(b.timestamp || '').getTime() - new Date(a.timestamp || '').getTime())[0];

            if (targetDateSubmission && targetDateSubmission.attachment) {
              try {
                const parsedData = JSON.parse(targetDateSubmission.attachment);
                setInitialVerificationData(parsedData);
              } catch (e) {
                console.warn('Failed to parse existing submission data:', e);
              }
            } else {
              // Clear initial data if no submission exists for the target date
              setInitialVerificationData(null);
            }
          } else {
            setSubmissionStatus('upcoming');
          }
        } else {
          // Handle daily, monthly, and once modes
          const existingSubmission = await firestoreService.commits.getSubmissionDetails(
            currentCommit.id,
            currentCommit.schedule.frequency,
            daySubmission,
            'Submission 1' // Default to first submission for monthly
          );

          if (existingSubmission.success && existingSubmission.data) {
            const submissionData = existingSubmission.data;

            if (submissionData.status === 'submitted' && submissionData.attachment) {
              try {
                const verificationData = JSON.parse(submissionData.attachment);
                setInitialVerificationData(verificationData);
                setSubmissionStatus('submitted');
              } catch (error) {
                console.error('Error parsing verification data:', error);
                setInitialVerificationData(null);
                setSubmissionStatus('upcoming');
              }
            } else {
              setSubmissionStatus('upcoming');
            }
          } else {
            setSubmissionStatus('upcoming');
          }
        }
      } catch (error) {
        console.error('Error checking existing submission:', error);
      } finally {
        setCheckingExistingSubmission(false);
      }
    };

    checkExistingSubmission();
  }, [currentCommit.id, userId, refreshTrigger, selectedDate]);

  // Check for existing GitHub integration and auto-set setupStatus if needed
  useEffect(() => {
    const checkGitHubIntegration = async () => {
      // Only check for GitHub commits that have setupStatus false
      if (currentCommit.evidence?.type !== 'github' || currentCommit.setupStatus) {
        return;
      }

      setCheckingGitHubIntegration(true);

      try {
        const integrationStatus = await getGitHubIntegrationStatus();

        if (integrationStatus.isConnected) {


          // Update commit setupStatus in database
          const result = await firestoreService.commits.updateCommit(currentCommit.id!, {
            setupStatus: true
          });

          if (result.success) {
            // Update local state
            const updatedCommit = {
              ...currentCommit,
              setupStatus: true
            };
            setCurrentCommit(updatedCommit);
            onCommitUpdate?.(updatedCommit);
          } else {
            console.error('Failed to update commit setupStatus:', result.error);
          }
        }
      } catch (error) {
        console.error('Error checking GitHub integration:', error);
      } finally {
        setCheckingGitHubIntegration(false);
      }
    };

    checkGitHubIntegration();
  }, [currentCommit.id, currentCommit.setupStatus, currentCommit.evidence?.type, onCommitUpdate, refreshTrigger]);

  // Re-check GitHub integration when screen comes into focus (e.g., returning from integrations screen)
  useFocusEffect(
    React.useCallback(() => {
      const recheckGitHubIntegration = async () => {
        // Only recheck for GitHub commits that currently have setupStatus true
        if (currentCommit.evidence?.type !== 'github' || !currentCommit.setupStatus) {
          return;
        }

        try {
          const integrationStatus = await getGitHubIntegrationStatus();

          // If GitHub integration is no longer connected, reset setupStatus
          if (!integrationStatus.isConnected) {


            // Update commit setupStatus in database
            const result = await firestoreService.commits.updateCommit(currentCommit.id!, {
              setupStatus: false
            });

            if (result.success) {
              // Update local state
              const updatedCommit = {
                ...currentCommit,
                setupStatus: false
              };
              setCurrentCommit(updatedCommit);
              onCommitUpdate?.(updatedCommit);
            } else {
              console.error('Failed to reset commit setupStatus:', result.error);
            }
          }
        } catch (error) {
          console.error('Error rechecking GitHub integration:', error);
        }
      };

      recheckGitHubIntegration();
    }, [currentCommit.id, currentCommit.setupStatus, currentCommit.evidence?.type, onCommitUpdate])
  );

  // Refresh commit setupStatus from database when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const refreshCommitFromDatabase = async () => {
        if (!currentCommit.id) {
          return;
        }

        try {
          // Fetch the latest commit data from database
          const result = await firestoreService.commits.getCommitById(currentCommit.id);

          if (result.success && result.data) {
            const latestCommit = result.data;

            // Check if setupStatus has changed
            if (latestCommit.setupStatus !== currentCommit.setupStatus) {
              console.log(`Commit setupStatus updated from database: ${currentCommit.setupStatus} -> ${latestCommit.setupStatus}`);

              // Update local state with the latest data
              setCurrentCommit(latestCommit);
              onCommitUpdate?.(latestCommit);
            }
          }
        } catch (error) {
          console.error('Error refreshing commit from database:', error);
        }
      };

      refreshCommitFromDatabase();
    }, [currentCommit.id, currentCommit.setupStatus, onCommitUpdate])
  );

  // Handle verification submission
  const handleVerificationSubmit = async (data: VerificationData): Promise<VerificationResult> => {
    if (!currentCommit.evidence?.type) {
      return { success: false, error: "Evidence type is required" };
    }

    try {
      const serializedData = JSON.stringify(data);
      let submissionId = 'Submission 1'; // Default for non-weekly modes

      // For weekly mode, use the next available submission slot
      if (currentCommit.schedule.frequency === 'weekly' && weeklyStatus?.nextSubmissionId) {
        // Double-check that user can submit today
        if (!weeklyStatus.canSubmitToday) {
          return {
            success: false,
            error: weeklyStatus.todaySubmissionExists
              ? "You have already submitted once today. You can submit again tomorrow."
              : "You have completed all submissions for this week."
          };
        }
        submissionId = weeklyStatus.nextSubmissionId;
      }

      // Update commit submission using the helper method
      const result = await firestoreService.commits.updateSubmissionDetails(
        currentCommit.id!,
        currentCommit.schedule.frequency,
        currentDaySubmission,
        {
          attachment: serializedData,
          status: 'submitted',
          timestamp: new Date().toISOString()
        },
        submissionId
      );

      if (!result.success) {
        return { success: false, error: result.error || "Failed to update submission" };
      }

      // Update UI state
      if (currentCommit.schedule.frequency === 'weekly') {
        // Refresh weekly status after submission
        const timesPerWeek = currentCommit.schedule.timesPerWeek || 1;
        // Pass the target date (selected date or today) to check submissions for that specific date
        const targetDate = selectedDate || new Date().toISOString().split('T')[0];
        const weeklyStatusResult = await firestoreService.commits.getWeeklySubmissionStatus(
          currentCommit.id!,
          currentDaySubmission,
          timesPerWeek,
          targetDate
        );

        if (weeklyStatusResult.success && weeklyStatusResult.data) {
          const updatedStatus = weeklyStatusResult.data;
          setWeeklyStatus(updatedStatus);

          // Update submission status based on daily and weekly limits
          if (!updatedStatus.canSubmit) {
            setSubmissionStatus('submitted'); // All weekly submissions are done
            setInitialVerificationData(data); // Keep the data for preview when all done
          } else if (updatedStatus.todaySubmissionExists) {
            setSubmissionStatus('submitted'); // Already submitted today
            setInitialVerificationData(null); // Clear data since they can submit again tomorrow
          } else {
            setSubmissionStatus('upcoming'); // Can still submit today
            setInitialVerificationData(null); // Clear data so user can submit again
          }
        }
      } else {
        setSubmissionStatus('submitted');
        setInitialVerificationData(data);
      }
      onSubmissionSuccess();

      return { success: true, data };
    } catch (error) {
      console.error("Error during verification submission:", error);
      return { success: false, error: "An error occurred during submission" };
    }
  };

  // Get verification config based on commit evidence type
  const getVerificationConfig = () => {
    const evidenceType = currentCommit.evidence?.type || 'photo';
    return getCommitVerificationConfig(evidenceType);
  };

  const getSubmissionTitle = () => {
    // For weekly mode, show contextual titles based on status
    if (currentCommit.schedule.frequency === 'weekly' && weeklyStatus) {
      if (!weeklyStatus.canSubmit) {
        return 'Weekly Challenge Complete! 🏆';
      } else if (weeklyStatus.todaySubmissionExists) {
        return 'Submission Recorded';
      } else if (weeklyStatus.canSubmitToday) {
        return 'Ready to Submit';
      } else {
        return 'Submission Status';
      }
    }

    // For other modes, show evidence-specific titles
    const evidenceType = currentCommit.evidence?.type || 'photo';

    // Handle all evidence types with a more flexible approach using string matching
    const evidenceTypeStr = evidenceType as string;

    if (evidenceTypeStr === 'photo') {
      return 'Submit your Photo Evidence';
    } else if (evidenceTypeStr === 'video' || evidenceTypeStr.includes('video')) {
      return 'Submit your Video Evidence';
    } else if (evidenceTypeStr.includes('camera')) {
      return 'Submit your Camera Evidence';
    } else if (evidenceTypeStr === 'gps-checkin') {
      return 'Check in at Location';
    } else if (evidenceTypeStr === 'gps-avoid') {
      return 'Confirm Location Avoidance';
    } else if (evidenceTypeStr === 'strava') {
      return 'Submit your Strava Activity';
    } else if (evidenceTypeStr === 'github') {
      return 'GitHub Commit Verification';
    } else if (evidenceTypeStr === 'screen-time') {
      return 'Submit your Screen Time Data';
    } else if (evidenceTypeStr === 'honor') {
      return 'Submit your Honor System Evidence';
    } else {
      return 'Submit your Evidence';
    }
  };



  // Helper function to get ordinal numbers (1st, 2nd, 3rd, 4th, etc.)
  const getOrdinalNumber = (num: number): string => {
    const suffix = ['th', 'st', 'nd', 'rd'];
    const value = num % 100;
    return num + (suffix[(value - 20) % 10] || suffix[value] || suffix[0]);
  };

  // Render submitted status UI
  const renderSubmittedStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="check-circle" size={24} color={colors.primary} />
        <Text style={styles.statusHeaderTitle}>Great job! 🎉</Text>
      </View>
      <Text style={styles.statusSubtitle}>
        {currentCommit.schedule.frequency === 'weekly' && weeklyStatus
          ? !weeklyStatus.canSubmit
            ? `Perfect! All submissions completed this week 🏆`
            : `${getOrdinalNumber(weeklyStatus.submittedCount)} submission recorded! 🎯`
          : `Submission completed successfully`
        }
      </Text>
      <Text style={styles.statusDescription}>
        Your evidence has been submitted successfully. The Accustom Team will review your progress.
      </Text>

      {/* Show submitted evidence preview */}
      {initialVerificationData && (
        <View style={styles.evidencePreview}>
          <Text style={styles.evidencePreviewTitle}>Submitted Evidence:</Text>
          {initialVerificationData.images && initialVerificationData.images.length > 0 && (
            <Text style={styles.evidencePreviewText}>
              📸 {initialVerificationData.images.length} photo{initialVerificationData.images.length > 1 ? 's' : ''} submitted
            </Text>
          )}
          {initialVerificationData.location && (
            <Text style={styles.evidencePreviewText}>
              📍 Location verified
            </Text>
          )}
          {initialVerificationData.textValue && (
            <Text style={styles.evidencePreviewText}>
              📝 Text submission: {initialVerificationData.textValue.substring(0, 50)}...
            </Text>
          )}
        </View>
      )}
    </View>
  );

  // Render verified status UI
  const renderVerifiedStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="verified" size={24} color={colors.success || '#4CAF50'} />
        <Text style={[styles.statusHeaderTitle, { color: colors.success || '#4CAF50' }]}>
          Verified! ✅
        </Text>
      </View>
      <Text style={styles.statusSubtitle}>
        Submission verified for {currentDaySubmission}
      </Text>
      <Text style={styles.statusDescription}>
        Your commitment has been reviewed and verified by the Accustom Team. Excellent work!
      </Text>
    </View>
  );

  // Render future date status UI
  const renderFutureDateStatus = () => (
    <View style={styles.statusContainer}>
      <View style={styles.statusHeader}>
        <MaterialIcons name="schedule" size={24} color={colors.textMuted} />
        <Text style={[styles.statusHeaderTitle, { color: colors.textMuted }]}>
          Future Date
        </Text>
      </View>
      <Text style={styles.statusSubtitle}>
        This date hasn't arrived yet
      </Text>
      <Text style={styles.statusDescription}>
        You can only submit evidence for today or past dates. Come back on this date to submit your evidence.
      </Text>
    </View>
  );

  // Show loading if checking GitHub integration
  if (checkingGitHubIntegration) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color={colors.primary} />
        <Text style={styles.loadingText}>Checking GitHub integration...</Text>
      </View>
    );
  }

  // Show setup if setupStatus is false
  if (!currentCommit.setupStatus) {
    return (
      <CommitSetup
        commit={currentCommit}
        onSetupComplete={handleSetupComplete}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Commit Info - Always at top */}
      <View style={styles.commitInfo}>
        <View style={styles.commitInfoHeader}>
          <View style={styles.commitInfoText}>
            <Text style={styles.commitTitle}>{currentCommit.title}</Text>
            <Text style={styles.commitFrequency}>
              {currentCommit.schedule.frequency.charAt(0).toUpperCase() + currentCommit.schedule.frequency.slice(1)} • {currentCommit.evidence?.type?.charAt(0).toUpperCase() + currentCommit.evidence?.type?.slice(1) || 'Photo'}
            </Text>
          </View>
          <InfoButton selectedCommitId={currentCommit.id}/>
        </View>
      </View>

      {/* Weekly Progress Indicator - Only for weekly mode */}
      {currentCommit.schedule.frequency === 'weekly' && weeklyStatus && (
        <WeeklyProgressIndicator
          submittedCount={weeklyStatus.submittedCount}
          totalRequired={weeklyStatus.submittedCount + weeklyStatus.pendingCount}
          todaySubmissionExists={weeklyStatus.todaySubmissionExists}
          canSubmitToday={weeklyStatus.canSubmitToday}
          style={styles.weeklyProgress}
        />
      )}

      {/* Status Title */}
      <View style={styles.statusTitleContainer}>
        <Text style={styles.statusTitle}>
          {getSubmissionTitle()}
        </Text>
      </View>

      {/* Countdown Timer - only show for ongoing unsubmitted commits */}
      {!checkingExistingSubmission && submissionStatus === 'upcoming' &&
       !(currentCommit.schedule.frequency === 'weekly' && weeklyStatus && !weeklyStatus.canSubmitToday) && (
        <CommitCountdownTimer commit={currentCommit} colors={colors} />
      )}

      {checkingExistingSubmission ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      ) : isFutureDate() ? (
        renderFutureDateStatus()
      ) : submissionStatus === 'verified' ? (
        renderVerifiedStatus()
      ) : submissionStatus === 'submitted' ? (
        // For weekly mode, show submitted status if user cannot submit today or has completed all submissions
        // For other modes, always show submitted status
        currentCommit.schedule.frequency === 'weekly'
          ? (weeklyStatus && (!weeklyStatus.canSubmitToday || !weeklyStatus.canSubmit) ? renderSubmittedStatus() : (
              <VerificationInput
                config={getVerificationConfig()}
                programId={currentCommit.id || ''}
                userId={userId}
                day={parseInt(currentDaySubmission.split(' ')[1]) || 1}
                onSubmit={handleVerificationSubmit}
                onValidationChange={setIsVerificationValid}
                initialData={initialVerificationData}
                disabled={isLoading}
              />
            ))
          : renderSubmittedStatus()
      ) : (
        <VerificationInput
          config={getVerificationConfig()}
          programId={currentCommit.id || ''}
          userId={userId}
          day={parseInt(currentDaySubmission.split(' ')[1]) || 1}
          onSubmit={handleVerificationSubmit}
          onValidationChange={setIsVerificationValid}
          initialData={initialVerificationData}
          disabled={isLoading || (currentCommit.schedule.frequency === 'weekly' && weeklyStatus && !weeklyStatus.canSubmitToday) || false}
        />
      )}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.surface,
    borderRadius: 12,
    padding: 16,
    margin: 16,
    borderWidth: 1,
    borderColor: colors.border || colors.textMuted + '20',
    borderStyle: 'dashed',
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    flex: 1,
  },
  dayIndicator: {
    fontSize: 14,
    fontFamily: 'MontserratSemiBold',
    color: colors.primary,
    backgroundColor: colors.primary + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  weeklyProgress: {
    marginVertical: 14,
    paddingHorizontal: 4,
  },
  commitInfo: {
    marginBottom: 8,
    paddingBottom: 14,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  commitInfoHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  commitInfoText: {
    flex: 1,
    paddingRight: 8,
  },
  statusTitleContainer: {
    marginBottom: 14,
    alignItems: 'center',
  },
  statusTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 4,
    letterSpacing: 0.3,
    lineHeight: 24,
  },
  commitTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 5,
    textAlign: 'center',
  },
  commitFrequency: {
    fontSize: 13,
    fontFamily: 'MontserratMedium',
    color: colors.textSecondary,
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 8,
  },
  statusContainer: {
    backgroundColor: colors.background,
    borderRadius: 12,
    padding: 18,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusHeaderTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginLeft: 8,
  },
  statusSubtitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  statusDescription: {
    fontSize: 15,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  evidencePreview: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    width: '100%',
  },
  evidencePreviewTitle: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: colors.text,
    marginBottom: 8,
  },
  evidencePreviewText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    color: colors.textSecondary,
    marginBottom: 4,
  },
});

export default CommitInput;
