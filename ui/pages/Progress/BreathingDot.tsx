import React, { useRef, useEffect } from "react";
import { Animated, Easing, ViewStyle } from "react-native";
import { useTheme } from "@/shared/contexts/ThemeContext";

interface BreathingDotProps {
  style?: ViewStyle;
}

const BreathingDot: React.FC<BreathingDotProps> = ({ style }) => {
  const { colors } = useTheme();
  const scale = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const pulse = Animated.loop(
      Animated.sequence([
        Animated.timing(scale, {
          toValue: 1.5,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: 1,
          duration: 1000,
          easing: Easing.inOut(Easing.ease),
          useNativeDriver: true,
        }),
      ])
    );
    pulse.start();
    return () => pulse.stop();
  }, [scale]);

  return (
    <Animated.View
      style={[
        {
          width: 8,
          height: 8,
          borderRadius: 4,
          backgroundColor: colors.primary,
          transform: [{ scale }],
        },
        style // Merge the custom style with the default styles
      ]}
    />
  );
};

export default BreathingDot;


