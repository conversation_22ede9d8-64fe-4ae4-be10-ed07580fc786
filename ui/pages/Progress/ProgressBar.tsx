import React, { useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
} from "react-native";
import { useTheme } from "@/shared/contexts/ThemeContext";

interface DayTile {
  day: number;
  date: string;
  status: string;
}

interface ProgressBarProps {
  days: DayTile[];
  currentDay: number;
  remainingDays: number;
  selectedDay: number;
  onDaySelect: (day: number) => void;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  days,
  currentDay,
  remainingDays,
  selectedDay,
  onDaySelect,
}) => {
  const { colors, designSystem } = useTheme();
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    setTimeout(() => {
      // Calculate the maximum scroll position (rightmost)
      const tileWidth = 70;
      const tileMargin = 8; // 4 on each side
      const remainingTileWidth = 100;
      const totalContentWidth = (days.length * (tileWidth + tileMargin)) + remainingTileWidth + tileMargin;

      // Get the FlatList's width to calculate max scroll position
      flatListRef.current?.scrollToEnd({
        animated: false,
      });
    }, 200);
  }, [days.length]); // Changed dependency to days.length since we want to scroll to right regardless of currentDay

  const getStatusEmoji = (status: string) => {
    switch (status) {
      case "verified":
        return "✅";
      case "submitted":
        return "⏰";
      case "upcoming":
        return "❌"; // or consider a different emoji for "Not Submitted Yet"
      case "bailed":
        return "🔨";
      default:
        console.warn(`Unknown status: ${status}`);
        return "❓";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "upcoming":
        return "Not Submitted Yet";
      // ... other cases
    }
  };

  // Create data array that includes both day tiles and the remaining days tile
  const listData = [
    ...days.map(dayTile => ({ type: 'day', data: dayTile })),
    { type: 'remaining', data: { remainingDays } }
  ];

  const renderItem = ({ item }: { item: any }) => {
    if (item.type === 'day') {
      const dayTile = item.data;
      return (
        <TouchableOpacity
          style={[
            styles.tile,
            dayTile.day === selectedDay && styles.currentTile,
          ]}
          onPress={() => onDaySelect(dayTile.day)}
        >
          <Text style={styles.dayText}>Day {dayTile.day}</Text>
          <Text style={styles.dateText}>{dayTile.date}</Text>
          <Text style={styles.statusEmoji}>
            {getStatusEmoji(dayTile.status)}
          </Text>
        </TouchableOpacity>
      );
    } else {
      // Remaining days tile
      return (
        <View style={styles.remainingTile}>
          <Text style={styles.remainingText}>
            {item.data.remainingDays} days remaining
          </Text>
        </View>
      );
    }
  };

  const styles = createStyles(colors, designSystem);

  return (
    <View style={styles.container}>
      <FlatList
        ref={flatListRef}
        data={listData}
        renderItem={renderItem}
        keyExtractor={(item, index) =>
          item.type === 'day' ? `day-${item.data.day}` : `remaining-${index}`
        }
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
      />
    </View>
  );
};

const createStyles = (colors: any, designSystem: any) => StyleSheet.create({
  container: {
    marginVertical: designSystem.spacing.xs,
    marginHorizontal: designSystem.spacing.sm + 2,
  },
  scrollViewContent: {
    alignItems: "center",
    paddingHorizontal: designSystem.spacing.xs,
  },
  tile: {
    width: 70,
    height: 80,
    marginHorizontal: designSystem.spacing.xs,
    borderRadius: designSystem.borderRadius.sm,
    backgroundColor: colors.card,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1.5,
    borderColor: colors.border,
  },
  currentTile: {
    borderColor: colors.primary,
    backgroundColor: colors.surface,
  },
  dayText: {
    fontSize: designSystem.typography.fontSize.sm,
    color: colors.primary,
    fontFamily: "MontserratBold",
  },
  dateText: {
    fontSize: designSystem.typography.fontSize.xs,
    color: colors.text,
    marginVertical: designSystem.spacing.xs / 2,
    fontFamily: "MontserratRegular",
  },
  statusEmoji: {
    fontSize: 16, // Reduced emoji size
  },
  remainingTile: {
    width: 100, // Adjusted width
    height: 80, // Adjusted height
    marginHorizontal: 4, // Reduced horizontal spacing
    borderRadius: 8, // Adjusted border radius
    backgroundColor: colors.surface,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 1.5, // Adjusted border width
    borderColor: colors.border,
  },
  remainingText: {
    fontSize: 12, // Reduced font size
    color: colors.warning,
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
});

export default ProgressBar;


