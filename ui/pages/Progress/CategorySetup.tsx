import React, { useState, useEffect } from "react";
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useCameraPermissions } from "expo-camera";
import * as ImagePicker from "expo-image-picker";
import * as Location from "expo-location";
import { getId } from "@/lib/utils/variables";
import { firestoreService } from "../../../lib/services/database";
import { Program } from "@/shared/types/CommonInterface";
import { githubService } from "@/lib/services/githubService";
import { useTheme } from "@/shared/contexts/ThemeContext";

interface CategorySetupProps {
  category: string | undefined;
  selectedProgram: any;
  updateSetupStatus: (updatedSetupStatus: boolean) => void;
  updateSelectedProgram: (updatedSelectedProgram: Program) => void;
  updateSignedUpProgram: (updateSignedUpProgram: any) => void;
}

const CategorySetup: React.FC<CategorySetupProps> = ({
  category,
  selectedProgram,
  updateSetupStatus,
  updateSelectedProgram,
  updateSignedUpProgram,
}) => {
  const { colors, designSystem, isDark } = useTheme();
  const [inputValue, setInputValue] = useState<string>("");
  const [isConnecting, setIsConnecting] = useState(false);
  const [permission, requestPermission] = useCameraPermissions();
  const [locationPermission, setLocationPermission] = useState<Location.PermissionResponse | null>(null);
  const [galleryPermission, setGalleryPermission] = useState<ImagePicker.PermissionResponse | null>(null);

  // Check location permission status on mount for gym category
  useEffect(() => {
    const checkLocationPermission = async () => {
      if (category === "gym") {
        try {
          const permissionResponse = await Location.getForegroundPermissionsAsync();
          setLocationPermission(permissionResponse);
        } catch (error) {
          console.error("Error checking location permission:", error);
        }
      }
    };

    checkLocationPermission();
  }, [category]);

  // Check gallery permission status on mount for photo categories
  useEffect(() => {
    const checkGalleryPermission = async () => {
      if (category && ['photo'].includes(category)) {
        try {
          const permissionResponse = await ImagePicker.getMediaLibraryPermissionsAsync();
          setGalleryPermission(permissionResponse);
        } catch (error) {
          console.error("Error checking gallery permission:", error);
        }
      }
    };

    checkGalleryPermission();
  }, [category]);

  const requestLocationPermission = async () => {
    try {
      const permissionResponse = await Location.requestForegroundPermissionsAsync();
      setLocationPermission(permissionResponse);
      return permissionResponse;
    } catch (error) {
      console.error("Error requesting location permission:", error);
      return null;
    }
  };

  const requestGalleryPermission = async () => {
    try {
      const permissionResponse = await ImagePicker.requestMediaLibraryPermissionsAsync();
      setGalleryPermission(permissionResponse);
      return permissionResponse;
    } catch (error) {
      console.error("Error requesting gallery permission:", error);
      return null;
    }
  };

  const handleConnect = async () => {
    let setupValue = "";
    setIsConnecting(true); // Start loading

    if (category === "cardio") {
      if (!inputValue.trim()) {
        Alert.alert(
          "Input Required",
          "Please enter the required Strava profile link."
        );
        setIsConnecting(false);
        return;
      }
      setupValue = inputValue;
    } else if (category === "coding") {
      // Handle GitHub OAuth
      try {
        await githubService.initialize();
        const authResult = await githubService.authenticate();

        if (authResult.success) {
          const userData = await githubService.getUserData();
          setupValue = `GitHub Connected: ${userData?.login || 'Unknown'}`;
        } else {
          Alert.alert(
            "GitHub Authentication Failed",
            authResult.error || "Failed to connect to GitHub"
          );
          setIsConnecting(false);
          return;
        }
      } catch (error) {
        Alert.alert(
          "GitHub Connection Error",
          "Failed to connect to GitHub. Please try again."
        );
        setIsConnecting(false);
        return;
      }
    } else if (category === "affirmations" || category === "journaling" || category === "writing") {
      if (!permission?.granted) {
        Alert.alert("Permission Required", "Please grant camera access first.");
        setIsConnecting(false);
        return;
      }
      setupValue = "Camera Access Granted";
    } else if (category === "photo") {
      if (!permission?.granted || !galleryPermission?.granted) {
        Alert.alert("Permission Required", "Please grant camera and gallery access first.");
        setIsConnecting(false);
        return;
      }
      setupValue = "Camera and Gallery Access Granted";
    } else if (category === "gym") {
      if (!permission?.granted) {
        Alert.alert("Permission Required", "Please grant camera access first.");
        setIsConnecting(false);
        return;
      }
      if (!locationPermission?.granted) {
        Alert.alert("Permission Required", "Please grant location access first.");
        setIsConnecting(false);
        return;
      }
      setupValue = "Camera and Location Access Granted";
    } else {
      Alert.alert("Error", "Invalid or unknown category.");
      setIsConnecting(false);
      return;
    }

    const firebaseUpdate = async (setupValue: string) => {
      try {
        const userId = await getId();
        if (!userId || !selectedProgram?.id) return;

        // Use centralized service for setup completion
        const result = await firestoreService.completeUserSetup(
          userId,
          selectedProgram.id,
          setupValue,
          selectedProgram.category || 'program'
        );

        if (result.success) {
          Alert.alert("Success", "Your account has been successfully connected!");
          updateSetupStatus(true);
        } else {
          throw new Error(result.error || 'Setup failed');
        }
        // setSetupStatus(true);
        if (selectedProgram) {
          updateSelectedProgram({
            ...selectedProgram,
            setupStatus: true,
          });
          // setSelectedProgram({
          //   ...selectedProgram,
          //   setupStatus: true,
          // });
        }
        updateSignedUpProgram((prevPrograms: any) =>
          prevPrograms.map((prog: any) =>
            prog.id === selectedProgram?.id
              ? { ...prog, setupStatus: true }
              : prog
          ))
        // setSignedUpPrograms((prevPrograms) =>
        //   prevPrograms.map((prog) =>
        //     prog.id === selectedProgram?.id
        //       ? { ...prog, setupStatus: updatedSetupStatus }
        //       : prog
        //   )
        // );
      } catch (error) {
        console.error("Error connecting account:", error);
        Alert.alert(
          "Error",
          "Failed to connect your account. Please try again."
        );
      }
    };

    await firebaseUpdate(setupValue);
    setIsConnecting(false); // Stop loading
  };

  if (!category) return null;

  // For cardio category that requires Strava profile link
  if (category === "cardio") {
    const dynamicStyles = createStyles(colors, designSystem, isDark);
    return (
      <View style={dynamicStyles.cardContainer}>
        <Text style={dynamicStyles.cardTitle}>Connect Your Strava Profile</Text>
        <Text style={dynamicStyles.cardDescription}>
          To track your progress seamlessly, please paste your Strava profile URL below.
        </Text>
        <TextInput
          style={dynamicStyles.inputField}
          placeholder="Enter your Strava URL"
          placeholderTextColor={colors.textMuted}
          value={inputValue}
          onChangeText={setInputValue}
        />
        <TouchableOpacity
          style={[dynamicStyles.connectButton, isConnecting && dynamicStyles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.connectButtonText}>Connect Now</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // For coding category that requires GitHub OAuth
  if (category === "coding") {
    const dynamicStyles = createStyles(colors, designSystem, isDark);
    return (
      <View style={dynamicStyles.cardContainer}>
        <Text style={dynamicStyles.cardTitle}>Connect Your GitHub Account</Text>
        <Text style={dynamicStyles.cardDescription}>
          To verify your coding commits, please connect your GitHub account using OAuth.
        </Text>
        <TouchableOpacity
          style={[dynamicStyles.connectButton, isConnecting && dynamicStyles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.connectButtonText}>Connect GitHub</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // For categories that require camera access (affirmations, journaling, or writing)
  if (category === "affirmations" || category === "journaling" || category === "writing") {
    const dynamicStyles = createStyles(colors, designSystem, isDark);
    return (
      <View style={dynamicStyles.cardContainer}>
        <Text style={dynamicStyles.cardTitle}>
          {category === "affirmations"
            ? "Enable Camera for Affirmations"
            : category === "journaling"
            ? "Enable Camera for Journaling"
            : "Enable Camera for Writing"}
        </Text>
        <Text style={dynamicStyles.cardDescription}>
          To capture your daily creative moments, please enable camera access.
        </Text>
        <TouchableOpacity
          style={[
            dynamicStyles.cameraAccessButton,
            permission?.granted && { backgroundColor: colors.success },
            isConnecting && dynamicStyles.disabledButton,
          ]}
          onPress={async () => {
            if (!permission?.granted) {
              await requestPermission();
            } else {
              Alert.alert(
                "Permission Granted",
                "Camera access already enabled!"
              );
            }
          }}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.cameraAccessButtonText}>
              Enable Camera Access {permission?.granted && "✓"}
            </Text>
          )}
        </TouchableOpacity>
        <TouchableOpacity
          style={[dynamicStyles.connectButton, isConnecting && dynamicStyles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.connectButtonText}>Connect Now</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // For photo category that requires both camera and gallery access
  if (category === "photo") {
    const dynamicStyles = createStyles(colors, designSystem, isDark);
    return (
      <View style={dynamicStyles.cardContainer}>
        <Text style={dynamicStyles.cardTitle}>Enable Camera & Gallery for Photos</Text>
        <Text style={dynamicStyles.cardDescription}>
          To capture or select photos for your submissions, please enable camera and gallery access.
        </Text>
        <Text style={dynamicStyles.privacyNote}>
          These permissions are only active during submission time.
        </Text>

        <TouchableOpacity
          style={[
            dynamicStyles.cameraAccessButton,
            permission?.granted && { backgroundColor: colors.success },
            isConnecting && dynamicStyles.disabledButton,
          ]}
          onPress={async () => {
            if (!permission?.granted) {
              await requestPermission();
            } else {
              Alert.alert(
                "Permission Granted",
                "Camera access already enabled!"
              );
            }
          }}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.cameraAccessButtonText}>
              Enable Camera Access {permission?.granted && "✓"}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            dynamicStyles.locationAccessButton,
            galleryPermission?.granted && { backgroundColor: colors.success },
            isConnecting && dynamicStyles.disabledButton,
          ]}
          onPress={async () => {
            if (!galleryPermission?.granted) {
              await requestGalleryPermission();
            } else {
              Alert.alert(
                "Permission Granted",
                "Gallery access already enabled!"
              );
            }
          }}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.locationAccessButtonText}>
              Enable Gallery Access {galleryPermission?.granted && "✓"}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[dynamicStyles.connectButton, isConnecting && dynamicStyles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.connectButtonText}>Continue</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  // For gym category that requires both camera and location access
  if (category === "gym") {
    const dynamicStyles = createStyles(colors, designSystem, isDark);
    return (
      <View style={dynamicStyles.cardContainer}>
        <Text style={dynamicStyles.cardTitle}>Enable Camera & Location for Gym</Text>
        <Text style={dynamicStyles.cardDescription}>
          To verify your gym workouts, please enable camera and location access.
        </Text>
        <Text style={dynamicStyles.privacyNote}>
          These permissions are only active during submission time.
        </Text>

        <TouchableOpacity
          style={[
            dynamicStyles.cameraAccessButton,
            permission?.granted && { backgroundColor: colors.success },
            isConnecting && dynamicStyles.disabledButton,
          ]}
          onPress={async () => {
            if (!permission?.granted) {
              await requestPermission();
            } else {
              Alert.alert(
                "Permission Granted",
                "Camera access already enabled!"
              );
            }
          }}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.cameraAccessButtonText}>
              Enable Camera Access {permission?.granted && "✓"}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            dynamicStyles.locationAccessButton,
            locationPermission?.granted && { backgroundColor: colors.success },
            isConnecting && dynamicStyles.disabledButton,
          ]}
          onPress={async () => {
            if (!locationPermission?.granted) {
              await requestLocationPermission();
            } else {
              Alert.alert(
                "Permission Granted",
                "Location access already enabled!"
              );
            }
          }}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.locationAccessButtonText}>
              Enable Location Access {locationPermission?.granted && "✓"}
            </Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[dynamicStyles.connectButton, isConnecting && dynamicStyles.disabledButton]}
          onPress={handleConnect}
          disabled={isConnecting}
        >
          {isConnecting ? (
            <ActivityIndicator size="small" color={colors.text} />
          ) : (
            <Text style={dynamicStyles.connectButtonText}>Connect Now</Text>
          )}
        </TouchableOpacity>
      </View>
    );
  }

  const dynamicStyles = createStyles(colors, designSystem, isDark);
  return (
    <Text style={dynamicStyles.errorText}>
      Program category is missing or unknown.
    </Text>
  );
};

const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.card,
    padding: 20,
    borderRadius: 16,
    alignItems: "center",
    marginVertical: 20,
    width: "100%",
    borderWidth: 1,
    borderColor: colors.border,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  cardTitle: {
    fontSize: 20,
    color: colors.text,
    fontFamily: 'MontserratBold',
    marginBottom: 10,
  },
  cardDescription: {
    fontSize: 14,
    color: colors.textSecondary,
    fontFamily: 'MontserratRegular',
    textAlign: "center",
    marginBottom: 20,
  },
  inputField: {
    backgroundColor: colors.surface,
    color: colors.text,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 16,
    width: "100%",
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    borderWidth: 1,
    borderColor: colors.border,
  },
  connectButton: {
    backgroundColor: colors.primary,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 12,
    width: "80%",
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  connectButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  cameraAccessButton: {
    backgroundColor: colors.primary,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 12,
    width: "80%",
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  cameraAccessButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  locationAccessButton: {
    backgroundColor: colors.primary,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginTop: 12,
    width: "80%",
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  locationAccessButtonText: {
    color: colors.background,
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  privacyNote: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: 'MontserratRegular',
    textAlign: "center",
    marginBottom: 10,
    fontStyle: "italic",
  },
  errorText: {
    color: colors.error,
    fontSize: 14,
    fontFamily: 'MontserratMedium',
    textAlign: "center",
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.7,
  },
});

export default CategorySetup;
