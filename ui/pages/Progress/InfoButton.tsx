import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { TouchableOpacity } from "react-native";
import { useTheme } from "@/shared/contexts/ThemeContext";

interface InfoButtonProps {
  selectedProgramId?: string;
  selectedCommitId?: string;
}

export function InfoButton ({ selectedProgramId, selectedCommitId }: InfoButtonProps) {
  const { colors } = useTheme();

  const handlePress = () => {
    if (selectedProgramId) {
      router.push(`/ProgramDetails?programId=${selectedProgramId}`);
    } else if (selectedCommitId) {
      router.push(`/CommitDetails?commitId=${selectedCommitId}`);
    }
  };

  // Only render if we have either a program or commit ID
  if (!selectedProgramId && !selectedCommitId) {
    return null;
  }

  return (
    <TouchableOpacity
      style={{
        padding: 4,
        minWidth: 28,
        minHeight: 28,
        alignItems: 'center',
        justifyContent: 'center',
      }}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <MaterialIcons name="info-outline" size={20} color={colors.primary} />
    </TouchableOpacity>
  );
};