import React, { useState, useCallback, useMemo } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { useTheme } from "@/shared/contexts/ThemeContext";

interface FAQSectionProps {
  category: string;
  betAmount: number;
  startDate: string;
  endDate: string;
}

interface Step {
  title: string;
  description: string;
  iconName: string;
}

const FAQSection: React.FC<FAQSectionProps> = React.memo(({
  category,
  betAmount,
  startDate,
  endDate,
}) => {
  const { colors, isDark, designSystem } = useTheme();
  const styles = createStyles(colors, isDark, designSystem);
  // Memoize steps calculation
  const steps = useMemo<Step[]>(() => {
    const baseSteps: Step[] = [
      {
        title: "Pledge it!",
        description: "Add your money to the pool.",
        iconName: "currency-usd",
      },
    ];
    const lowerCategory = category.toLowerCase();
    if (lowerCategory === "cardio") {
      baseSteps.push(
        {
          title: "Connect Account",
          description: "Strava",
          iconName: "account",
        },
        {
          title: "Make Progress",
          description: "Upload your Strava activity everyday",
          iconName: "chart-line",
        }
      );
    } else if (lowerCategory === "coding") {
      baseSteps.push(
        {
          title: "Connect Account",
          description: "GitHub",
          iconName: "code-tags",
        },
        {
          title: "Make Progress",
          description: "Make at least 2 commits everyday",
          iconName: "source-branch",
        }
      );
    } else if (lowerCategory === "gym") {
      baseSteps.push(
        {
          title: "Give Permission",
          description: "Camera, GPS access required",
          iconName: "camera",
        },
        {
          title: "Make Progress",
          description: "Upload a picture from the gym",
          iconName: "image",
        }
      );
    } else {
      baseSteps.push(
        {
          title: "Connect Account",
          description: "Connect your account",
          iconName: "account",
        },
        {
          title: "Make Progress",
          description: "Follow your daily routine",
          iconName: "check",
        }
      );
    }
    baseSteps.push({
      title: "Claim your Money",
      description: "Your money back + your share of the pot.",
      iconName: "cash",
    });
    return baseSteps;
  }, [category]);

  // Memoize FAQ questions calculation
  const faqQuestions = useMemo(() => [
    {
      question: "Why do I need to pay money?",
      answer: "Put your money on the line to stay motivated — finish and get it back. If others drop out, you profit!"
    },
    {
      question: "How do I get my money back?",
      answer: "If you make it to the end of the challenge by staying consistent, you will get your money along with your share of the losers pot.",
    },
    {
      question: "Can I take days off?",
      answer: "You can use your lives to skip a day which is provided to you for free. You can also purchase more lives but has a limit.",
    },
    {
      question: "How many lives do I get and how many can I purchase?",
      answer: "Depends on the duration of the challenge, you can check it in the challenge details before signup",
    },
    {
      question: "How will my submission be verified?",
      answer: "Your submission will be verified by the moderator assigned to your challenge. They will check your submission and verify if it is valid or not.",
    },
    {
      question: "What if my submission is invalid?",
      answer: "If your submission is invalid, a life will be deducted from your lives. If you're out of lives, you will be disqualified.",
    },
    {
      question: "Will all the winners get same share of the pot?",
      answer: "No, the share of the pot is distributed based on the number of lives winners have used at the end of the challenge.",
    },
    {
      question: "What happens if I don't make it?",
      answer: "If you don't make it, you lose your money and will be retained in the pool, which will be shared among the winners.",
    },
    {
      question: "How do I track my progress?",
      answer: "Log your daily submissions and monitor improvements on our interactive dashboard.",
    },
    {
      question: "How is my submission data used?",
      answer: "Your data is only used to verify the legitamacy of your submissions and will be deleted 15 days after the challenge ends if there's no pending dispute.",
    },
    {
      question: "Is my data safe?",
      answer: "Your data is encrypted and stored securely. We take data security seriously and will never share your data with anyone no matter what.",
    }
  ], [category, betAmount, startDate, endDate]);

  const [expanded, setExpanded] = useState<Record<number, boolean>>({});

  const toggleFAQ = useCallback((index: number) => {
    setExpanded(prev => ({ ...prev, [index]: !prev[index] }));
  }, []);

  return (
    <View style={styles.container}>
      {/* How It Works Section */}
      <BlurView intensity={40} tint="dark" style={styles.howItWorksContainer}>
        <LinearGradient
          colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
          style={styles.howItWorksGradient}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="lightbulb-on" size={24} color={colors.primary} />
            <Text style={styles.howItWorksTitle}>How It Works</Text>
          </View>
          <View style={styles.stepsGrid}>
            {steps.map((step, idx) => (
              <BlurView key={idx} intensity={30} tint="dark" style={styles.stepCard}>
                <LinearGradient
                  colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
                  style={styles.stepCardGradient}
                >
                  <View style={styles.stepNumberContainer}>
                    <LinearGradient
                      colors={[colors.primary, colors.warning]}
                      style={styles.stepNumberGradient}
                    >
                      <Text style={styles.stepNumber}>{idx + 1}</Text>
                    </LinearGradient>
                  </View>
                  <MaterialCommunityIcons name={step.iconName as any} size={28} color={colors.primary} />
                  <Text style={styles.stepTitle}>{step.title}</Text>
                  <Text style={styles.stepDescription}>{step.description}</Text>
                </LinearGradient>
              </BlurView>
            ))}
          </View>
        </LinearGradient>
      </BlurView>

      {/* FAQ Section */}
      <BlurView intensity={40} tint="dark" style={styles.faqContainer}>
        <LinearGradient
          colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
          style={styles.faqGradient}
        >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="help-circle" size={24} color={colors.primary} />
            <Text style={styles.faqTitle}>Frequently Asked Questions</Text>
          </View>
          {faqQuestions.map((item, index) => (
            <BlurView key={index} intensity={20} tint="dark" style={styles.faqItem}>
              <LinearGradient
                colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
                style={styles.faqItemGradient}
              >
                <TouchableOpacity onPress={() => toggleFAQ(index)} style={styles.faqQuestionContainer}>
                  <Text style={styles.faqQuestionText}>{item.question}</Text>
                  <MaterialIcons name={expanded[index] ? "keyboard-arrow-up" : "keyboard-arrow-down"} size={20} color={colors.primary} />
                </TouchableOpacity>
                {expanded[index] && (
                  <View style={styles.faqAnswerContainer}>
                    <Text style={styles.faqAnswerText}>{item.answer}</Text>
                  </View>
                )}
              </LinearGradient>
            </BlurView>
          ))}
        </LinearGradient>
      </BlurView>
    </View>
  );
});

const createStyles = (colors: any, isDark: boolean, designSystem: any) => StyleSheet.create({
  container: {
    marginHorizontal: designSystem.spacing.sm,
    gap: designSystem.spacing.sm,
  },
  howItWorksContainer: {
    borderRadius: designSystem.borderRadius.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  howItWorksGradient: {
    padding: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designSystem.spacing.sm,
    justifyContent: 'center',
  },
  howItWorksTitle: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    color: colors.text,
    marginLeft: designSystem.spacing.xs,
  },
  stepsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    gap: designSystem.spacing.xs,
  },
  stepCard: {
    width: "48%",
    borderRadius: designSystem.borderRadius.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.sm,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  stepCardGradient: {
    padding: designSystem.spacing.sm,
    alignItems: "center",
    minHeight: 90,
    justifyContent: 'center',
    position: "relative",
  },
  stepNumberContainer: {
    position: "absolute",
    top: designSystem.spacing.xs,
    left: designSystem.spacing.xs,
    width: 20,
    height: 20,
    borderRadius: 10,
    overflow: 'hidden',
  },
  stepNumberGradient: {
    width: '100%',
    height: '100%',
    justifyContent: "center",
    alignItems: "center",
  },
  stepNumber: {
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: "MontserratBold",
    color: "#000000",
  },
  stepTitle: {
    fontSize: designSystem.typography.fontSize.sm,
    fontFamily: "MontserratBold",
    color: colors.text,
    marginTop: designSystem.spacing.xs,
    textAlign: "center",
  },
  stepDescription: {
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: "MontserratRegular",
    color: colors.textSecondary,
    textAlign: "center",
    marginTop: designSystem.spacing.xs / 2,
    lineHeight: designSystem.typography.lineHeight.normal * designSystem.typography.fontSize.xs,
  },
  faqContainer: {
    borderRadius: designSystem.borderRadius.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  faqGradient: {
    padding: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.lg,
  },
  faqTitle: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    color: colors.text,
    marginLeft: designSystem.spacing.xs,
  },
  faqItem: {
    marginBottom: designSystem.spacing.xs,
    borderRadius: designSystem.borderRadius.sm,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
  },
  faqItemGradient: {
    padding: designSystem.spacing.sm,
  },
  faqQuestionContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  faqQuestionText: {
    fontSize: designSystem.typography.fontSize.sm,
    color: colors.text,
    fontFamily: "MontserratBold",
    flex: 1,
    marginRight: designSystem.spacing.xs,
  },
  faqAnswerContainer: {
    marginTop: designSystem.spacing.xs,
    paddingLeft: designSystem.spacing.xs,
    borderTopWidth: 1,
    borderTopColor: colors.glassBorder,
    paddingTop: designSystem.spacing.xs,
  },
  faqAnswerText: {
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: "MontserratRegular",
    color: colors.textSecondary,
    lineHeight: designSystem.typography.lineHeight.normal * designSystem.typography.fontSize.xs,
  },
});

export default FAQSection;
