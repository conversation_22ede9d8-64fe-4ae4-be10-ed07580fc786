import React from 'react';
import { StyleSheet, View } from 'react-native';
import Svg, { Defs, RadialGradient, Stop, Polygon, Text as SvgText } from 'react-native-svg';

const OctagonalStarStamp: React.FC = () => {
  // Scaled coordinates for a 100x100 view (approximate values)
  const starPoints =
    "50,8 67,33 100,33 75,58 83,83 50,67 17,83 25,58 0,33 33,33";
    
  return (
    <View style={styles.stampContainer}>
      <Svg height="100" width="100" style={{ transform: [{ rotate: "-12deg" }] }}>
        <Defs>
          <RadialGradient id="grad" cx="50%" cy="50%" rx="50%" ry="50%">
            <Stop offset="0" stopColor="#FFD700" stopOpacity="1" />
            <Stop offset="1" stopColor="#FF4500" stopOpacity="1" />
          </RadialGradient>
        </Defs>
        <Polygon
          points={starPoints}
          fill="url(#grad)"
          stroke="#FFEB3B"
          strokeWidth="2"
        />
        <SvgText
          x="50"
          y="45"
          fill="white"
          fontSize="16"
          fontWeight="bold"
          fontFamily="Helvetica"
          letterSpacing="1"
          textAnchor="middle"
          alignmentBaseline="middle"
          stroke="black"
          strokeWidth="1"
        >
          Registration
        </SvgText>
        <SvgText
          x="50"
          y="62"
          fill="white"
          fontSize="16"
          fontWeight="bold"
          fontFamily="Helvetica"
          letterSpacing="1"
          textAnchor="middle"
          alignmentBaseline="middle"
          stroke="black"
          strokeWidth="1"
        >
          Open
        </SvgText>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  stampContainer: {
    position: 'absolute',
    top: 30, // ensures it's below the status bar
    left: 10,
    zIndex: 10,
  },
});

export default OctagonalStarStamp;
