import React, { useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  Platform,
  Image,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { PREMIUM_FEATURES, SUBSCRIPTION_PRICING, getSubscriptionFeatures } from '@/shared/constants/premiumFeatures';
import { BlurView } from 'expo-blur';
import { LinearGradient } from 'expo-linear-gradient';
import { VantaDotsBackground } from '@/shared/components/ui/VantaDots';

// Platform-specific blur configuration
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 15,
      tint: 'dark' as const
    };
  } else {
    return {
      intensity: 30,
      tint: 'systemMaterialDark' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  }
};

// Enhanced Floating Particle System
const FloatingParticle: React.FC<{
  x: number;
  y: number;
  size: number;
  delay: number;
  color: string;
}> = ({ x, y, size, delay, color }) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const translateX = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(0.3)).current;
  const rotate = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      opacity.setValue(0);
      translateY.setValue(0);
      translateX.setValue(0);
      scale.setValue(0.3);
      rotate.setValue(0);

      Animated.sequence([
        Animated.parallel([
          Animated.timing(opacity, {
            toValue: 0.9,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1.2,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(rotate, {
            toValue: 1,
            duration: 8000,
            useNativeDriver: true,
          }),
        ]),
        Animated.parallel([
          Animated.timing(translateY, {
            toValue: -30,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(translateX, {
            toValue: Math.random() > 0.5 ? 15 : -15,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 3000,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0.2,
            duration: 3000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        setTimeout(animate, Math.random() * 2000 + 1000);
      });
    };

    setTimeout(animate, delay);
  }, [delay, opacity, translateY, translateX, scale, rotate]);

  const rotateInterpolate = rotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={{
        position: 'absolute',
        left: x,
        top: y,
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: color,
        opacity,
        transform: [
          { translateY },
          { translateX },
          { scale },
          { rotate: rotateInterpolate }
        ],
        shadowColor: color,
        shadowOpacity: 0.8,
        shadowRadius: 12,
        shadowOffset: { width: 0, height: 4 },
        elevation: 8,
      }}
    />
  );
};

// Pulsing Crown Animation
const PulsingCrown: React.FC = () => {
  const scale = useRef(new Animated.Value(1)).current;
  const opacity = useRef(new Animated.Value(0.7)).current;

  useEffect(() => {
    const animate = () => {
      Animated.parallel([
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 1.15,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(opacity, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0.7,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => animate());
    };

    animate();
  }, [scale, opacity]);

  return (
    <Animated.View
      style={{
        transform: [{ scale }],
        opacity,
      }}
    >
      <MaterialIcons name="diamond" size={28} color="#FFD700" />
    </Animated.View>
  );
};

// Shimmer Effect for Feature Cards
const ShimmerEffect: React.FC = () => {
  const shimmer = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      Animated.sequence([
        Animated.timing(shimmer, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmer, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ]).start(() => animate());
    };

    animate();
  }, [shimmer]);

  const translateX = shimmer.interpolate({
    inputRange: [0, 1],
    outputRange: [-100, 100],
  });

  return (
    <Animated.View
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        transform: [{ translateX }],
      }}
    >
      <LinearGradient
        colors={[
          'transparent',
          'rgba(255, 255, 255, 0.1)',
          'rgba(255, 215, 0, 0.1)',
          'rgba(255, 255, 255, 0.1)',
          'transparent',
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{
          flex: 1,
          width: '200%',
        }}
      />
    </Animated.View>
  );
};

// Pulsing Button Animation
const PulsingButton: React.FC<{ children: React.ReactNode; onPress: () => void }> = ({ children, onPress }) => {
  const scale = useRef(new Animated.Value(1)).current;
  const shadowOpacity = useRef(new Animated.Value(0.4)).current;

  useEffect(() => {
    const animate = () => {
      Animated.parallel([
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
        Animated.sequence([
          Animated.timing(shadowOpacity, {
            toValue: 0.8,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shadowOpacity, {
            toValue: 0.4,
            duration: 1000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => animate());
    };

    animate();
  }, [scale, shadowOpacity]);

  return (
    <Animated.View
      style={{
        transform: [{ scale }],
        shadowOpacity,
      }}
    >
      <TouchableOpacity onPress={onPress}>
        {children}
      </TouchableOpacity>
    </Animated.View>
  );
};

interface SubscriptionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubscribe: () => void;
  featureName: string;
}

export const SubscriptionModal: React.FC<SubscriptionModalProps> = ({
  visible,
  onClose,
  onSubscribe,
  featureName,
}) => {
  const { colors, designSystem, isDark } = useTheme();

  const premiumFeatures = [
    { icon: 'sms', title: 'SMS Reminders', description: 'Never miss a commitment' },
    { icon: 'chat', title: 'WhatsApp Alerts', description: 'Get reminders instantly' },
    { icon: 'edit', title: 'Motivational Notes', description: 'Personal motivation' },
    { icon: 'favorite', title: 'Grace Days', description: 'Flexibility when needed' },
    { icon: 'schedule', title: 'Custom Timing', description: 'Set perfect reminder times' },
    { icon: 'analytics', title: 'Advanced Analytics', description: 'Deep insights & patterns' },
    { icon: 'support-agent', title: 'Priority Support', description: 'Get help faster' },
    { icon: 'event', title: 'Calendar Sync', description: 'Sync with your calendar' },
  ];

  const styles = {
    overlay: {
      flex: 1,
      backgroundColor: '#000000',
    },
    modalContainer: {
      flex: 1,
      backgroundColor: '#000000',
      position: 'relative' as const,
      paddingTop: 50,
      paddingBottom: 30,
      paddingHorizontal: 20,
      justifyContent: 'space-between' as const,
    },
    backgroundPattern: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: 0.03,
    },
    closeButton: {
      position: 'absolute' as const,
      top: 20,
      right: 20,
      padding: 12,
      borderRadius: 20,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      zIndex: 1000,
    },
    header: {
      alignItems: 'center' as const,
    },
    brandContainer: {
      alignItems: 'center' as const,
      marginBottom: 12,
    },
    logoContainer: {
      position: 'relative' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginBottom: 6,
    },
    logo: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: 'transparent',
      padding: 6,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    crownContainer: {
      position: 'absolute' as const,
      top: -8,
      right: -8,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderRadius: 12,
      padding: 2,
      borderWidth: 1,
      borderColor: 'rgba(255, 215, 0, 0.3)',
    },
    brandName: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: '#FFFFFF',
      marginBottom: 2,
      textAlign: 'center' as const,
      letterSpacing: 1,
    },
    brandTagline: {
      fontSize: 11,
      fontFamily: 'MontserratMedium',
      color: 'rgba(255, 255, 255, 0.6)',
      textAlign: 'center' as const,
      marginBottom: 12,
    },
    titleContainer: {
      position: 'relative' as const,
      alignItems: 'center' as const,
      marginBottom: 4,
    },
    titleGlow: {
      position: 'absolute' as const,
      fontSize: 24,
      fontFamily: 'MontserratBold',
      color: '#FFD700',
      textAlign: 'center' as const,
      letterSpacing: 1.2,
      textShadowColor: '#FFD700',
      textShadowOffset: { width: 0, height: 0 },
      textShadowRadius: 20,
      opacity: 0.8,
    },
    title: {
      fontSize: 24,
      fontFamily: 'MontserratBold',
      color: '#FFD700',
      textAlign: 'center' as const,
      textShadowColor: 'rgba(255, 215, 0, 0.5)',
      textShadowOffset: { width: 0, height: 3 },
      textShadowRadius: 8,
      letterSpacing: 1.2,
    },
    subtitle: {
      fontSize: 13,
      fontFamily: 'MontserratRegular',
      color: 'rgba(255, 255, 255, 0.8)',
      textAlign: 'center' as const,
      marginBottom: 12,
      lineHeight: 18,
    },
    priceContainer: {
      alignItems: 'center' as const,
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      borderRadius: 12,
      padding: 12,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.1)',
      ...designSystem.shadows.xl,
      shadowColor: 'rgba(0, 0, 0, 0.3)',
      shadowOpacity: 0.3,
      position: 'relative' as const,
      overflow: 'hidden' as const,
    },
    priceGradientOverlay: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      opacity: 0.1,
    },
    priceContent: {
      alignItems: 'center' as const,
      zIndex: 1,
    },
    priceText: {
      fontSize: 28,
      fontFamily: 'MontserratBold',
      color: '#FFD700',
      textShadowColor: 'rgba(255, 215, 0, 0.5)',
      textShadowOffset: { width: 0, height: 2 },
      textShadowRadius: 6,
    },
    priceSubtext: {
      fontSize: 12,
      fontFamily: 'MontserratMedium',
      color: 'rgba(255, 255, 255, 0.8)',
      marginTop: 2,
      letterSpacing: 0.5,
    },
    featuresContainer: {
      flex: 1,
      justifyContent: 'center' as const,
    },
    featuresTitle: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: '#FFFFFF',
      textAlign: 'center' as const,
      marginBottom: 16,
      textShadowColor: 'rgba(255, 255, 255, 0.1)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
    },
    featuresGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      justifyContent: 'space-between' as const,
    },
    featureCard: {
      width: '48%' as any,
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      borderRadius: 10,
      padding: 10,
      marginBottom: 8,
      alignItems: 'center' as const,
      borderWidth: 1,
      borderColor: 'rgba(255, 255, 255, 0.15)',
      minHeight: 85,
      ...designSystem.shadows.lg,
      shadowColor: 'rgba(0, 0, 0, 0.2)',
      shadowOpacity: 0.3,
      position: 'relative' as const,
      overflow: 'hidden' as const,
    },
    featureCardGlow: {
      position: 'absolute' as const,
      top: -50,
      left: -50,
      right: -50,
      bottom: -50,
      backgroundColor: 'rgba(255, 255, 255, 0.02)',
      borderRadius: 100,
    },
    featureIconContainer: {
      backgroundColor: 'rgba(255, 215, 0, 0.12)',
      borderRadius: 8,
      padding: 4,
      marginBottom: 6,
    },
    featureTitle: {
      fontSize: 13,
      fontFamily: 'MontserratSemiBold',
      color: '#FFFFFF',
      textAlign: 'center' as const,
      marginBottom: 4,
      letterSpacing: 0.3,
    },
    featureDescription: {
      fontSize: 10,
      fontFamily: 'MontserratRegular',
      color: 'rgba(255, 255, 255, 0.75)',
      textAlign: 'center' as const,
      lineHeight: 14,
    },
    ctaContainer: {
      alignItems: 'center' as const,
      paddingTop: 12,
    },
    upgradeButton: {
      marginBottom: 12,
      alignSelf: 'stretch' as const,
      borderRadius: designSystem.borderRadius.xl,
      overflow: 'hidden' as const,
      borderWidth: 2,
      borderColor: '#FFD700', // Golden border like create commit
      ...designSystem.shadows.xl,
      shadowColor: '#FFD700',
    },
    upgradeContainer: {
      paddingVertical: designSystem.spacing.xl,
      paddingHorizontal: designSystem.spacing.xl,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      position: 'relative' as const,
      backgroundColor: '#000000', // Black background like create commit
      minHeight: 60,
      width: '100%' as any,
      overflow: 'hidden' as const,
    },
    upgradeBlur: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 2,
      borderRadius: designSystem.borderRadius.xl,
      overflow: 'hidden' as const,
    },
    upgradeContent: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      zIndex: 30, // Above blur layer
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    upgradeText: {
      color: "#FFD700", // Gold text like create commit
      fontSize: designSystem.typography.fontSize.lg,
      fontFamily: "MontserratBold",
      letterSpacing: designSystem.typography.letterSpacing.wide,
      textShadowColor: 'rgba(0, 0, 0, 0.8)',
      textShadowOffset: { width: 0, height: 1 },
      textShadowRadius: 2,
      textAlign: 'center' as const,
      marginHorizontal: designSystem.spacing.sm,
    },
    skipText: {
      fontSize: 16,
      fontFamily: 'MontserratMedium',
      color: 'rgba(255, 255, 255, 0.65)',
      textDecorationLine: 'underline' as const,
      textDecorationColor: 'rgba(255, 255, 255, 0.3)',
    },
    vantaDotsContainer: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      zIndex: 0, // Above background but below content
    },

  };

  if (!visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        {/* VantaDots Background */}
        <VantaDotsBackground screenKey="subscription-modal" />

        {/* Enhanced Floating Particle System */}
        <FloatingParticle x={40} y={120} size={6} delay={0} color="#FFD700" />
        <FloatingParticle x={320} y={180} size={4} delay={600} color="#FF6B6B" />
        <FloatingParticle x={120} y={250} size={8} delay={1200} color="#4ECDC4" />
        <FloatingParticle x={280} y={90} size={5} delay={1800} color="#45B7D1" />
        <FloatingParticle x={60} y={350} size={7} delay={2400} color="#96CEB4" />
        <FloatingParticle x={200} y={60} size={4} delay={3000} color="#FFEAA7" />
        <FloatingParticle x={340} y={320} size={6} delay={3600} color="#DDA0DD" />
        <FloatingParticle x={30} y={220} size={5} delay={4200} color="#98D8C8" />
        <FloatingParticle x={180} y={400} size={3} delay={5000} color="#F7DC6F" />
        <FloatingParticle x={350} y={150} size={4} delay={5600} color="#BB8FCE" />

        <View style={styles.modalContainer}>
          {/* VantaDots Background */}
          <View style={styles.vantaDotsContainer}>
            <VantaDotsBackground screenKey="subscription-modal" />
          </View>

          {/* Subtle Background Pattern */}
          <LinearGradient
            colors={[
              'rgba(255, 215, 0, 0.02)',
              'rgba(255, 165, 0, 0.01)',
              'rgba(255, 215, 0, 0.02)',
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.backgroundPattern}
          />

          {/* Close Button */}
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <MaterialIcons name="close" size={24} color="#FFD700" />
          </TouchableOpacity>

          {/* Header */}
          <View style={styles.header}>
            {/* Brand Section */}
            <View style={styles.brandContainer}>
              <View style={styles.logoContainer}>
                <View style={styles.logo}>
                  <Image
                    source={require('@/assets/images/new_logo.png')}
                    style={{ width: 48, height: 48 }}
                    resizeMode="contain"
                  />
                </View>
              </View>
              <Text style={styles.brandName}>ACCUSTOM</Text>
              <Text style={styles.brandTagline}>Premium Habit Tracking</Text>
            </View>

            <View style={styles.titleContainer}>
              <Text style={styles.titleGlow}>Unlock Premium</Text>
              <Text style={styles.title}>Unlock Premium</Text>
            </View>
            <Text style={styles.subtitle}>Advanced tools for maximum success</Text>

            <View style={styles.priceContainer}>
              <LinearGradient
                colors={['rgba(255, 255, 255, 0.08)', 'rgba(255, 255, 255, 0.03)']}
                style={styles.priceGradientOverlay}
              />
              <View style={styles.priceContent}>
                <Text style={styles.priceText}>${SUBSCRIPTION_PRICING.monthly.amount}</Text>
                <Text style={styles.priceSubtext}>per month</Text>
              </View>
            </View>
          </View>

          {/* Features Grid */}
          <View style={styles.featuresContainer}>
            <Text style={styles.featuresTitle}>Everything Included</Text>
            <View style={styles.featuresGrid}>
              {premiumFeatures.map((feature, index) => (
                <View key={index} style={styles.featureCard}>
                  <View style={styles.featureCardGlow} />
                  <View style={styles.featureIconContainer}>
                    <MaterialIcons name={feature.icon as any} size={20} color="#FFD700" />
                  </View>
                  <Text style={styles.featureTitle}>{feature.title}</Text>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </View>
              ))}
            </View>
          </View>

          {/* CTA */}
          <View style={styles.ctaContainer}>
            <TouchableOpacity
              style={styles.upgradeButton}
              onPress={onSubscribe}
              activeOpacity={0.8}
            >
              <View style={styles.upgradeContainer}>
                {/* Floating Dots Animation - Same as settings screen */}
                {[
                  { x: 20, y: 15, size: 16, delay: 0 },
                  { x: 60, y: 25, size: 20, delay: 200 },
                  { x: 40, y: 40, size: 14, delay: 400 },
                  { x: 100, y: 20, size: 18, delay: 600 },
                  { x: 130, y: 35, size: 15, delay: 800 },
                  { x: 80, y: 45, size: 19, delay: 1000 },
                  { x: 170, y: 25, size: 17, delay: 1200 },
                  { x: 200, y: 35, size: 22, delay: 1400 },
                  { x: 180, y: 10, size: 13, delay: 1600 },
                  { x: 240, y: 30, size: 18, delay: 1800 },
                  { x: 270, y: 15, size: 16, delay: 2000 },
                  { x: 250, y: 45, size: 15, delay: 2200 },
                  { x: 300, y: 25, size: 17, delay: 2400 },
                  { x: 320, y: 40, size: 14, delay: 2600 },
                ].map((dot, index) => (
                  <FloatingParticle
                    key={index}
                    x={dot.x}
                    y={dot.y}
                    size={dot.size}
                    delay={dot.delay}
                    color="#FFD700"
                  />
                ))}

                <BlurView
                  intensity={getBlurConfig().intensity}
                  tint={getBlurConfig().tint}
                  {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
                  style={styles.upgradeBlur}
                />
                <View style={styles.upgradeContent}>
                  <MaterialIcons name="diamond" size={18} color="#FFD700" />
                  <Text style={styles.upgradeText}>Get Premium Now</Text>
                  <MaterialIcons name="arrow-right" size={16} color="#FFD700" />
                </View>
              </View>
            </TouchableOpacity>
            <TouchableOpacity onPress={onClose}>
              <Text style={styles.skipText}>Maybe Later</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};
