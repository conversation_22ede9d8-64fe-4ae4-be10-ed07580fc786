import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
  ScrollView,
  Animated,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';

const { height: screenHeight, width: screenWidth } = Dimensions.get('window');

// Generate amounts from $2 to $18
const AMOUNT_OPTIONS = Array.from({ length: 17 }, (_, i) => i + 2);
const ITEM_HEIGHT = 80;
const VISIBLE_ITEMS = 5;
const DEFAULT_AMOUNT = 10;

interface AmountSelectorProps {
  selectedAmount: number;
  onAmountSelect: (amount: number) => void;
  visible: boolean;
  onClose: () => void;
}

export const AmountSelector: React.FC<AmountSelectorProps> = ({
  selectedAmount,
  onAmountSelect,
  visible,
  onClose,
}) => {
  const { colors } = useTheme();
  const scrollViewRef = useRef<ScrollView>(null);
  const [currentAmount, setCurrentAmount] = useState(selectedAmount || DEFAULT_AMOUNT);

  // Initialize scroll position when modal opens
  useEffect(() => {
    if (visible && scrollViewRef.current) {
      const index = AMOUNT_OPTIONS.indexOf(currentAmount);
      if (index !== -1) {
        setTimeout(() => {
          scrollViewRef.current?.scrollTo({
            y: index * ITEM_HEIGHT,
            animated: false,
          });
        }, 100);
      }
    }
  }, [visible]);

  const getAmountFromScrollPosition = (y: number) => {
    const index = Math.round(y / ITEM_HEIGHT);
    const clampedIndex = Math.max(0, Math.min(index, AMOUNT_OPTIONS.length - 1));
    return AMOUNT_OPTIONS[clampedIndex];
  };

  const handleScroll = (event: any) => {
    const y = event.nativeEvent.contentOffset.y;
    const newAmount = getAmountFromScrollPosition(y);
    
    if (newAmount !== currentAmount) {
      setCurrentAmount(newAmount);
    }
  };

  const handleScrollEnd = (event: any) => {
    const y = event.nativeEvent.contentOffset.y;
    const newAmount = getAmountFromScrollPosition(y);
    setCurrentAmount(newAmount);
  };

  const handleConfirm = () => {
    onAmountSelect(currentAmount);
    onClose();
  };

  const renderWheelItem = (amount: number, index: number) => {
    const isSelected = amount === currentAmount;

    return (
      <View
        key={amount}
        style={styles.wheelItem}
      >
        <Text
          style={[
            styles.amountText,
            {
              color: isSelected ? colors.primary : colors.textMuted,
              fontSize: isSelected ? 28 : 20,
              fontFamily: isSelected ? 'MontserratBold' : 'MontserratRegular',
              opacity: isSelected ? 1 : 0.5,
            }
          ]}
        >
          ${amount}
        </Text>
      </View>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={styles.backdrop}>
        <View style={[styles.container, { backgroundColor: colors.surface }]}>
          <View style={styles.header}>
            <View>
              <Text style={[styles.title, { color: colors.text }]}>Select Amount</Text>
              <Text style={[styles.subtitle, { color: colors.textMuted }]}>
                How much do you want to stake per report?
              </Text>
            </View>
            <TouchableOpacity onPress={onClose} style={[styles.closeBtn, { backgroundColor: colors.background }]}>
              <MaterialIcons name="close" size={18} color={colors.textMuted} />
            </TouchableOpacity>
          </View>

          <View style={styles.wheelContainer}>
            <View style={[styles.selectionIndicator, { borderColor: colors.primary }]} />

            <ScrollView
              ref={scrollViewRef}
              style={styles.wheelScrollView}
              contentContainerStyle={styles.wheelContent}
              showsVerticalScrollIndicator={false}
              snapToInterval={ITEM_HEIGHT}
              decelerationRate="fast"
              onScroll={handleScroll}
              onMomentumScrollEnd={handleScrollEnd}
              scrollEventThrottle={16}
            >
              <View style={{ height: ITEM_HEIGHT * 2 }} />
              {AMOUNT_OPTIONS.map((amount, index) => renderWheelItem(amount, index))}
              <View style={{ height: ITEM_HEIGHT * 2 }} />
            </ScrollView>
          </View>

          <View style={styles.footer}>
            <TouchableOpacity
              style={[styles.confirmButton, { backgroundColor: colors.primary }]}
              onPress={handleConfirm}
            >
              <Text style={[styles.confirmButtonText, { color: colors.background }]}>
                Confirm ${currentAmount}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// Button Component
interface AmountButtonProps {
  selectedAmount: number;
  onPress: () => void;
}

export const AmountButton: React.FC<AmountButtonProps> = ({
  selectedAmount,
  onPress,
}) => {
  const { colors } = useTheme();

  return (
    <TouchableOpacity
      style={[styles.button, { backgroundColor: colors.surface, borderColor: colors.primary }]}
      onPress={onPress}
      accessibilityLabel={`Selected amount: $${selectedAmount}`}
      accessibilityRole="button"
      accessibilityHint="Tap to change stake amount"
    >
      <MaterialIcons name="attach-money" size={16} color={colors.primary} />
      <Text style={[styles.buttonText, { color: colors.primary }]}>
        {selectedAmount}
      </Text>
      <MaterialIcons name="keyboard-arrow-down" size={16} color={colors.primary} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    maxHeight: screenHeight * 0.7,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
  },
  closeBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  wheelContainer: {
    height: ITEM_HEIGHT * VISIBLE_ITEMS,
    marginHorizontal: 24,
    position: 'relative',
  },
  selectionIndicator: {
    position: 'absolute',
    top: ITEM_HEIGHT * 2,
    left: 0,
    right: 0,
    height: ITEM_HEIGHT,
    borderTopWidth: 1,
    borderBottomWidth: 1,
    zIndex: 1,
    pointerEvents: 'none',
  },
  wheelScrollView: {
    flex: 1,
  },
  wheelContent: {
    paddingVertical: 0,
  },
  wheelItem: {
    height: ITEM_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
  },
  amountText: {
    textAlign: 'center',
  },
  footer: {
    padding: 24,
    paddingTop: 16,
  },
  confirmButton: {
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  confirmButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
  },
  button: {
    borderRadius: 8,
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderWidth: 2,
    marginHorizontal: 3,
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 85,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginHorizontal: 4,
  },
});
