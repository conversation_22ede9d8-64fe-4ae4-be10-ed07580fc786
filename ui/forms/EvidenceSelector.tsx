import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { EvidenceType, EVIDENCE_OPTIONS } from '@/shared/types/customStake';

const { height: screenHeight } = Dimensions.get('window');

interface EvidenceSelectorProps {
  selectedEvidence: EvidenceType;
  onEvidenceSelect: (evidence: EvidenceType) => void;
  visible: boolean;
  onClose: () => void;
}

export const EvidenceSelector: React.FC<EvidenceSelectorProps> = ({
  selectedEvidence,
  onEvidenceSelect,
  visible,
  onClose,
}) => {
  const { colors } = useTheme();

  const handleSelect = (evidenceType: EvidenceType) => {
    onEvidenceSelect(evidenceType);
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.backdrop}>
        <View style={[styles.container, { backgroundColor: colors.surface }]}>
          <View style={styles.header}>
            <View>
              <Text style={[styles.title, { color: colors.text }]}>Select Evidence Type</Text>
              <Text style={[styles.subtitle, { color: colors.textMuted }]}>
                How will you prove your commitment?
              </Text>
            </View>
            <TouchableOpacity
              onPress={onClose}
              style={[styles.closeBtn, { backgroundColor: colors.background }]}
              accessibilityLabel="Close evidence selector"
              accessibilityRole="button"
            >
              <MaterialIcons name="close" size={18} color={colors.textMuted} />
            </TouchableOpacity>
          </View>

          <View style={styles.options}>
            {EVIDENCE_OPTIONS.map((option) => (
              <TouchableOpacity
                key={option.key}
                style={[styles.option, { backgroundColor: colors.background }]}
                onPress={() => handleSelect(option.key)}
                accessibilityLabel={`Select ${option.label} evidence type`}
                accessibilityRole="button"
                accessibilityHint={option.description}
                activeOpacity={0.8}
              >
                <View style={styles.optionContent}>
                  <View style={[styles.iconContainer, { backgroundColor: colors.surface }]}>
                    <MaterialIcons
                      name={option.icon as any}
                      size={24}
                      color={colors.text}
                    />
                  </View>
                  <View style={styles.textContainer}>
                    <View style={styles.titleRow}>
                      <Text style={[styles.optionTitle, { color: colors.text }]}>
                        {option.label}
                      </Text>
                      {selectedEvidence === option.key && (
                        <MaterialIcons name="check-circle" size={20} color={colors.primary} />
                      )}
                    </View>
                    <Text style={[styles.optionDescription, { color: colors.textMuted }]}>
                      {option.description}
                    </Text>
                  </View>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Modal>
  );
};

interface EvidenceButtonProps {
  selectedEvidence: EvidenceType;
  onPress: () => void;
}

export const EvidenceButton: React.FC<EvidenceButtonProps> = ({
  selectedEvidence,
  onPress,
}) => {
  const { colors } = useTheme();
  const selectedOption = EVIDENCE_OPTIONS.find(opt => opt.key === selectedEvidence);

  return (
    <TouchableOpacity
      style={[styles.button, { backgroundColor: colors.surface, borderColor: colors.primary }]}
      onPress={onPress}
      accessibilityLabel={`Selected evidence type: ${selectedOption?.label || 'evidence'}`}
      accessibilityRole="button"
      accessibilityHint="Tap to change evidence type"
    >
      <Text style={[styles.buttonText, { color: colors.primary }]}>
        {selectedOption?.label.toLowerCase() || 'evidence'}
      </Text>
      <MaterialIcons name="keyboard-arrow-down" size={16} color={colors.primary} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    paddingBottom: 24,
    maxHeight: screenHeight * 0.8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
  },
  closeBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  options: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  option: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  optionContent: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  optionTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    flex: 1,
  },
  optionDescription: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    lineHeight: 20,
  },
  button: {
    backgroundColor: 'transparent', // Will be set dynamically
    borderRadius: 8,
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderWidth: 2,
    borderColor: 'transparent', // Will be set dynamically
    marginHorizontal: 3,
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 85,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: 'transparent', // Will be set dynamically
    textAlign: 'center',
    marginRight: 4,
  },
});
