import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { RecipientType, RECIPIENT_OPTIONS } from '@/shared/types/customStake';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface RecipientSelectorProps {
  selectedRecipient: RecipientType;
  onRecipientSelect: (recipient: RecipientType) => void;
  visible: boolean;
  onClose: () => void;
}

export const RecipientSelector: React.FC<RecipientSelectorProps> = ({
  selectedRecipient,
  onRecipientSelect,
  visible,
  onClose,
}) => {
  const { colors } = useTheme();

  const handleSelect = (recipient: RecipientType) => {
    onRecipientSelect(recipient);
    onClose();
  };

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={styles.backdrop}>
        <View style={[styles.container, { backgroundColor: colors.surface }]}>
          
          {/* Header */}
          <View style={styles.header}>
            <View>
              <Text style={[styles.title, { color: colors.text }]}>Where should your stake go?</Text>
              <Text style={[styles.subtitle, { color: colors.textMuted }]}>
                If you don't complete your commitment
              </Text>
            </View>
            <TouchableOpacity onPress={onClose} style={[styles.close, { backgroundColor: colors.background }]}>
              <MaterialIcons name="close" size={18} color={colors.textMuted} />
            </TouchableOpacity>
          </View>

          {/* Options */}
          <View style={styles.options}>
            {RECIPIENT_OPTIONS.map((option, index) => {
              const isSelected = selectedRecipient === option.key;
              const isCharity = option.key === 'charity';
              
              return (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.card,
                    { 
                      backgroundColor: colors.background,
                      borderColor: isSelected ? colors.primary : colors.border + '30',
                      borderWidth: isSelected ? 2 : 1,
                    }
                  ]}
                  onPress={() => handleSelect(option.key)}
                  activeOpacity={0.8}
                >
                  {/* Image Side */}
                  <View style={styles.imageContainer}>
                    {isCharity ? (
                      <Image
                        source={{ 
                          uri: 'https://images.unsplash.com/photo-1488521787991-ed7bbaae773c?w=300&h=300&fit=crop&crop=faces' 
                        }}
                        style={styles.charityImage}
                        defaultSource={require('@/assets/images/new_logo.png')}
                      />
                    ) : (
                      <View style={styles.codingContainer}>
                        <Image
                          source={{
                            uri: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=300&h=300&fit=crop&crop=center'
                          }}
                          style={styles.codingImage}
                          defaultSource={require('@/assets/images/new_logo.png')}
                        />
                        <View style={styles.logoOverlay}>
                          <Image
                            source={require('@/assets/images/new_logo.png')}
                            style={styles.overlayLogo}
                            resizeMode="contain"
                          />
                        </View>
                      </View>
                    )}
                    
                    {/* Gradient Fade */}
                    <LinearGradient
                      colors={['transparent', colors.background + 'CC', colors.background]}
                      style={styles.gradientFade}
                      start={{ x: 0, y: 0 }}
                      end={{ x: 1, y: 0 }}
                    />
                  </View>

                  {/* Content Side */}
                  <View style={styles.content}>
                    <View style={styles.contentHeader}>
                      <Text style={[
                        styles.optionName,
                        {
                          color: colors.text,
                          fontFamily: 'MontserratBold'
                        }
                      ]}>
                        {option.label}
                      </Text>
                      {isSelected && (
                        <View style={[styles.checkmark, { backgroundColor: colors.primary }]}>
                          <MaterialIcons name="check" size={14} color="white" />
                        </View>
                      )}
                    </View>
                    
                    <Text style={[styles.description, { color: colors.textMuted }]}>
                      {isCharity
                        ? 'Support children in need through verified charities'
                        : 'Help us improve the platform for everyone'
                      }
                    </Text>


                  </View>
                </TouchableOpacity>
              );
            })}
          </View>

          {/* Learn More Link */}
          <View style={styles.footer}>
            <TouchableOpacity
              style={styles.learnMoreButton}
              onPress={() => {
                // TODO: Navigate to charity info page or open web link
                console.log('Learn more about charity connections');
              }}
            >
              <MaterialIcons name="info-outline" size={16} color={colors.textMuted} />
              <Text style={[styles.learnMoreText, { color: colors.textMuted }]}>
                Learn more about our charity connections
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

// Button Component
interface RecipientButtonProps {
  selectedRecipient: RecipientType;
  onPress: () => void;
}

export const RecipientButton: React.FC<RecipientButtonProps> = ({
  selectedRecipient,
  onPress,
}) => {
  const { colors } = useTheme();
  const selectedOption = RECIPIENT_OPTIONS.find(opt => opt.key === selectedRecipient);

  return (
    <TouchableOpacity 
      style={[styles.button, { 
        backgroundColor: colors.surface,
        borderColor: colors.primary 
      }]} 
      onPress={onPress}
    >
      <Text style={[styles.buttonText, { color: colors.primary }]}>
        {selectedOption?.label.toLowerCase() || 'recipient'}
      </Text>
      <MaterialIcons name="keyboard-arrow-down" size={16} color={colors.primary} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    maxHeight: screenHeight * 0.8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
  },
  close: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  options: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  card: {
    flexDirection: 'row',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    height: 120,
  },
  imageContainer: {
    width: '40%',
    position: 'relative',
  },
  charityImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  codingContainer: {
    width: '100%',
    height: '100%',
    position: 'relative',
  },
  codingImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  logoOverlay: {
    position: 'absolute',
    top: 12,
    left: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'white',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 4,
    borderWidth: 1,
    borderColor: 'rgba(0, 0, 0, 0.1)',
  },
  overlayLogo: {
    width: 20,
    height: 20,
    tintColor: '#000',
  },
  gradientFade: {
    position: 'absolute',
    right: 0,
    top: 0,
    bottom: 0,
    width: '50%',
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'space-between',
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionName: {
    fontSize: 18,
    flex: 1,
  },
  checkmark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  description: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    lineHeight: 18,
    flex: 1,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'flex-start',
    marginTop: 8,
  },
  badgeText: {
    fontSize: 11,
    fontFamily: 'MontserratMedium',
    marginLeft: 4,
  },
  button: {
    borderRadius: 8,
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderWidth: 2,
    marginHorizontal: 3,
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 85,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginRight: 4,
  },
  footer: {
    paddingHorizontal: 24,
    paddingBottom: 16,
    paddingTop: 4,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  learnMoreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  learnMoreText: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    marginLeft: 6,
  },
});
