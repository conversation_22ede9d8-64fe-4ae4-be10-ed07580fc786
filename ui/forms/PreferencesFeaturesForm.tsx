import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { ProgramPreferences, ReminderChannel, StrictnessLevel, NotificationTiming, REMINDER_CHANNEL_OPTIONS, STRICTNESS_OPTIONS } from '@/shared/types/customStake';
import { SubscriptionModal } from './SubscriptionModal';
import { NotificationTimingSelector } from './NotificationTimingSelector';
import { SuccessModal } from '@/shared/components/modals';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { isPremiumUser } from '@/lib/hooks/useFastSubscription';

interface PreferencesFeaturesFormProps {
  programPreferences: ProgramPreferences;
  reportingFrequency: string;
  onUpdateMotivationalNote: (note: string) => void;
  onUpdateReminderChannels: (channels: ReminderChannel[]) => void;
  onUpdateStrictnessLevel: (level: StrictnessLevel) => void;
  onUpdateNotificationTiming: (timing: NotificationTiming) => void;
  onUpdateProgramPreferences: (updates: Partial<ProgramPreferences>) => void;
  onSubmit?: () => void;
  onCancel?: () => void;
  showActionButtons?: boolean;
  title?: string;
  subtitle?: string;
}

export const PreferencesFeaturesForm: React.FC<PreferencesFeaturesFormProps> = ({
  programPreferences,
  reportingFrequency,
  onUpdateMotivationalNote,
  onUpdateReminderChannels,
  onUpdateStrictnessLevel,
  onUpdateNotificationTiming,
  onUpdateProgramPreferences,
  onSubmit,
  onCancel,
  showActionButtons = true,
  title = "Customize Your Commitment",
  subtitle = "Set up preferences and advanced options",
}) => {
  const { colors } = useTheme();
  const { refreshSubscriptionStatus } = useSubscription();
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false);
  const [selectedFeatureName, setSelectedFeatureName] = useState('');

  // Modal state for replacing Alert dialog
  const [successModalVisible, setSuccessModalVisible] = useState(false);

  const handlePremiumFeatureClick = (featureName: string, callback: () => void) => {
    if (isPremiumUser()) {
      // User is premium, allow the action
      callback();
    } else {
      // User is not premium, show subscription modal
      setSelectedFeatureName(featureName);
      setShowSubscriptionModal(true);
    }
  };

  const handleSubscribe = async () => {
    // TODO: Implement actual subscription purchase logic
    setShowSubscriptionModal(false);

    // Refresh subscription status after purchase
    await refreshSubscriptionStatus();

    setSuccessModalVisible(true);
  };

  const toggleReminderChannel = (channel: ReminderChannel) => {
    const option = REMINDER_CHANNEL_OPTIONS.find(opt => opt.key === channel);

    if (option?.isPremium && !isPremiumUser()) {
      // Show subscription modal for non-premium users
      setSelectedFeatureName(option.label);
      setShowSubscriptionModal(true);
      return;
    }

    const currentChannels = programPreferences.reminderChannels;
    const isSelected = currentChannels.includes(channel);

    if (isSelected) {
      onUpdateReminderChannels(currentChannels.filter(c => c !== channel));
    } else {
      onUpdateReminderChannels([...currentChannels, channel]);
    }
  };

  const selectStrictnessLevel = (level: StrictnessLevel) => {
    const option = STRICTNESS_OPTIONS.find(opt => opt.key === level);

    if (option?.isPremium && !isPremiumUser()) {
      // Show subscription modal for non-premium users
      setSelectedFeatureName(option.label);
      setShowSubscriptionModal(true);
      return;
    }

    onUpdateStrictnessLevel(level);
  };

  const graceDayOptions = [0, 1, 2, 3, 4, 5];

  // Calculate total premium cost (no premium features currently)
  const totalPremiumCost = 0;

  const styles = {
    container: {
      flex: 1,
      backgroundColor: colors.background,
      paddingHorizontal: 12, // Reduced horizontal padding for wider content
      paddingVertical: 20,
    },
    headerSection: {
      alignItems: 'center' as const,
      marginBottom: 24,
      paddingBottom: 16,
      borderBottomWidth: 1,
      borderBottomColor: colors.separator,
    },
    title: {
      fontSize: 22,
      fontFamily: 'MontserratBold',
      color: colors.text,
      marginBottom: 8,
      textAlign: 'center' as const,
    },
    subtitle: {
      fontSize: 14,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      textAlign: 'center' as const,
      lineHeight: 20,
    },
    section: {
      marginBottom: 16, // Reduced gap between sections
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 12, // Reduced padding for wider content
      borderWidth: 1,
      borderColor: colors.border,
    },
    subSection: {
      marginTop: 16,
      paddingTop: 16,
      borderTopWidth: 1,
      borderTopColor: colors.separator,
    },
    subSectionNoBorder: {
      marginTop: 16,
      paddingTop: 16,
    },
    sectionTitle: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: colors.text,
      marginBottom: 16,
      textAlign: 'center' as const,
    },
    sectionDescription: {
      fontSize: 14,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      marginBottom: 16,
      lineHeight: 20,
    },
    textInput: {
      backgroundColor: colors.surface,
      borderRadius: 8, // Match app's standard border radius
      padding: 16,
      fontSize: 16,
      fontFamily: 'MontserratRegular',
      color: colors.text,
      borderWidth: 1,
      borderColor: colors.border,
      minHeight: 100,
      textAlignVertical: 'top' as const,
    },
    channelGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
    },
    channelOption: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.surface,
      borderRadius: 8, // Match app's standard border radius
      padding: 12,
      borderWidth: 1, // Thinner border for cleaner look
      borderColor: colors.border,
      minWidth: '45%',
    },
    channelOptionSelected: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '20',
    },
    channelOptionPremium: {
      // Removed different border color - same as regular options
    },
    channelText: {
      fontSize: 14,
      fontFamily: 'MontserratMedium',
      color: colors.text,
      marginLeft: 8,
      flex: 1,
    },
    premiumBadge: {
      backgroundColor: colors.warning,
      borderRadius: 6, // Smaller border radius
      paddingHorizontal: 4, // Reduced padding
      paddingVertical: 1, // Reduced padding
      marginLeft: 4,
    },
    premiumBadgeText: {
      fontSize: 8, // Smaller font size
      fontFamily: 'MontserratBold',
      color: '#FFFFFF',
    },
    premiumStar: {
      position: 'absolute' as const,
      top: -2,
      right: -2,
    },
    graceDaysGrid: {
      flexDirection: 'row' as const,
      flexWrap: 'wrap' as const,
      gap: 12,
    },
    graceDayOption: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 2,
      borderColor: colors.border,
      alignItems: 'center' as const,
      minWidth: 60,
    },
    graceDayOptionSelected: {
      borderColor: '#FFEB3B',
      backgroundColor: colors.primary + '20',
    },
    graceDayText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: colors.text,
    },
    graceDayCost: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      marginTop: 4,
    },
    totalCost: {
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      marginTop: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    totalCostText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: colors.text,
      textAlign: 'center' as const,
    },
    buttonContainer: {
      flexDirection: 'row' as const,
      gap: 12,
      marginTop: 24,
    },
    button: {
      flex: 1,
      borderRadius: 12,
      paddingVertical: 16,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    cancelButton: {
      backgroundColor: colors.surface,
      borderWidth: 1,
      borderColor: colors.border,
    },
    submitButton: {
      backgroundColor: colors.primary, // App's standard gold color
      shadowColor: colors.primary,
      shadowOffset: {
        width: 0,
        height: 3,
      },
      shadowOpacity: 0.3,
      shadowRadius: 6,
      elevation: 8,
    },
    buttonText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: colors.text,
    },
    submitButtonText: {
      color: '#000000',
    },
    premiumFeatureOption: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.surface,
      borderRadius: 12,
      padding: 16,
      borderWidth: 2,
      borderColor: colors.border,
      marginBottom: 12,
    },
    programPreferenceSelected: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '20',
    },
    featureContent: {
      flex: 1,
      marginLeft: 12,
    },
    featureTitle: {
      fontSize: 16,
      fontFamily: 'MontserratSemiBold',
      color: colors.text,
      marginBottom: 4,
    },
    featureDescription: {
      fontSize: 14,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      lineHeight: 18,
    },
    strictnessGrid: {
      gap: 12,
    },
    strictnessOption: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      backgroundColor: colors.surface,
      borderRadius: 8,
      padding: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    strictnessOptionSelected: {
      borderColor: colors.primary,
      backgroundColor: colors.primary + '20',
    },
    strictnessOptionPremium: {
      // Removed different border color - same as regular options
    },
    strictnessContent: {
      flex: 1,
      marginLeft: 12,
    },
    strictnessTitle: {
      fontSize: 14,
      fontFamily: 'MontserratBold',
      color: colors.text,
      marginBottom: 2,
    },
    strictnessDescription: {
      fontSize: 12,
      fontFamily: 'MontserratRegular',
      color: colors.textSecondary,
      lineHeight: 16,
    },
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.headerSection}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.subtitle}>{subtitle}</Text>
      </View>



      {/* Reminder Channels */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>How would you like to be reminded?</Text>
        <View style={styles.channelGrid}>
          {REMINDER_CHANNEL_OPTIONS.map((option) => {
            const isSelected = programPreferences.reminderChannels.includes(option.key);
            return (
              <TouchableOpacity
                key={option.key}
                style={[
                  styles.channelOption,
                  isSelected && styles.channelOptionSelected,
                  option.isPremium && styles.channelOptionPremium,
                ]}
                onPress={() => toggleReminderChannel(option.key)}
              >
                <MaterialIcons
                  name={option.icon as any}
                  size={20}
                  color={isSelected ? colors.primary : colors.text}
                />
                <Text style={styles.channelText}>{option.label}</Text>
                {option.isPremium && (
                  <View style={styles.premiumBadge}>
                    <Text style={styles.premiumBadgeText}>PRO</Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>

        {/* Notification Timing Selector - Only show if reminder channels are selected */}
        {programPreferences.reminderChannels.length > 0 && (
          <NotificationTimingSelector
            notificationTiming={programPreferences.notificationTiming || { type: 'hours-before', hoursBeforeDeadline: 2 }}
            onUpdate={onUpdateNotificationTiming}
          />
        )}
      </View>

      {/* Strictness Level - Only show if not "once" frequency */}
      {reportingFrequency !== 'once' && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Evaluation Strictness</Text>
          <Text style={styles.sectionDescription}>
            Choose how strict you want your evaluation to be.
          </Text>

          <View style={styles.strictnessGrid}>
            {STRICTNESS_OPTIONS.map((option) => {
              const isSelected = programPreferences.strictnessLevel === option.key;
              return (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.strictnessOption,
                    isSelected && styles.strictnessOptionSelected,
                    option.isPremium && styles.strictnessOptionPremium,
                  ]}
                  onPress={() => selectStrictnessLevel(option.key)}
                >
                  <MaterialIcons
                    name={option.icon as any}
                    size={20}
                    color={isSelected ? colors.primary : colors.text}
                  />
                  <View style={styles.strictnessContent}>
                    <Text style={styles.strictnessTitle}>{option.label}</Text>
                    <Text style={styles.strictnessDescription}>{option.description}</Text>
                  </View>
                  {option.isPremium && (
                    <View style={styles.premiumBadge}>
                      <Text style={styles.premiumBadgeText}>PRO</Text>
                    </View>
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      )}



      {/* Total Premium Cost */}
      {totalPremiumCost > 0 && (
        <View style={styles.totalCost}>
          <Text style={styles.totalCostText}>
            Total Premium Features Cost: ${totalPremiumCost}
          </Text>
        </View>
      )}

      {/* Action Buttons */}
      {showActionButtons && onSubmit && onCancel && (
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={[styles.button, styles.cancelButton]} onPress={onCancel}>
            <Text style={styles.buttonText}>← Step 1</Text>
          </TouchableOpacity>
          <TouchableOpacity style={[styles.button, styles.submitButton]} onPress={onSubmit}>
            <Text style={[styles.buttonText, styles.submitButtonText]}>Next</Text>
          </TouchableOpacity>
        </View>
      )}

      <SubscriptionModal
        visible={showSubscriptionModal}
        onClose={() => setShowSubscriptionModal(false)}
        onSubscribe={handleSubscribe}
        featureName={selectedFeatureName}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title="Subscription"
        message="Premium subscription feature would be implemented here with payment processing."
      />
    </ScrollView>
  );
};

// Backward compatibility exports
export const ProgramPreferencesForm = PreferencesFeaturesForm;
export const PremiumFeaturesForm = PreferencesFeaturesForm;
