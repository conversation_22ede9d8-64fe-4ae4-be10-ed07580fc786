import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { TimingType, TIMING_OPTIONS } from '@/shared/types/customStake';

const { height: screenHeight } = Dimensions.get('window');

interface TimingSelectorProps {
  selectedTiming: TimingType | undefined;
  onTimingSelect: (timing: TimingType) => void;
  visible: boolean;
  onClose: () => void;
}

export const TimingSelector: React.FC<TimingSelectorProps> = ({
  selectedTiming,
  onTimingSelect,
  visible,
  onClose,
}) => {
  const { colors } = useTheme();

  const handleSelect = (timing: TimingType) => {
    onTimingSelect(timing);
    onClose();
  };

  return (
    <Modal visible={visible} transparent animationType="slide" onRequestClose={onClose}>
      <View style={styles.backdrop}>
        <View style={[styles.container, { backgroundColor: colors.surface }]}>
          <View style={styles.header}>
            <View>
              <Text style={[styles.title, { color: colors.text }]}>Select Timing</Text>
              <Text style={[styles.subtitle, { color: colors.textMuted }]}>
                When should you complete your commitment?
              </Text>
            </View>
            <TouchableOpacity onPress={onClose} style={[styles.closeBtn, { backgroundColor: colors.background }]}>
              <MaterialIcons name="close" size={18} color={colors.textMuted} />
            </TouchableOpacity>
          </View>

          <View style={styles.options}>
            {TIMING_OPTIONS.map((option) => {
              const isDisabled = option.key === 'between';
              const isSelected = selectedTiming === option.key;
              
              return (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.option,
                    { 
                      backgroundColor: colors.background,
                      borderColor: isSelected ? colors.primary : colors.border + '30',
                      borderWidth: isSelected ? 2 : 1,
                    },
                    isDisabled && styles.disabledOption
                  ]}
                  onPress={isDisabled ? undefined : () => handleSelect(option.key)}
                  disabled={isDisabled}
                  activeOpacity={isDisabled ? 1 : 0.8}
                >
                  <View style={styles.optionContent}>
                    <View style={styles.iconContainer}>
                      <MaterialIcons 
                        name={option.icon as any} 
                        size={24} 
                        color={isDisabled ? colors.textMuted : colors.text} 
                      />
                    </View>
                    <View style={styles.textContainer}>
                      <View style={styles.titleRow}>
                        <Text style={[
                          styles.optionTitle,
                          { color: isDisabled ? colors.textMuted : colors.text },
                        ]}>
                          {option.label}
                        </Text>
                        {isDisabled && (
                          <View style={[styles.comingSoonTag, { backgroundColor: colors.primary + '20' }]}>
                            <Text style={[styles.comingSoonText, { color: colors.primary }]}>Coming Soon</Text>
                          </View>
                        )}
                        {isSelected && !isDisabled && (
                          <MaterialIcons name="check-circle" size={20} color={colors.primary} />
                        )}
                      </View>
                      <Text style={[styles.optionDescription, { color: colors.textMuted }]}>
                        {option.description}
                      </Text>
                    </View>
                  </View>
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </View>
    </Modal>
  );
};

// Button Component
interface TimingButtonProps {
  selectedTiming: TimingType | undefined;
  onPress: () => void;
  displayText?: string;
}

export const TimingButton: React.FC<TimingButtonProps> = ({
  selectedTiming,
  onPress,
  displayText,
}) => {
  const { colors } = useTheme();
  const selectedOption = TIMING_OPTIONS.find(opt => opt.key === selectedTiming);
  const buttonText = displayText || selectedOption?.label.toLowerCase() || 'timing';

  return (
    <TouchableOpacity style={[styles.button, { backgroundColor: colors.surface, borderColor: colors.primary }]} onPress={onPress}>
      <Text style={[styles.buttonText, { color: colors.primary }]}>
        {buttonText}
      </Text>
      <MaterialIcons name="keyboard-arrow-down" size={16} color={colors.primary} />
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  backdrop: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'flex-end',
  },
  container: {
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 8,
    maxHeight: screenHeight * 0.8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 24,
    paddingBottom: 16,
  },
  title: {
    fontSize: 20,
    fontFamily: 'MontserratBold',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
  },
  closeBtn: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  options: {
    paddingHorizontal: 24,
    paddingBottom: 32,
  },
  option: {
    marginBottom: 16,
    borderRadius: 16,
    overflow: 'hidden',
  },
  disabledOption: {
    opacity: 0.6,
  },
  optionContent: {
    flexDirection: 'row',
    padding: 20,
    alignItems: 'center',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  optionTitle: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    flex: 1,
  },
  optionDescription: {
    fontSize: 13,
    fontFamily: 'MontserratRegular',
    lineHeight: 18,
  },
  comingSoonTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8,
  },
  comingSoonText: {
    fontSize: 10,
    fontFamily: 'MontserratBold',
    textTransform: 'uppercase',
  },
  button: {
    borderRadius: 8,
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderWidth: 2,
    marginHorizontal: 3,
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 85,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    marginRight: 4,
  },
});
