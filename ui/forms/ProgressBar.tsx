import React from 'react';
import {
  View,
} from 'react-native';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  currentStep,
  totalSteps,
}) => {
  const { colors } = useTheme();

  const progress = (currentStep / totalSteps) * 100;

  const styles = {
    container: {
      backgroundColor: colors.background,
      paddingHorizontal: 20,
      paddingVertical: 8, // Reduced from 16 to reduce space above and below
      alignItems: 'center' as const,
    },
    stepContainer: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    stepCircle: {
      width: 12,
      height: 12,
      borderRadius: 6,
      backgroundColor: '#FFFFFF', // White for not yet done/selected
      marginHorizontal: 4,
      borderWidth: 1,
      borderColor: colors.border,
    },
    stepCircleActive: {
      backgroundColor: colors.primary, // Use theme primary color
      borderColor: colors.primary,
    },
    stepConnector: {
      width: 40,
      height: 2,
      backgroundColor: '#FFFFFF', // White for not yet done
      marginHorizontal: 4,
    },
    stepConnectorActive: {
      backgroundColor: colors.primary, // Use theme primary color
    },
  };

  return (
    <View style={styles.container}>
      <View style={styles.stepContainer}>
        {Array.from({ length: totalSteps }, (_, index) => {
          const stepNumber = index + 1;
          const isActive = currentStep >= stepNumber;
          const isLast = index === totalSteps - 1;

          return (
            <React.Fragment key={stepNumber}>
              {/* Step Circle */}
              <View style={[
                styles.stepCircle,
                isActive && styles.stepCircleActive
              ]} />

              {/* Connector (don't show after last step) */}
              {!isLast && (
                <View style={[
                  styles.stepConnector,
                  currentStep > stepNumber && styles.stepConnectorActive
                ]} />
              )}
            </React.Fragment>
          );
        })}
      </View>
    </View>
  );
};
