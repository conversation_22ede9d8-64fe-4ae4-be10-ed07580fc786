import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { TransitionalProgramStatus as TransitionalStatus } from '@/lib/utils/individualProgramStatus';

interface TransitionalProgramStatusProps {
  transitionalStatus: TransitionalStatus;
  loading?: boolean;
}

export const TransitionalProgramStatusComponent: React.FC<TransitionalProgramStatusProps> = ({
  transitionalStatus,
  loading = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Loading program status...</Text>
      </View>
    );
  }

  // Only render if it's a transitional state
  if (!transitionalStatus.isTransitional) {
    return null;
  }

  const renderTransitionalContent = () => {
    // Only show if we need to display the waiting message
    if (!transitionalStatus.showWaitingMessage) {
      return null;
    }

    return (
      <View style={styles.transitionalContainer}>
        <View style={styles.iconContainer}>
          <MaterialIcons name="hourglass-empty" size={32} color={colors.textSecondary} />
        </View>
        <Text style={styles.title}>Program Ended</Text>
        <Text style={styles.message}>
          {transitionalStatus.transitionalMessage}
        </Text>
        <View style={styles.infoBox}>
          <MaterialIcons name="public" size={16} color={colors.textSecondary} />
          <Text style={styles.infoText}>
            Participants in different timezones may still be completing their programs.
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {renderTransitionalContent()}
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  transitionalContainer: {
    backgroundColor: colors.surface,
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  message: {
    fontSize: 16,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 20,
  },
  infoBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.background,
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    width: '100%',
  },
  infoText: {
    flex: 1,
    fontSize: 14,
    color: colors.textSecondary,
    marginLeft: 8,
    lineHeight: 18,
  },
  warningBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: colors.warning + '10',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    width: '100%',
    borderWidth: 1,
    borderColor: colors.warning + '30',
  },
  warningText: {
    flex: 1,
    fontSize: 14,
    color: colors.warning,
    marginLeft: 8,
    lineHeight: 18,
    fontWeight: '500',
  },
});

/**
 * Compact version for use in smaller UI areas
 */
export const TransitionalProgramStatusBanner: React.FC<TransitionalProgramStatusProps> = ({
  transitionalStatus,
  loading = false,
}) => {
  const { colors } = useTheme();
  const bannerStyles = createBannerStyles(colors);

  if (loading || !transitionalStatus.isTransitional) {
    return null;
  }

  // Only show banner if we need to display the waiting message
  if (!transitionalStatus.showWaitingMessage) {
    return null;
  }

  const config = {
    icon: 'hourglass-empty',
    color: colors.textSecondary,
    backgroundColor: colors.textSecondary + '10',
    message: 'Program ended - waiting for final results',
  };

  return (
    <View style={[bannerStyles.banner, { backgroundColor: config.backgroundColor }]}>
      <MaterialIcons name={config.icon as any} size={16} color={config.color} />
      <Text style={[bannerStyles.bannerText, { color: config.color }]}>
        {config.message}
      </Text>
    </View>
  );
};

const createBannerStyles = (colors: any) => StyleSheet.create({
  banner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderRadius: 6,
    marginVertical: 4,
  },
  bannerText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 6,
    flex: 1,
  },
});

export default TransitionalProgramStatusComponent;
