import React, { useState, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { ConfirmationModal } from '@/shared/components/modals';

interface GitHubAuthModalProps {
  visible: boolean;
  authUrl: string;
  onClose: () => void;
  onSuccess: (code: string) => void;
  onError: (error: string) => void;
}

export const GitHubAuthModal: React.FC<GitHubAuthModalProps> = ({
  visible,
  authUrl,
  onClose,
  onSuccess,
  onError,
}) => {
  const { colors } = useTheme();
  const [loading, setLoading] = useState(true);
  const [currentUrl, setCurrentUrl] = useState('');

  // Modal state for replacing Alert dialog
  const [confirmationModalVisible, setConfirmationModalVisible] = useState(false);

  const styles = createStyles(colors);

  useEffect(() => {
    if (visible) {
      setLoading(true);
      setCurrentUrl('');
    }
  }, [visible]);

  const handleNavigationStateChange = (navState: any) => {
    const { url } = navState;
    setCurrentUrl(url);



    // Check if this is our cloud function callback URL with code parameter
    if (url.includes('githuboauthcallback-5zdo6ysy2a-uc.a.run.app') && url.includes('code=')) {

      try {
        const urlObj = new URL(url);
        const code = urlObj.searchParams.get('code');
        const error = urlObj.searchParams.get('error');

        if (error) {
          console.error('❌ GitHub OAuth error:', error);
          onError(error);
          onClose();
        } else if (code) {
          onSuccess(code);
          onClose();
        } else {
          console.warn('⚠️ No code in callback URL');
          onError('No authorization code received');
          onClose();
        }
      } catch (urlError) {
        console.error('💥 Error parsing callback URL:', urlError);
        onError('Failed to parse callback URL');
        onClose();
      }
      return;
    }

    // Check if this is our mobile redirect URI (fallback)
    if (url.includes('accustom://github-auth')) {
      try {
        const urlObj = new URL(url);
        const code = urlObj.searchParams.get('code');
        const error = urlObj.searchParams.get('error');

        if (error) {
          console.error('❌ GitHub OAuth error:', error);
          onError(error);
          onClose();
        } else if (code) {
          onSuccess(code);
          onClose();
        } else {
          console.warn('⚠️ No code or error in callback URL');
          onError('No authorization code received');
          onClose();
        }
      } catch (urlError) {
        console.error('💥 Error parsing callback URL:', urlError);
        onError('Failed to parse callback URL');
        onClose();
      }
    }
  };

  const handleLoadStart = () => {
    setLoading(true);
  };

  const handleLoadEnd = () => {
    setLoading(false);
  };

  const handleError = (syntheticEvent: any) => {
    const { nativeEvent } = syntheticEvent;
    console.error('💥 WebView error:', nativeEvent);
    setLoading(false);
    onError('Failed to load GitHub authentication page');
  };

  const handleMessage = (event: any) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);


      if (data.type === 'GITHUB_AUTH_SUCCESS') {
        const { state, code, user } = data;

        // Pass the authorization code for direct token exchange
        onSuccess(code || state); // Use code if available, fallback to state
        onClose();
      } else if (data.type === 'GITHUB_AUTH_ERROR') {
        console.error('❌ GitHub OAuth error message received:', data.error);
        onError(data.error || 'Authentication failed');
        onClose();
      }
    } catch (error) {
      console.warn('⚠️ Failed to parse WebView message:', event.nativeEvent.data);
    }
  };

  const handleClose = () => {
    setConfirmationModalVisible(true);
  };

  const handleConfirmCancel = () => {
    setConfirmationModalVisible(false);
    onError('Authentication cancelled by user');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={handleClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={handleClose}
          >
            <MaterialIcons name="close" size={24} color={colors.text} />
          </TouchableOpacity>
          <View style={styles.titleContainer}>
            <Text style={styles.title}>Connect GitHub</Text>
            <Text style={styles.subtitle}>Authenticate with your GitHub account</Text>
          </View>
          <View style={styles.placeholder} />
        </View>

        {/* Loading Indicator */}
        {loading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.primary} />
            <Text style={styles.loadingText}>Loading GitHub...</Text>
          </View>
        )}

        {/* WebView */}
        <WebView
          source={{ uri: authUrl }}
          onNavigationStateChange={handleNavigationStateChange}
          onLoadStart={handleLoadStart}
          onLoadEnd={handleLoadEnd}
          onError={handleError}
          onMessage={handleMessage}
          style={styles.webview}
          startInLoadingState={true}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          sharedCookiesEnabled={true}
          userAgent="Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1"
        />

        {/* URL Display (for debugging) */}
        {__DEV__ && currentUrl && (
          <View style={styles.debugContainer}>
            <Text style={styles.debugText} numberOfLines={1}>
              {currentUrl}
            </Text>
          </View>
        )}
      </SafeAreaView>

      {/* Confirmation Modal */}
      <ConfirmationModal
        visible={confirmationModalVisible}
        onClose={() => setConfirmationModalVisible(false)}
        onConfirm={handleConfirmCancel}
        title="Cancel Authentication"
        message="Are you sure you want to cancel GitHub authentication?"
        confirmText="Cancel"
        cancelText="Continue"
        confirmButtonStyle="danger"
      />
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  closeButton: {
    padding: 8,
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.text,
  },
  subtitle: {
    fontSize: 14,
    color: colors.textMuted,
    marginTop: 2,
  },
  placeholder: {
    width: 40,
  },
  loadingContainer: {
    position: 'absolute',
    top: '50%',
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1,
    backgroundColor: colors.background,
    paddingVertical: 20,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: colors.text,
  },
  webview: {
    flex: 1,
  },
  debugContainer: {
    backgroundColor: colors.card,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  debugText: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: 'monospace',
  },
});

export default GitHubAuthModal;
