import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { 
  getGitHubIntegrationStatus, 
  getAllActiveIntegrations, 
  disconnectIntegration,
  checkIntegrationsHealth,
  IntegrationStatus 
} from '@/lib/utils/integrationHelpers';
import { Integration } from '@/lib/services/database/types';
import { githubService } from '@/lib/services/githubService';

interface IntegrationStatusProps {
  onIntegrationChange?: () => void;
}

export const IntegrationStatusComponent: React.FC<IntegrationStatusProps> = ({ 
  onIntegrationChange 
}) => {
  const [githubStatus, setGithubStatus] = useState<IntegrationStatus>({ isConnected: false });
  const [allIntegrations, setAllIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState(false);

  useEffect(() => {
    loadIntegrationData();
  }, []);

  const loadIntegrationData = async () => {
    try {
      setLoading(true);
      
      // Load GitHub status
      const githubResult = await getGitHubIntegrationStatus();
      setGithubStatus(githubResult);
      
      // Load all active integrations
      const allResult = await getAllActiveIntegrations();
      setAllIntegrations(allResult);
      
      // Check integrations health
      const healthCheck = await checkIntegrationsHealth();
      if (healthCheck.errors.length > 0) {
        console.warn('Integration health issues:', healthCheck.errors);
      }
      
    } catch (error) {
      console.error('Error loading integration data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConnectGitHub = async () => {
    try {
      setConnecting(true);
      
      const result = await githubService.authenticate();
      
      if (result.success) {
        Alert.alert(
          'Success',
          'GitHub integration connected successfully!',
          [{ text: 'OK', onPress: () => {
            loadIntegrationData();
            onIntegrationChange?.();
          }}]
        );
      } else {
        Alert.alert(
          'Connection Failed',
          result.error || 'Failed to connect GitHub integration'
        );
      }
    } catch (error) {
      console.error('Error connecting GitHub:', error);
      Alert.alert('Error', 'An unexpected error occurred');
    } finally {
      setConnecting(false);
    }
  };

  const handleDisconnectGitHub = async () => {
    Alert.alert(
      'Disconnect GitHub',
      'Are you sure you want to disconnect your GitHub integration?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Disconnect',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await disconnectIntegration('github');
              
              if (result.success) {
                // Also sign out from GitHub service
                await githubService.signOut();
                
                Alert.alert(
                  'Disconnected',
                  'GitHub integration has been disconnected',
                  [{ text: 'OK', onPress: () => {
                    loadIntegrationData();
                    onIntegrationChange?.();
                  }}]
                );
              } else {
                Alert.alert('Error', result.error || 'Failed to disconnect GitHub integration');
              }
            } catch (error) {
              console.error('Error disconnecting GitHub:', error);
              Alert.alert('Error', 'An unexpected error occurred');
            }
          }
        }
      ]
    );
  };

  const formatLastSync = (lastSyncAt?: string) => {
    if (!lastSyncAt) return 'Never';
    
    const syncDate = new Date(lastSyncAt);
    const now = new Date();
    const diffMs = now.getTime() - syncDate.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading integrations...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Connected Integrations</Text>
      
      {/* GitHub Integration */}
      <View style={styles.integrationCard}>
        <View style={styles.integrationHeader}>
          <MaterialIcons 
            name="code" 
            size={24} 
            color={githubStatus.isConnected ? '#28a745' : '#6c757d'} 
          />
          <Text style={styles.integrationName}>GitHub</Text>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: githubStatus.isConnected ? '#28a745' : '#6c757d' }
          ]}>
            <Text style={styles.statusText}>
              {githubStatus.isConnected ? 'Connected' : 'Not Connected'}
            </Text>
          </View>
        </View>
        
        {githubStatus.isConnected && githubStatus.integration && (
          <View style={styles.integrationDetails}>
            <Text style={styles.detailText}>
              User: {githubStatus.integration.user.login || githubStatus.integration.user.name}
            </Text>
            <Text style={styles.detailText}>
              Last sync: {formatLastSync(githubStatus.lastSyncAt)}
            </Text>
          </View>
        )}
        
        <TouchableOpacity
          style={[
            styles.actionButton,
            githubStatus.isConnected ? styles.disconnectButton : styles.connectButton
          ]}
          onPress={githubStatus.isConnected ? handleDisconnectGitHub : handleConnectGitHub}
          disabled={connecting}
        >
          <Text style={styles.actionButtonText}>
            {connecting ? 'Connecting...' : (githubStatus.isConnected ? 'Disconnect' : 'Connect')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Summary */}
      <View style={styles.summary}>
        <Text style={styles.summaryText}>
          {allIntegrations.length} integration{allIntegrations.length !== 1 ? 's' : ''} connected
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  loadingText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 16,
  },
  integrationCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  integrationName: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
    color: '#333',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  integrationDetails: {
    marginBottom: 12,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  actionButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 6,
    alignItems: 'center',
  },
  connectButton: {
    backgroundColor: '#007bff',
  },
  disconnectButton: {
    backgroundColor: '#dc3545',
  },
  actionButtonText: {
    color: 'white',
    fontWeight: '500',
  },
  summary: {
    marginTop: 16,
    padding: 12,
    backgroundColor: '#e9ecef',
    borderRadius: 6,
  },
  summaryText: {
    textAlign: 'center',
    color: '#666',
    fontSize: 14,
  },
});

export default IntegrationStatusComponent;
