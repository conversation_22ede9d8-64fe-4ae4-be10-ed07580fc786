# Accustom

Accustom is a habit‑based betting platform built with React Native and Expo. Users join or create programs where they stake an amount on completing daily habits. The app tracks progress, awards points, and lets participants challenge disputed results. It integrates with Firebase for authentication, data storage and push notifications.

## Major features

- Explore and join betting programs
- Track progress with streaks, points and leaderboards
- View personal programs and program details
- In‑app and push notifications
- Dispute management and support pages
- User profile and email settings

## Development

1. Install dependencies:
   ```bash
   npm install
   ```
2. Start the development server:
   ```bash
   npx expo start
   ```
   This launches the Expo CLI where you can run the app on Android, iOS or the web.

### Tests and linting

Run `npm test` to execute Jest tests. You can also run `npm run lint` to check code style.

## Firebase configuration

The app uses Firebase for authentication, Firestore, and storage. Create a `.env` file in the project root and provide your Firebase credentials:

```bash
FIREBASE_API_KEY=your_key
FIREBASE_AUTH_DOMAIN=your_domain
FIREBASE_DATABASE_URL=your_db_url
FIREBASE_PROJECT_ID=your_project
FIREBASE_STORAGE_BUCKET=your_bucket
FIREBASE_MESSAGING_SENDER_ID=your_sender_id
FIREBASE_APP_ID=your_app_id
FIREBASE_MEASUREMENT_ID=your_measurement_id
```

Update `config/firebase.ts` to read these variables (e.g. via `process.env`).
