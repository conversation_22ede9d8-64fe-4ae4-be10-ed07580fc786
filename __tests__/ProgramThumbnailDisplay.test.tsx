import React from 'react';
import { render } from '@testing-library/react-native';
import { Image, View, Text } from 'react-native';

// Mock the program card component for testing thumbnail display
const MockProgramCardWithThumbnail = ({ program }: { program: any }) => {
  return (
    <View>
      {/* Program Image Thumbnail */}
      {program.image?.url && (
        <Image
          source={{ uri: program.image.url }}
          style={{ width: '100%', height: 120 }}
          resizeMode="cover"
          testID="program-thumbnail"
        />
      )}
      
      {/* Program Title */}
      <Text testID="program-title">{program.name}</Text>
      
      {/* Category Icon (always shown) */}
      <Text testID="category-icon">Category Icon</Text>
    </View>
  );
};

// Mock the program details banner component
const MockProgramDetailsBanner = ({ program }: { program: any }) => {
  return (
    <View>
      {/* Program Image Banner */}
      {program.image?.url && (
        <Image
          source={{ uri: program.image.url }}
          style={{ width: '100%', height: 200 }}
          resizeMode="cover"
          testID="program-banner"
        />
      )}
      
      {/* Program Title */}
      <Text testID="program-title">{program.name}</Text>
    </View>
  );
};

describe('Program Thumbnail Display', () => {
  const programWithImage = {
    id: '1',
    name: 'Test Program',
    image: {
      url: 'https://firebasestorage.googleapis.com/v0/b/betonself.appspot.com/o/program-images%2F1751340202101_ChatGPT_Image_Jul_1__2025__12_11_53_PM.png?alt=media&token=3f9f4cc1-6c5b-473a-a631-1b5a7cbc5aec',
      alt: 'ChatGPT Image Jul 1, 2025, 12 11 53 PM'
    }
  };

  const programWithoutImage = {
    id: '1',
    name: 'Test Program',
    category: 'fitness'
  };

  describe('Explore Page Card', () => {
    it('should display thumbnail above program title when image is provided', () => {
      const { getByTestId } = render(
        <MockProgramCardWithThumbnail program={programWithImage} />
      );

      // Should display the thumbnail
      expect(getByTestId('program-thumbnail')).toBeTruthy();
      // Should display the program title
      expect(getByTestId('program-title')).toBeTruthy();
      // Should still display category icon
      expect(getByTestId('category-icon')).toBeTruthy();
    });

    it('should not display thumbnail when no image is provided', () => {
      const { queryByTestId, getByTestId } = render(
        <MockProgramCardWithThumbnail program={programWithoutImage} />
      );

      // Should not display the thumbnail
      expect(queryByTestId('program-thumbnail')).toBeNull();
      // Should display the program title
      expect(getByTestId('program-title')).toBeTruthy();
      // Should display category icon
      expect(getByTestId('category-icon')).toBeTruthy();
    });
  });

  describe('Program Details Page', () => {
    it('should display banner above program title when image is provided', () => {
      const { getByTestId } = render(
        <MockProgramDetailsBanner program={programWithImage} />
      );

      // Should display the banner
      expect(getByTestId('program-banner')).toBeTruthy();
      // Should display the program title
      expect(getByTestId('program-title')).toBeTruthy();
    });

    it('should not display banner when no image is provided', () => {
      const { queryByTestId, getByTestId } = render(
        <MockProgramDetailsBanner program={programWithoutImage} />
      );

      // Should not display the banner
      expect(queryByTestId('program-banner')).toBeNull();
      // Should display the program title
      expect(getByTestId('program-title')).toBeTruthy();
    });
  });
});
