import { useState, useEffect, useCallback } from 'react';
import { subscriptionService, SubscriptionStatus } from '../services/subscriptionService';

export const useSubscription = () => {
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus>(() => {
    // Try to get cached status immediately (synchronous)
    const cached = subscriptionService.getCachedStatus();
    return cached || { isPremium: false };
  });
  const [loading, setLoading] = useState(() => {
    // If we have cached data, don't show loading
    const cached = subscriptionService.getCachedStatus();
    return !cached;
  });

  useEffect(() => {
    let mounted = true;

    const checkSubscription = async () => {
      try {
        // Only show loading if we don't have cached data
        const cached = subscriptionService.getCachedStatus();
        if (!cached) {
          setLoading(true);
        }

        const status = await subscriptionService.checkUserPremiumStatus();
        if (mounted) {
          setSubscriptionStatus(status);
          setLoading(false);
        }
      } catch (error) {
        console.error('Error checking subscription:', error);
        if (mounted) {
          setSubscriptionStatus({ isPremium: false });
          setLoading(false);
        }
      }
    };

    // Subscribe to subscription changes
    const unsubscribe = subscriptionService.subscribe((status) => {
      if (mounted) {
        setSubscriptionStatus(status);
        setLoading(false);
      }
    });

    checkSubscription();

    return () => {
      mounted = false;
      unsubscribe();
    };
  }, []);

  const refreshSubscriptionStatus = useCallback(async () => {
    try {
      setLoading(true);
      const status = await subscriptionService.checkUserPremiumStatus(true); // Force refresh
      setSubscriptionStatus(status);
    } catch (error) {
      console.error('Error refreshing subscription:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    ...subscriptionStatus,
    loading,
    refreshSubscriptionStatus,
  };
};
