/**
 * React hooks for individual program status management
 */

import { useState, useEffect, useCallback } from 'react';
import { Participant } from '../services/database/types';
import { individualProgramStatusService } from '../services/individualProgramStatusService';
import {
  calculateUserProgramStatus,
  getTimeUntilDeadline,
  formatTimeRemaining,
  UserProgramStatus,
} from '../utils/individualProgramStatus';

/**
 * Hook to get and manage individual user program status
 */
export const useIndividualProgramStatus = (
  programId: string,
  userId: string,
  participant?: Participant
) => {
  const [statusInfo, setStatusInfo] = useState<UserProgramStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const updateStatus = useCallback(async () => {
    if (!programId || !userId) return;

    try {
      setLoading(true);
      setError(null);

      // If we have participant data, calculate status directly
      if (participant) {
        const status = calculateUserProgramStatus(participant);
        setStatusInfo(status);
        setLoading(false);
        return;
      }

      // Otherwise, fetch from service
      const result = await individualProgramStatusService.getParticipantProgramStatus(
        programId,
        userId
      );

      if (result.success && result.statusInfo) {
        setStatusInfo(result.statusInfo);
      } else {
        setError(result.error || 'Failed to get program status');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, [programId, userId, participant]);

  useEffect(() => {
    updateStatus();
  }, [updateStatus]);

  // Auto-refresh every minute to keep status current
  useEffect(() => {
    const interval = setInterval(updateStatus, 60000);
    return () => clearInterval(interval);
  }, [updateStatus]);

  return {
    statusInfo,
    loading,
    error,
    refresh: updateStatus,
  };
};

/**
 * Hook to get submission deadline countdown
 */
export const useSubmissionDeadline = (
  participant: Participant | null,
  dayNumber: number
) => {
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [formattedTime, setFormattedTime] = useState<string>('');
  const [deadlinePassed, setDeadlinePassed] = useState(false);

  useEffect(() => {
    if (!participant) return;

    const updateCountdown = () => {
      const remaining = getTimeUntilDeadline(participant, dayNumber);
      setTimeRemaining(remaining);
      setFormattedTime(formatTimeRemaining(remaining));
      setDeadlinePassed(remaining <= 0);
    };

    // Update immediately
    updateCountdown();

    // Update every second for countdown
    const interval = setInterval(updateCountdown, 1000);
    return () => clearInterval(interval);
  }, [participant, dayNumber]);

  return {
    timeRemaining,
    formattedTime,
    deadlinePassed,
  };
};

/**
 * Hook to manage daily checkups
 */
export const useDailyCheckup = (programId: string, userId: string) => {
  const [isRunning, setIsRunning] = useState(false);
  const [lastCheckup, setLastCheckup] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);

  const runCheckup = useCallback(async () => {
    if (!programId || !userId || isRunning) return;

    try {
      setIsRunning(true);
      setError(null);

      const result = await individualProgramStatusService.runDailyCheckup(
        programId,
        userId
      );

      if (result.success) {
        setLastCheckup(new Date());
      } else {
        setError(result.error || 'Failed to run daily checkup');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsRunning(false);
    }
  }, [programId, userId, isRunning]);

  return {
    runCheckup,
    isRunning,
    lastCheckup,
    error,
  };
};

/**
 * Hook to get program day information
 */
export const useProgramDay = (participant: Participant | null) => {
  const [dayInfo, setDayInfo] = useState<{
    currentDay: number;
    status: 'upcoming' | 'ongoing' | 'ended';
    message: string;
  } | null>(null);

  useEffect(() => {
    if (!participant) {
      setDayInfo(null);
      return;
    }

    const updateDayInfo = () => {
      const statusInfo = calculateUserProgramStatus(participant);
      setDayInfo({
        currentDay: statusInfo.currentDay,
        status: statusInfo.status,
        message: statusInfo.message,
      });
    };

    // Update immediately
    updateDayInfo();

    // Update every minute
    const interval = setInterval(updateDayInfo, 60000);
    return () => clearInterval(interval);
  }, [participant]);

  return dayInfo;
};

/**
 * Hook to check if program is active for user
 */
export const useIsProgramActive = (participant: Participant | null) => {
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    if (!participant) {
      setIsActive(false);
      return;
    }

    const checkActive = () => {
      const statusInfo = calculateUserProgramStatus(participant);
      setIsActive(statusInfo.status === 'ongoing' && !participant.disqualified);
    };

    // Check immediately
    checkActive();

    // Check every minute
    const interval = setInterval(checkActive, 60000);
    return () => clearInterval(interval);
  }, [participant]);

  return isActive;
};

/**
 * Hook to get program timeline information
 */
export const useProgramTimeline = (participant: Participant | null) => {
  const [timeline, setTimeline] = useState<{
    startDate: Date;
    endDate: Date;
    totalDays: number;
    daysCompleted: number;
    daysRemaining: number;
    progressPercentage: number;
  } | null>(null);

  useEffect(() => {
    if (!participant) {
      setTimeline(null);
      return;
    }

    const updateTimeline = () => {
      const startDate = new Date(participant.personalStartDate);
      const endDate = new Date(participant.personalEndDate);
      const statusInfo = calculateUserProgramStatus(participant);
      
      const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const daysCompleted = Math.max(0, statusInfo.currentDay - 1);
      const daysRemaining = Math.max(0, totalDays - daysCompleted);
      const progressPercentage = totalDays > 0 ? (daysCompleted / totalDays) * 100 : 0;

      setTimeline({
        startDate,
        endDate,
        totalDays,
        daysCompleted,
        daysRemaining,
        progressPercentage,
      });
    };

    // Update immediately
    updateTimeline();

    // Update every hour
    const interval = setInterval(updateTimeline, 60 * 60 * 1000);
    return () => clearInterval(interval);
  }, [participant]);

  return timeline;
};
