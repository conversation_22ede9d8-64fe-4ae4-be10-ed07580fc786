import React, { useMemo } from 'react';
import { subscriptionService } from '../services/subscriptionService';

/**
 * Ultra-fast subscription hook that returns cached data immediately
 * Use this for UI elements that need instant premium status checks
 */
export const useFastSubscription = () => {
  const subscriptionStatus = useMemo(() => {
    // Get cached status immediately (no async, no loading)
    const cached = subscriptionService.getCachedStatus();
    return cached || { isPremium: false };
  }, []);

  return {
    isPremium: subscriptionStatus.isPremium,
    subscriptionStatus: subscriptionStatus.subscriptionStatus,
    // Fast check function for inline use
    checkPremium: () => {
      const cached = subscriptionService.getCachedStatus();
      return cached?.isPremium || false;
    },
  };
};

/**
 * Standalone function for immediate premium checks
 * Use this in event handlers and conditions
 */
export const isPremiumUser = (): boolean => {
  const cached = subscriptionService.getCachedStatus();
  return cached?.isPremium || false;
};

/**
 * Higher-order component for premium feature gating
 */
export const withPremiumCheck = <T extends object>(
  Component: React.ComponentType<T>,
  fallback?: React.ComponentType<T>
) => {
  return (props: T) => {
    const isPremium = isPremiumUser();

    if (!isPremium && fallback) {
      const FallbackComponent = fallback;
      return React.createElement(FallbackComponent, props);
    }

    if (!isPremium) {
      return null;
    }

    return React.createElement(Component, props);
  };
};
