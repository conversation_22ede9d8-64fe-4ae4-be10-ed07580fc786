/**
 * Program Scheduling Service
 * Handles timezone-aware program scheduling operations
 */

import { getFunctions, httpsCallable } from 'firebase/functions';

export interface ProgramSchedulingResult {
  success: boolean;
  message: string;
  programId: string;
  startTime?: string;
  endDate?: string;
  participantCount?: number;
}



/**
 * Service class for managing program scheduling
 */
export class ProgramSchedulingService {
  private functions = getFunctions();

  /**
   * Set up initial program start scheduler
   * Call this when a program is created
   */
  async setupProgramStartScheduler(programId: string): Promise<ProgramSchedulingResult> {
    try {
      const setupScheduler = httpsCallable(this.functions, 'setupProgramStartScheduler');
      const result = await setupScheduler({ programId });
      
      return {
        success: true,
        message: 'Program start scheduler set up successfully',
        programId,
        ...(result.data as any)
      };
    } catch (error) {
      console.error('Error setting up program start scheduler:', error);
      return {
        success: false,
        message: `Failed to set up program start scheduler: ${error}`,
        programId
      };
    }
  }





  /**
   * Batch update scheduling for multiple programs
   * Useful for migration or bulk operations
   */
  async batchUpdateScheduling(programIds: string[]): Promise<ProgramSchedulingResult[]> {
    const results: ProgramSchedulingResult[] = [];
    
    for (const programId of programIds) {
      try {
        const result = await this.setupProgramStartScheduler(programId);
        results.push(result);
        
        // Add small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        results.push({
          success: false,
          message: `Failed to update scheduling for ${programId}: ${error}`,
          programId
        });
      }
    }
    
    return results;
  }


}

// Export singleton instance
export const programSchedulingService = new ProgramSchedulingService();

/**
 * Utility functions for direct use
 */

/**
 * Set up program scheduling when a program is created
 */
export const setupProgramScheduling = async (programId: string): Promise<boolean> => {
  try {
    const result = await programSchedulingService.setupProgramStartScheduler(programId);
    return result.success;
  } catch (error) {
    console.error('Error setting up program scheduling:', error);
    return false;
  }
};



/**
 * Fix program scheduling if it's incorrect
 */
export const fixProgramScheduling = async (programId: string): Promise<boolean> => {
  try {
    // Try to fix by setting up the scheduler
    const result = await programSchedulingService.setupProgramStartScheduler(programId);
    return result.success;
  } catch (error) {
    console.error('Error fixing program scheduling:', error);
    return false;
  }
};


