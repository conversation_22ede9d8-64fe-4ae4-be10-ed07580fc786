// Alert import removed - using console warnings instead
import { STRIPE_CONFIG, API_CONFIG, validateStripeConfig, validateAPIConfig } from '@/lib/config/payments';
import { mockCreatePaymentIntent, mockConfirmPayment, logPaymentEvent } from '../utils/mockPaymentAPI';

// Payment service interface
export interface PaymentResult {
  success: boolean;
  paymentIntentId?: string;
  error?: string;
}

export interface PaymentData {
  amount: number; // Amount in cents
  currency: string;
  description: string;
  programId: string;
  userId: string;
}

// Mobile-specific Stripe implementation
const initializeStripeMobile = async () => {
  // Validate configuration before initializing
  if (!validateStripeConfig()) {
    throw new Error('Stripe configuration is invalid. Please check your publishable key.');
  }

  // Mobile implementation using @stripe/stripe-react-native
  const { initStripe } = await import('@stripe/stripe-react-native');
  await initStripe({
    publishableKey: STRIPE_CONFIG.PUBLISHABLE_KEY,
    merchantIdentifier: STRIPE_CONFIG.MERCHANT_IDENTIFIER,
  });
  return true;
};

// Create payment intent (shared between platforms)
const createPaymentIntent = async (paymentData: PaymentData) => {
  // Validate API configuration
  if (!validateAPIConfig()) {
    throw new Error('API configuration is invalid. Please check your backend URL.');
  }

  // In development mode, use mock API
  if (__DEV__) {
    logPaymentEvent('Using mock payment intent creation', { amount: paymentData.amount });
    return await mockCreatePaymentIntent(
      paymentData.amount,
      paymentData.currency,
      paymentData.description,
      { programId: paymentData.programId, userId: paymentData.userId }
    );
  }

  // Real API call for production
  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CREATE_PAYMENT_INTENT}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(paymentData),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  logPaymentEvent('Payment intent created successfully', { paymentIntentId: data.id });

  return {
    clientSecret: data.client_secret,
    paymentIntentId: data.id,
  };
};

// Process Stripe payment for mobile
const processStripePaymentMobile = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    logPaymentEvent('Starting mobile Stripe payment', { amount: paymentData.amount });

    await initializeStripeMobile();

    // Create payment intent
    const paymentIntentResult = await createPaymentIntent(paymentData);
    // Use type assertion to handle the union type
    const clientSecret = (paymentIntentResult as any).clientSecret;
    const paymentIntentId = (paymentIntentResult as any).paymentIntentId;

    // In development mode with mock API, simulate payment success
    if (__DEV__ && clientSecret.includes('mock')) {
      logPaymentEvent('Using mock payment confirmation for mobile', { paymentIntentId });

      const mockResult = await mockConfirmPayment(paymentIntentId, 'card');

      if (mockResult.status === 'succeeded') {
        return {
          success: true,
          paymentIntentId: mockResult.id,
        };
      } else {
        return {
          success: false,
          error: 'Mock payment failed for testing',
        };
      }
    }

    // Real Stripe payment processing - only import on mobile
    const { confirmPayment } = await import('@stripe/stripe-react-native');

    // Confirm payment
    const { error, paymentIntent } = await confirmPayment(clientSecret, {
      paymentMethodType: 'Card',
    });

    if (error) {
      logPaymentEvent('Mobile payment failed', error);
      return {
        success: false,
        error: error.message,
      };
    }

    if (paymentIntent?.status === 'Succeeded') {
      logPaymentEvent('Mobile payment succeeded', { paymentIntentId: paymentIntent.id });
      return {
        success: true,
        paymentIntentId: paymentIntent.id,
      };
    }

    return {
      success: false,
      error: 'Payment was not completed successfully',
    };
  } catch (error) {
    logPaymentEvent('Mobile payment error', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Main payment processing function for mobile
export const processStripePayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  return processStripePaymentMobile(paymentData);
};

// Google Pay implementation (mobile only)
export const processGooglePayPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    await initializeStripeMobile();

    // Import Google Pay functions - only on mobile
    const stripeModule = await import('@stripe/stripe-react-native') as any;
    const { isGooglePaySupported, initGooglePay, presentGooglePay } = stripeModule;

    // Check if Google Pay is supported
    const isSupported = await isGooglePaySupported({ testEnv: true });
    if (!isSupported) {
      return {
        success: false,
        error: 'Google Pay is not supported on this device',
      };
    }

    // Initialize Google Pay
    const { error: initError } = await initGooglePay({
      testEnv: STRIPE_CONFIG.TEST_ENV,
      merchantName: 'Accustom',
      countryCode: 'US',
    });

    if (initError) {
      return {
        success: false,
        error: initError.message,
      };
    }

    // Create payment intent
    const paymentIntentResult2 = await createPaymentIntent(paymentData);
    const clientSecret = (paymentIntentResult2 as any).clientSecret;
    const paymentIntentId = (paymentIntentResult2 as any).paymentIntentId;

    // Present Google Pay
    const { error } = await presentGooglePay({
      clientSecret,
      forSetupIntent: false,
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      paymentIntentId,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};

// Apple Pay implementation (iOS only)
export const processApplePayPayment = async (paymentData: PaymentData): Promise<PaymentResult> => {
  try {
    await initializeStripeMobile();

    // Import Apple Pay functions - only on mobile
    const stripeModule2 = await import('@stripe/stripe-react-native') as any;
    const { isApplePaySupported, presentApplePay } = stripeModule2;

    // Check if Apple Pay is supported
    const isSupported = await isApplePaySupported();
    if (!isSupported) {
      return {
        success: false,
        error: 'Apple Pay is not supported on this device',
      };
    }

    // Create payment intent
    const paymentIntentResult3 = await createPaymentIntent(paymentData);
    const clientSecret = (paymentIntentResult3 as any).clientSecret;
    const paymentIntentId = (paymentIntentResult3 as any).paymentIntentId;

    // Present Apple Pay
    const { error } = await presentApplePay({
      clientSecret,
      applePay: {
        merchantCountryCode: 'US',
        currencyCode: paymentData.currency.toUpperCase(),
        cartItems: [
          {
            label: paymentData.description,
            amount: (paymentData.amount / 100).toString(),
            paymentType: 'Immediate',
          },
        ],
        merchantDisplayName: 'Accustom',
      },
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    return {
      success: true,
      paymentIntentId,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
};
