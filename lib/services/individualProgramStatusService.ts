/**
 * Individual Program Status Service
 * 
 * This service manages individual user program status updates and daily checkups
 * without relying on centralized program status coordination.
 */

import { ParticipantService } from './database/ParticipantService';
import { SubmissionService } from './database/SubmissionService';
import { Participant, Submission } from './database/types';
import {
  calculateUserProgramStatus,
  updateParticipantProgramStatus,
  shouldRunDailyCheckup,
  isSubmissionDeadlinePassed,
  UserProgramStatus,
} from '../utils/individualProgramStatus';

export class IndividualProgramStatusService {
  private participantService = new ParticipantService();
  private submissionService = new SubmissionService();

  /**
   * Update a participant's program status based on current time
   */
  async updateParticipantStatus(
    programId: string,
    userId: string,
    currentTime: Date = new Date()
  ): Promise<{ success: boolean; statusInfo?: UserProgramStatus; error?: string }> {
    try {
      // Get current participant data
      const participantResult = await this.participantService.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return { success: false, error: 'Participant not found' };
      }

      const participant = participantResult.data;
      
      // Calculate new status
      const statusInfo = calculateUserProgramStatus(participant, currentTime);
      const updateData = updateParticipantProgramStatus(participant, currentTime);

      // Update participant if status changed
      if (updateData.personalProgramStatus !== participant.personalProgramStatus ||
          updateData.personalCurrentDay !== participant.personalCurrentDay) {
        
        const updateResult = await this.participantService.updateParticipant(
          programId,
          userId,
          updateData
        );

        if (!updateResult.success) {
          return { success: false, error: 'Failed to update participant status' };
        }
      }

      return { success: true, statusInfo };
    } catch (error) {
      console.error('Error updating participant status:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Run daily checkup for a participant
   */
  async runDailyCheckup(
    programId: string,
    userId: string,
    currentTime: Date = new Date()
  ): Promise<{ success: boolean; action?: string; error?: string }> {
    try {
      // Get current participant data
      const participantResult = await this.participantService.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return { success: false, error: 'Participant not found' };
      }

      const participant = participantResult.data;

      // Check if daily checkup is needed
      if (!shouldRunDailyCheckup(participant, currentTime)) {
        return { success: true, action: 'no_checkup_needed' };
      }

      // Update participant status first
      const statusUpdate = await this.updateParticipantStatus(programId, userId, currentTime);
      if (!statusUpdate.success || !statusUpdate.statusInfo) {
        return { success: false, error: 'Failed to update participant status' };
      }

      const statusInfo = statusUpdate.statusInfo;

      // Only process checkups for ongoing programs
      if (statusInfo.status !== 'ongoing') {
        return { success: true, action: 'program_not_ongoing' };
      }

      // Check previous day's submission (current day - 1)
      const previousDay = statusInfo.currentDay - 1;
      if (previousDay < 1) {
        return { success: true, action: 'no_previous_day' };
      }

      // Get previous day's submission
      const submissionResult = await this.submissionService.getSubmission(
        programId,
        userId,
        `Day ${previousDay}`
      );

      let submissionStatus: Submission['status'] = 'upcoming';
      if (submissionResult.success && submissionResult.data) {
        submissionStatus = submissionResult.data.status;
      }

      // Process based on submission status
      if (submissionStatus === 'upcoming') {
        // User didn't submit - check if they have lives left
        if (participant.livesLeft > 0) {
          // Deduct a life and mark as bailed
          const newLives = participant.livesLeft - 1;
          const shouldDisqualify = newLives <= 0;

          // Update participant
          const updateData: Partial<Participant> = { livesLeft: newLives };
          if (shouldDisqualify) {
            updateData.disqualified = true;
            updateData.disqualifyReason = `Missed submission on day ${previousDay} - No lives left`;
          }

          await this.participantService.updateParticipant(programId, userId, updateData);

          // Update submission status
          await this.submissionService.updateSubmissionStatus(
            programId,
            userId,
            `Day ${previousDay}`,
            shouldDisqualify ? 'not_submitted' : 'bailed',
            shouldDisqualify ? 'Disqualified - No lives left' : 'Bailed - Life deducted'
          );

          return {
            success: true,
            action: shouldDisqualify ? 'disqualified' : 'bailed_life_deducted'
          };
        } else {
          // No lives left - disqualify
          await this.participantService.updateParticipant(programId, userId, {
            disqualified: true,
            disqualifyReason: `Missed submission on day ${previousDay} - No lives left`
          });

          await this.submissionService.updateSubmissionStatus(
            programId,
            userId,
            `Day ${previousDay}`,
            'not_submitted',
            'Disqualified - No lives left'
          );

          return { success: true, action: 'disqualified' };
        }
      }

      return { success: true, action: 'already_processed' };
    } catch (error) {
      console.error('Error running daily checkup:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Get current program status for a participant
   */
  async getParticipantProgramStatus(
    programId: string,
    userId: string,
    currentTime: Date = new Date()
  ): Promise<{ success: boolean; statusInfo?: UserProgramStatus; error?: string }> {
    try {
      const participantResult = await this.participantService.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return { success: false, error: 'Participant not found' };
      }

      const statusInfo = calculateUserProgramStatus(participantResult.data, currentTime);
      return { success: true, statusInfo };
    } catch (error) {
      console.error('Error getting participant program status:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Check if submission deadline has passed for current day
   */
  async checkSubmissionDeadline(
    programId: string,
    userId: string,
    dayNumber: number,
    currentTime: Date = new Date()
  ): Promise<{ success: boolean; deadlinePassed?: boolean; error?: string }> {
    try {
      const participantResult = await this.participantService.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return { success: false, error: 'Participant not found' };
      }

      const deadlinePassed = isSubmissionDeadlinePassed(
        participantResult.data,
        dayNumber,
        currentTime
      );

      return { success: true, deadlinePassed };
    } catch (error) {
      console.error('Error checking submission deadline:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  /**
   * Batch update status for multiple participants
   */
  async batchUpdateParticipantStatus(
    programId: string,
    userIds: string[],
    currentTime: Date = new Date()
  ): Promise<{ success: boolean; results?: Array<{ userId: string; success: boolean; error?: string }>; error?: string }> {
    try {
      const results = [];

      for (const userId of userIds) {
        const result = await this.updateParticipantStatus(programId, userId, currentTime);
        results.push({
          userId,
          success: result.success,
          error: result.error
        });

        // Add small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      return { success: true, results };
    } catch (error) {
      console.error('Error in batch update:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

// Export singleton instance
export const individualProgramStatusService = new IndividualProgramStatusService();
