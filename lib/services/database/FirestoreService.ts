import { UserService } from './UserService';
import { ProgramService } from './ProgramService';
import { ParticipantService } from './ParticipantService';
import { SubmissionService } from './SubmissionService';
import { NotificationService } from './NotificationService';
import { ChatService } from './ChatService';
import { TransactionService } from './TransactionService';
import { CommitService } from './CommitService';
import { IntegrationService } from './IntegrationService';
import { BaseService } from './BaseService';
import {
  ServiceResponse,
  Dispute,
  COLLECTIONS,
  UnsubscribeFunction,
} from './types';

/**
 * Centralized Firestore Service
 * 
 * This service provides a unified interface to all database operations,
 * eliminating the need for direct Firestore imports throughout the app.
 * 
 * Benefits:
 * - Centralized error handling and logging
 * - Consistent API across all database operations
 * - Type safety with TypeScript interfaces
 * - Reusable query patterns and utilities
 * - Easy testing with mockable service methods
 * - Real-time subscription management
 */
export class FirestoreService extends BaseService {
  // Service modules
  public readonly users: UserService;
  public readonly programs: ProgramService;
  public readonly participants: ParticipantService;
  public readonly submissions: SubmissionService;
  public readonly notifications: NotificationService;
  public readonly chat: ChatService;
  public readonly transactions: TransactionService;
  public readonly commits: CommitService;
  public readonly integrations: IntegrationService;

  constructor() {
    super();

    // Initialize service modules
    this.users = new UserService();
    this.programs = new ProgramService();
    this.participants = new ParticipantService();
    this.submissions = new SubmissionService();
    this.notifications = new NotificationService();
    this.chat = new ChatService();
    this.transactions = new TransactionService();
    this.commits = new CommitService();
    this.integrations = new IntegrationService();
  }

  // Dispute management (not modularized as it's simpler)
  async createDispute(disputeData: Omit<Dispute, 'id'>): Promise<ServiceResponse<string>> {
    return this.createDocument<Dispute>(COLLECTIONS.DISPUTES, disputeData);
  }

  async getDispute(disputeId: string): Promise<ServiceResponse<Dispute | null>> {
    return this.getDocument<Dispute>(COLLECTIONS.DISPUTES, disputeId);
  }

  async updateDispute(disputeId: string, disputeData: Partial<Dispute>): Promise<ServiceResponse<void>> {
    return this.updateDocument<Dispute>(COLLECTIONS.DISPUTES, disputeId, disputeData);
  }

  async getAllDisputes(): Promise<ServiceResponse<Dispute[]>> {
    return this.getCollection<Dispute>(COLLECTIONS.DISPUTES, {
      orderBy: { field: 'createdAt', direction: 'desc' }
    });
  }

  async getDisputesByUser(userEmail: string): Promise<ServiceResponse<Dispute[]>> {
    try {
      // Get all disputes and filter client-side to avoid index requirements
      const allResult = await this.getAllDisputes();
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const userDisputes = (allResult.data || [])
        .filter(dispute => dispute.email === userEmail)
        .sort((a, b) => {
          // Handle both Timestamp and string types
          const dateA = a.createdAt
            ? (typeof a.createdAt === 'string' ? new Date(a.createdAt) : a.createdAt.toDate())
            : new Date(0);
          const dateB = b.createdAt
            ? (typeof b.createdAt === 'string' ? new Date(b.createdAt) : b.createdAt.toDate())
            : new Date(0);
          return dateB.getTime() - dateA.getTime(); // desc order
        });

      return this.createSuccessResponse(userDisputes);
    } catch (error) {
      const dbError = this.handleError('read', COLLECTIONS.DISPUTES, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getDisputesByStatus(status: Dispute['status']): Promise<ServiceResponse<Dispute[]>> {
    try {
      // Get all disputes and filter client-side to avoid index requirements
      const allResult = await this.getAllDisputes();
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const statusDisputes = (allResult.data || [])
        .filter(dispute => dispute.status === status)
        .sort((a, b) => {
          // Handle both Timestamp and string types
          const dateA = a.createdAt
            ? (typeof a.createdAt === 'string' ? new Date(a.createdAt) : a.createdAt.toDate())
            : new Date(0);
          const dateB = b.createdAt
            ? (typeof b.createdAt === 'string' ? new Date(b.createdAt) : b.createdAt.toDate())
            : new Date(0);
          return dateB.getTime() - dateA.getTime(); // desc order
        });

      return this.createSuccessResponse(statusDisputes);
    } catch (error) {
      const dbError = this.handleError('read', COLLECTIONS.DISPUTES, error as Error);
      return this.createErrorResponse(dbError);
    }
  }



  // High-level composite operations
  
  /**
   * Complete user enrollment in a program
   * This handles all the necessary database operations for program enrollment
   */
  async enrollUserInProgram(
    userId: string,
    programId: string,
    enrollmentData: {
      fname: string;
      paymentDone: boolean;
      timezone?: string;
      programPreferences?: import('@/shared/types/customStake').ProgramPreferences;
    }
  ): Promise<ServiceResponse<void>> {
    try {
      // Get program details
      const programResult = await this.programs.getProgramById(programId);
      if (!programResult.success || !programResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: 'programs',
          originalError: new Error('Program not found'),
          timestamp: new Date(),
        });
      }

      const program = programResult.data;

      // Check if user is already enrolled
      const isEnrolledResult = await this.participants.isParticipantEnrolled(programId, userId);
      if (!isEnrolledResult.success) {
        return this.createErrorResponse(isEnrolledResult as any);
      }

      if (isEnrolledResult.data) {
        return this.createErrorResponse({
          operation: 'create',
          collection: 'participants',
          originalError: new Error('User already enrolled in program'),
          timestamp: new Date(),
        });
      }

      // Create participant
      const participantResult = await this.participants.enrollParticipant(programId, userId, {
        fname: enrollmentData.fname,
        paymentDone: enrollmentData.paymentDone,
        defaultLives: program.defaultLives,
        maxLivePurchase: program.maxLivePurchase,
        timezone: enrollmentData.timezone || 'UTC',
        programStartDate: program.startDate,
        programDuration: program.duration,
        programPreferences: enrollmentData.programPreferences,
      });

      if (!participantResult.success) {
        return this.createErrorResponse(participantResult as any);
      }

      // Add program to user's programs
      const userProgramResult = await this.users.addUserProgram(userId, programId);
      if (!userProgramResult.success) {
        return this.createErrorResponse(userProgramResult as any);
      }

      // Update program participant count
      const incrementResult = await this.programs.incrementParticipantCount(programId);
      if (!incrementResult.success) {
        return this.createErrorResponse(incrementResult as any);
      }

      // Initialize submissions for the program
      const submissionResult = await this.submissions.initializeSubmissionsForProgram(
        programId,
        userId,
        program.duration
      );
      if (!submissionResult.success) {
        return this.createErrorResponse(submissionResult as any);
      }

      // Create welcome notifications
      await this.notifications.createProgramJoinedNotification(
        userId,
        program.name,
        new Date(program.startDate).toLocaleDateString()
      );

      await this.notifications.createSetupReminderNotification(userId, program.name);

      // Set up individual scheduling for the new participant (async, don't wait)
      if (program.status === 'upcoming') {
        // Fire and forget - don't wait for scheduling setup to complete
        // This prevents the user from waiting for potentially long-running scheduler operations
        console.log(`Starting background scheduling setup for participant ${userId} in program ${programId}`);
        this.setupIndividualSchedulingAfterEnrollment(programId, userId).catch(schedulingError => {
          console.warn('Failed to set up individual scheduling after enrollment:', schedulingError);
          // The participant can still be manually scheduled later if this fails
        });
      }

      // Create enrollment transaction record
      const transactionResult = await this.transactions.createProgramEnrollmentTransaction(
        userId,
        programId,
        program.name,
        program.betAmount,
        {
          paymentMethod: enrollmentData.paymentDone ? 'stripe' : 'free',
        }
      );
      if (!transactionResult.success) {
        console.warn('Failed to create enrollment transaction:', transactionResult.error);
        // Don't fail the enrollment if transaction creation fails
      }

      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('create', 'enrollment', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Set up individual scheduling for a participant after enrollment
   * This calls the Cloud Function to set up individual daily checkups
   */
  private async setupIndividualSchedulingAfterEnrollment(programId: string, userId: string): Promise<void> {
    try {
      // Import Firebase Functions
      const { getFunctions, httpsCallable } = await import('firebase/functions');
      const functions = getFunctions();

      // Call the Cloud Function to set up individual scheduling
      const setupScheduling = httpsCallable(functions, 'setupIndividualSchedulingOnEnrollment');
      const result = await setupScheduling({ programId, userId });

      console.log('Individual scheduling set up after enrollment:', result.data);
    } catch (error) {
      console.error('Error setting up individual scheduling after enrollment:', error);
      throw error;
    }
  }

  /**
   * Complete user setup for a program
   */
  async completeUserSetup(
    userId: string,
    programId: string,
    setupVar: string,
    category: string
  ): Promise<ServiceResponse<void>> {
    try {
      // Update participant setup status
      const setupResult = await this.participants.completeSetup(programId, userId, setupVar);
      if (!setupResult.success) {
        return this.createErrorResponse(setupResult as any);
      }

      // Create setup complete notification
      await this.notifications.createSetupCompleteNotification(userId, category);

      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('update', 'setup', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Submit user progress for a day
   */
  async submitDayProgress(
    userId: string,
    programId: string,
    dayId: string,
    attachment?: string
  ): Promise<ServiceResponse<void>> {
    try {
      // Submit the day's progress
      const submissionResult = await this.submissions.submitForDay(
        programId,
        userId,
        dayId,
        attachment
      );

      if (!submissionResult.success) {
        return this.createErrorResponse(submissionResult as any);
      }

      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('create', 'submission', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Handle user bailing out for a day
   */
  async bailOutForDay(
    userId: string,
    programId: string,
    dayId: string
  ): Promise<ServiceResponse<void>> {
    try {
      // Get current participant data
      const participantResult = await this.participants.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: 'participants',
          originalError: new Error('Participant not found'),
          timestamp: new Date(),
        });
      }

      const participant = participantResult.data;
      const livesInfo = `(${participant.livesLeft}) (${participant.livesPurchaseLeft})`;

      // Mark submission as bailed
      const bailResult = await this.submissions.markAsBailed(programId, userId, dayId, livesInfo);
      if (!bailResult.success) {
        return this.createErrorResponse(bailResult as any);
      }

      // Decrement lives
      const livesResult = await this.participants.decrementLives(programId, userId);
      if (!livesResult.success) {
        return this.createErrorResponse(livesResult as any);
      }

      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('update', 'bailout', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Purchase lives for a participant
   */
  async purchaseLivesForParticipant(
    userId: string,
    programId: string,
    livesToPurchase: number
  ): Promise<ServiceResponse<void>> {
    return this.participants.purchaseLives(programId, userId, livesToPurchase);
  }

  // Utility methods for common operations

  /**
   * Get comprehensive user data including programs and stats
   */
  async getUserDashboardData(userId: string): Promise<ServiceResponse<{
    user: any;
    programs: any[];
    stats: any;
    unreadNotifications: number;
  }>> {
    try {
      const [userResult, programsResult, statsResult, notificationCountResult] = await Promise.all([
        this.users.getUserById(userId),
        this.users.getUserPrograms(userId),
        this.users.getUserStats(userId),
        this.notifications.getUnreadCount(userId),
      ]);

      if (!userResult.success) {
        return this.createErrorResponse(userResult as any);
      }

      const dashboardData = {
        user: userResult.data,
        programs: programsResult.data || [],
        stats: statsResult.data || {},
        unreadNotifications: notificationCountResult.data || 0,
      };

      return this.createSuccessResponse(dashboardData);
    } catch (error) {
      const dbError = this.handleError('read', 'dashboard', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Get comprehensive program data including participants and stats
   */
  async getProgramDashboardData(programId: string): Promise<ServiceResponse<{
    program: any;
    participants: any[];
    stats: any;
    messageCount: number;
  }>> {
    try {
      const [programResult, participantsResult, statsResult, messageCountResult] = await Promise.all([
        this.programs.getProgramById(programId),
        this.participants.getAllParticipants(programId),
        this.programs.getProgramStats(programId),
        this.chat.getMessageCount(programId),
      ]);

      if (!programResult.success) {
        return this.createErrorResponse(programResult as any);
      }

      const dashboardData = {
        program: programResult.data,
        participants: participantsResult.data || [],
        stats: statsResult.data || {},
        messageCount: messageCountResult.data || 0,
      };

      return this.createSuccessResponse(dashboardData);
    } catch (error) {
      const dbError = this.handleError('read', 'program-dashboard', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Get user programs with participant data for progress tracking
   */
  async getUserProgramsWithParticipantData(userId: string): Promise<ServiceResponse<any[]>> {
    try {
      // Get user programs
      const userProgramsResult = await this.users.getUserPrograms(userId);
      if (!userProgramsResult.success) {
        return this.createErrorResponse(userProgramsResult as any);
      }

      const userPrograms = userProgramsResult.data || [];

      const programIds = userPrograms
        .filter(up => up && up.programId && !up.isArchived) // Filter out null/undefined entries, entries without programId, and archived programs
        .map(up => up.programId);

      if (programIds.length === 0) {
        return this.createSuccessResponse([]);
      }

      // Get program details and participant data in parallel
      const programPromises = programIds.map(async (programId) => {
        try {
          if (!programId) {
            return null;
          }

          const [programResult, participantResult] = await Promise.all([
            this.programs.getProgramById(programId),
            this.participants.getParticipant(programId, userId),
          ]);

          if (!programResult.success || !programResult.data) {
            console.warn(`Failed to fetch program ${programId}:`, programResult.error);
            return null;
          }

          const program = programResult.data;
          const participant = participantResult.success ? participantResult.data : null;

          // Only include programs with specific statuses
          // Map 'active' to 'ongoing' for compatibility with CommonInterface
          if (['active', 'ongoing', 'upcoming', 'ended'].includes(program.status as string)) {
            return {
              ...program,
              id: programId,
              // Map 'active' to 'ongoing' for compatibility if needed
              status: program.status === 'active' ? 'ongoing' : program.status as any,
              setupStatus: participant?.setupStatus || false,
              disqualified: participant?.disqualified || false,
            };
          }

          return null;
        } catch (error) {
          console.error(`Error fetching program ${programId}:`, error);
          return null;
        }
      });

      const programs = await Promise.all(programPromises);
      const filteredPrograms = programs.filter(program => program !== null);

      return this.createSuccessResponse(filteredPrograms);
    } catch (error) {
      const dbError = this.handleError('read', 'user-programs-with-participant-data', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Get participant day status for progress tracking
   */
  async getParticipantsDayStatus(
    programId: string,
    userId: string,
    currentDay: number,
    selectedDay?: number
  ): Promise<ServiceResponse<{
    currentDayParticipants: Array<{
      id: string;
      fname: string;
      livesLeft: number;
      livesPurchaseLeft: number;
      status: string;
      borderColor: string;
    }>;
    selectedDayParticipants?: Array<{
      id: string;
      fname: string;
      livesLeft: number;
      livesPurchaseLeft: number;
      status: string;
      borderColor: string;
    }>;
    userDays: Array<{
      day: number;
      date: string;
      status: string;
    }>;
  }>> {
    try {
      // Get participants with submissions data
      const participantsResult = await this.participants.getParticipantsWithSubmissions(
        programId,
        currentDay,
        selectedDay
      );

      if (!participantsResult.success) {
        return this.createErrorResponse(participantsResult as any);
      }

      // Get user's day tiles
      const userSubmissionsResult = await this.submissions.getAllSubmissions(programId, userId);
      const userSubmissions = userSubmissionsResult.success ? userSubmissionsResult.data || [] : [];

      const submissionsMap = userSubmissions.reduce((acc: any, submission) => {
        acc[submission.id || ''] = submission;
        return acc;
      }, {});

      // Get program to calculate dates
      const programResult = await this.programs.getProgramById(programId);
      if (!programResult.success || !programResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: 'programs',
          originalError: new Error('Program not found'),
          timestamp: new Date(),
        });
      }

      const program = programResult.data;
      const startDate = new Date(program.startDate);
      const userDays = [];

      // Create user day tiles up to current day
      for (let i = 1; i <= Math.min(currentDay, program.duration); i++) {
        const dayDocId = `Day ${i}`;
        const dayData = submissionsMap[dayDocId];
        const dayStatus = dayData?.status || 'upcoming';

        const dayDate = new Date(startDate.getTime() + (i - 1) * 86400000);

        userDays.push({
          day: i,
          date: dayDate.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
          }),
          status: dayStatus,
        });
      }

      return this.createSuccessResponse({
        currentDayParticipants: participantsResult.data?.currentDayParticipants || [],
        selectedDayParticipants: participantsResult.data?.selectedDayParticipants,
        userDays,
      });
    } catch (error) {
      const dbError = this.handleError('read', 'participants-day-status', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  /**
   * Optimized batch function to get participant day status for multiple programs
   */
  async getBatchParticipantsDayStatus(
    programRequests: Array<{
      programId: string;
      userId: string;
      currentDay: number;
      selectedDay?: number;
    }>
  ): Promise<ServiceResponse<{
    [programId: string]: {
      currentDayParticipants: Array<{
        id: string;
        fname: string;
        livesLeft: number;
        livesPurchaseLeft: number;
        status: string;
        borderColor: string;
      }>;
      selectedDayParticipants?: Array<{
        id: string;
        fname: string;
        livesLeft: number;
        livesPurchaseLeft: number;
        status: string;
        borderColor: string;
      }>;
      userDays: Array<{
        day: number;
        date: string;
        status: string;
      }>;
    };
  }>> {
    try {
      // Process all requests in parallel
      const batchPromises = programRequests.map(async (request) => {
        const result = await this.getParticipantsDayStatus(
          request.programId,
          request.userId,
          request.currentDay,
          request.selectedDay
        );
        return {
          programId: request.programId,
          data: result.success ? result.data : null,
        };
      });

      const batchResults = await Promise.all(batchPromises);

      // Organize results by program ID
      const organizedResults: any = {};
      batchResults.forEach(({ programId, data }) => {
        if (data) {
          organizedResults[programId] = data;
        }
      });

      return this.createSuccessResponse(organizedResults);
    } catch (error) {
      const dbError = this.handleError('read', 'batch participants day status', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Subscription management helpers
  private subscriptions: Map<string, UnsubscribeFunction> = new Map();

  /**
   * Add a subscription with automatic cleanup
   */
  addSubscription(key: string, unsubscribe: UnsubscribeFunction): void {
    // Clean up existing subscription if it exists
    this.removeSubscription(key);
    this.subscriptions.set(key, unsubscribe);
  }

  /**
   * Remove a specific subscription
   */
  removeSubscription(key: string): void {
    const unsubscribe = this.subscriptions.get(key);
    if (unsubscribe) {
      unsubscribe();
      this.subscriptions.delete(key);
    }
  }

  /**
   * Clean up all subscriptions
   */
  cleanup(): void {
    this.subscriptions.forEach((unsubscribe) => unsubscribe());
    this.subscriptions.clear();
  }
}

// Export singleton instance
export const firestoreService = new FirestoreService();
