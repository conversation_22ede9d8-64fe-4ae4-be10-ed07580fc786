import { BaseService } from './BaseService';
import {
  Integration,
  ServiceResponse,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
  DocumentSubscriptionCallback,
} from './types';

export class IntegrationService extends BaseService {
  private readonly COLLECTION_PATH = COLLECTIONS.USERS;

  // Integration CRUD operations
  async createIntegration(
    userId: string,
    integrationData: Omit<Integration, 'id'>
  ): Promise<ServiceResponse<string>> {
    try {


      const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
      const integrationId = `${integrationData.provider}_${Date.now()}`;



      const result = await this.createDocument<Integration>(collectionPath, integrationData, integrationId);

      if (result.success) {
      } else {
      }

      return result;
    } catch (error) {
      console.error('💥 Exception in createIntegration:', error);
      const dbError = this.handleError('create', `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getIntegration(
    userId: string,
    integrationId: string
  ): Promise<ServiceResponse<Integration | null>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.getDocument<Integration>(collectionPath, integrationId);
  }

  async getIntegrationByProvider(
    userId: string,
    provider: Integration['provider']
  ): Promise<ServiceResponse<Integration | null>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    const result = await this.getCollection<Integration>(collectionPath, {
      where: [{ field: 'provider', operator: '==', value: provider }],
      limit: 1,
    });

    if (!result.success) {
      return this.createErrorResponse(result as any);
    }

    const integration = result.data && result.data.length > 0 ? result.data[0] : null;
    return this.createSuccessResponse(integration);
  }

  async getAllIntegrations(userId: string): Promise<ServiceResponse<Integration[]>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.getCollection<Integration>(collectionPath, {
      orderBy: { field: 'createdAt', direction: 'desc' }
    });
  }

  async getActiveIntegrations(userId: string): Promise<ServiceResponse<Integration[]>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.getCollection<Integration>(collectionPath, {
      where: [{ field: 'isActive', operator: '==', value: true }],
      orderBy: { field: 'createdAt', direction: 'desc' }
    });
  }

  async updateIntegration(
    userId: string,
    integrationId: string,
    integrationData: Partial<Integration>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.updateDocument<Integration>(collectionPath, integrationId, integrationData);
  }

  async deleteIntegration(userId: string, integrationId: string): Promise<ServiceResponse<void>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.deleteDocument(collectionPath, integrationId);
  }

  // Integration-specific operations
  async saveGitHubIntegration(
    userId: string,
    githubData: {
      accessToken: string;
      tokenType?: string;
      scope?: string;
      user: {
        id: number;
        login: string;
        name?: string;
        email?: string;
        avatar_url?: string;
      };
    }
  ): Promise<ServiceResponse<string>> {
    try {


      const integrationData: Omit<Integration, 'id'> = {
        provider: 'github',
        accessToken: githubData.accessToken,
        tokenType: githubData.tokenType || 'bearer',
        scope: githubData.scope,
        user: githubData.user,
        isActive: true,
        lastSyncAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };


      // Check if GitHub integration already exists
      const existingResult = await this.getIntegrationByProvider(userId, 'github');

      if (existingResult.success && existingResult.data) {

        // Update existing integration
        const updateResult = await this.updateIntegration(
          userId,
          existingResult.data.id!,
          {
            ...integrationData,
            updatedAt: new Date().toISOString(),
          }
        );
        if (updateResult.success) {
          return this.createSuccessResponse(existingResult.data.id!);
        } else {
          return this.createErrorResponse(updateResult as any);
        }
      } else {

        // Create new integration
        const createResult = await this.createIntegration(userId, integrationData);
        if (createResult.success) {
        } else {
        }
        return createResult;
      }
    } catch (error) {

      const dbError = this.handleError('create', `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async updateIntegrationSyncTime(
    userId: string,
    integrationId: string
  ): Promise<ServiceResponse<void>> {
    return this.updateIntegration(userId, integrationId, {
      lastSyncAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
  }

  async deactivateIntegration(
    userId: string,
    integrationId: string
  ): Promise<ServiceResponse<void>> {
    return this.updateIntegration(userId, integrationId, {
      isActive: false,
      updatedAt: new Date().toISOString(),
    });
  }

  async reactivateIntegration(
    userId: string,
    integrationId: string
  ): Promise<ServiceResponse<void>> {
    return this.updateIntegration(userId, integrationId, {
      isActive: true,
      updatedAt: new Date().toISOString(),
    });
  }

  // Subscription methods
  subscribeToIntegrations(
    userId: string,
    callback: DocumentSubscriptionCallback<Integration[]>
  ): UnsubscribeFunction {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.subscribeToCollection<Integration>(collectionPath, callback, {
      orderBy: { field: 'createdAt', direction: 'desc' }
    });
  }

  subscribeToIntegration(
    userId: string,
    integrationId: string,
    callback: DocumentSubscriptionCallback<Integration | null>
  ): UnsubscribeFunction {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_INTEGRATIONS}`;
    return this.subscribeToDocument<Integration>(collectionPath, integrationId, callback);
  }

  // Validation
  validateIntegrationData(integrationData: Partial<Integration>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!integrationData.provider) {
      errors.push('Provider is required');
    }

    if (!integrationData.accessToken) {
      errors.push('Access token is required');
    }

    if (!integrationData.user || !integrationData.user.id) {
      errors.push('User data with ID is required');
    }

    if (integrationData.isActive === undefined) {
      errors.push('isActive status is required');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
