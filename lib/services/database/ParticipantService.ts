import { BaseService } from './BaseService';
import {
  Participant,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
  DocumentSubscriptionCallback,
} from './types';

export class ParticipantService extends BaseService {
  private getParticipantPath(programId: string): string {
    return `${COLLECTIONS.PROGRAMS}/${programId}/${SUBCOLLECTIONS.PROGRAM_PARTICIPANTS}`;
  }

  // Participant CRUD operations
  async createParticipant(
    programId: string,
    userId: string,
    participantData: Omit<Participant, 'id' | 'userId'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getParticipantPath(programId);
    const fullParticipantData: Omit<Participant, 'id'> = {
      ...participantData,
      userId,
    };
    return this.createDocument<Participant>(collectionPath, fullParticipantData, userId);
  }

  async getParticipant(programId: string, userId: string): Promise<ServiceResponse<Participant | null>> {
    const collectionPath = this.getParticipantPath(programId);
    return this.getDocument<Participant>(collectionPath, userId);
  }

  async updateParticipant(
    programId: string,
    userId: string,
    participantData: Partial<Participant>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getParticipantPath(programId);
    return this.updateDocument<Participant>(collectionPath, userId, participantData);
  }

  async deleteParticipant(programId: string, userId: string): Promise<ServiceResponse<void>> {
    const collectionPath = this.getParticipantPath(programId);
    return this.deleteDocument(collectionPath, userId);
  }

  // Participant enrollment
  async enrollParticipant(
    programId: string,
    userId: string,
    enrollmentData: {
      fname: string;
      paymentDone: boolean;
      defaultLives: number;
      maxLivePurchase: number;
      timezone?: string;
      programStartDate: string; // Program's scheduled start date
      programDuration: number | string;  // Program duration in days
      programPreferences?: import('@/shared/types/customStake').ProgramPreferences;
    }
  ): Promise<ServiceResponse<string>> {
    const userTimezone = enrollmentData.timezone || 'UTC';

    // Debug logging
    console.log('(NOBRIDGE) DEBUG enrollParticipant called with:', {
      programId,
      userId,
      programStartDate: enrollmentData.programStartDate,
      programDuration: enrollmentData.programDuration,
      timezone: userTimezone
    });

    // Calculate personal start and end dates based on user's timezone
    const personalStartDate = this.calculatePersonalStartDate(enrollmentData.programStartDate, userTimezone);
    const personalEndDate = this.calculatePersonalEndDate(personalStartDate, enrollmentData.programDuration);

    const participantData: Omit<Participant, 'id' | 'userId'> = {
      fname: enrollmentData.fname,
      signedUpAt: new Date().toISOString(),
      paymentDone: enrollmentData.paymentDone,
      setupStatus: false,
      setupVar: '',
      livesLeft: enrollmentData.defaultLives,
      livesPurchaseLeft: enrollmentData.maxLivePurchase,
      disqualified: false,
      timezone: userTimezone,

      // Program preferences and settings
      programPreferences: enrollmentData.programPreferences,

      // Individual program status fields
      personalProgramStatus: 'upcoming',
      personalStartDate,
      personalEndDate,
      personalCurrentDay: 0, // Will be 1 when program starts
      // lastDayCheckup is optional and will be set during first checkup
    };

    return this.createParticipant(programId, userId, participantData);
  }

  async isParticipantEnrolled(programId: string, userId: string): Promise<ServiceResponse<boolean>> {
    const result = await this.getParticipant(programId, userId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data !== null);
  }

  // Participant status management
  async updateSetupStatus(
    programId: string,
    userId: string,
    setupStatus: boolean,
    setupVar?: string
  ): Promise<ServiceResponse<void>> {
    const updateData: Partial<Participant> = { setupStatus };
    if (setupVar !== undefined) {
      updateData.setupVar = setupVar;
    }
    return this.updateParticipant(programId, userId, updateData);
  }

  async completeSetup(
    programId: string,
    userId: string,
    setupVar: string
  ): Promise<ServiceResponse<void>> {
    return this.updateSetupStatus(programId, userId, true, setupVar);
  }

  async disqualifyParticipant(
    programId: string,
    userId: string,
    reason?: string
  ): Promise<ServiceResponse<void>> {
    const updateData: Partial<Participant> = {
      disqualified: true,
      disqualifyReason: reason,
    };
    return this.updateParticipant(programId, userId, updateData);
  }

  async requalifyParticipant(programId: string, userId: string): Promise<ServiceResponse<void>> {
    const updateData: Partial<Participant> = {
      disqualified: false,
      disqualifyReason: undefined,
    };
    return this.updateParticipant(programId, userId, updateData);
  }

  // Lives management
  async updateLives(
    programId: string,
    userId: string,
    livesLeft: number
  ): Promise<ServiceResponse<void>> {
    return this.updateParticipant(programId, userId, { livesLeft });
  }

  async decrementLives(
    programId: string,
    userId: string,
    decrementBy: number = 1
  ): Promise<ServiceResponse<void>> {
    try {
      const participantResult = await this.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: this.getParticipantPath(programId),
          originalError: new Error('Participant not found'),
          timestamp: new Date(),
        });
      }

      const currentLives = participantResult.data.livesLeft;
      const newLives = Math.max(0, currentLives - decrementBy);
      
      return this.updateLives(programId, userId, newLives);
    } catch (error) {
      const dbError = this.handleError('update', this.getParticipantPath(programId), error as Error, userId);
      return this.createErrorResponse(dbError);
    }
  }

  async purchaseLives(
    programId: string,
    userId: string,
    livesToPurchase: number
  ): Promise<ServiceResponse<void>> {
    try {
      const participantResult = await this.getParticipant(programId, userId);
      if (!participantResult.success || !participantResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: this.getParticipantPath(programId),
          originalError: new Error('Participant not found'),
          timestamp: new Date(),
        });
      }

      const participant = participantResult.data;
      
      if (participant.livesPurchaseLeft < livesToPurchase) {
        return this.createErrorResponse({
          operation: 'update',
          collection: this.getParticipantPath(programId),
          originalError: new Error('Not enough lives available for purchase'),
          timestamp: new Date(),
        });
      }

      const updateData: Partial<Participant> = {
        livesLeft: participant.livesLeft + livesToPurchase,
        livesPurchaseLeft: participant.livesPurchaseLeft - livesToPurchase,
      };

      return this.updateParticipant(programId, userId, updateData);
    } catch (error) {
      const dbError = this.handleError('update', this.getParticipantPath(programId), error as Error, userId);
      return this.createErrorResponse(dbError);
    }
  }

  // Participant querying
  async getAllParticipants(programId: string): Promise<ServiceResponse<Participant[]>> {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.getCollection<Participant>(collectionPath, options);
  }

  async getActiveParticipants(programId: string): Promise<ServiceResponse<Participant[]>> {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      where: [
        { field: 'disqualified', operator: '==', value: false },
        { field: 'livesLeft', operator: '>=', value: 0 }
      ],
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.getCollection<Participant>(collectionPath, options);
  }

  async getDisqualifiedParticipants(programId: string): Promise<ServiceResponse<Participant[]>> {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      where: [{ field: 'disqualified', operator: '==', value: true }],
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.getCollection<Participant>(collectionPath, options);
  }

  async getParticipantsWithoutSetup(programId: string): Promise<ServiceResponse<Participant[]>> {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      where: [{ field: 'setupStatus', operator: '==', value: false }],
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.getCollection<Participant>(collectionPath, options);
  }

  async getParticipantsWithPayment(programId: string): Promise<ServiceResponse<Participant[]>> {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      where: [{ field: 'paymentDone', operator: '==', value: true }],
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.getCollection<Participant>(collectionPath, options);
  }

  // Participant statistics
  async getParticipantCount(programId: string): Promise<ServiceResponse<number>> {
    const result = await this.getAllParticipants(programId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  async getActiveParticipantCount(programId: string): Promise<ServiceResponse<number>> {
    const result = await this.getActiveParticipants(programId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  async getDisqualifiedParticipantCount(programId: string): Promise<ServiceResponse<number>> {
    const result = await this.getDisqualifiedParticipants(programId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  // Real-time subscriptions
  subscribeToParticipant(
    programId: string,
    userId: string,
    callback: DocumentSubscriptionCallback<Participant>
  ): UnsubscribeFunction {
    const collectionPath = this.getParticipantPath(programId);
    return this.subscribeToDocument<Participant>(collectionPath, userId, callback);
  }

  subscribeToAllParticipants(
    programId: string,
    callback: (participants: Participant[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.subscribeToCollection<Participant>(collectionPath, callback, options);
  }

  subscribeToActiveParticipants(
    programId: string,
    callback: (participants: Participant[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getParticipantPath(programId);
    const options: QueryOptions = {
      where: [
        { field: 'disqualified', operator: '==', value: false },
        { field: 'livesLeft', operator: '>=', value: 0 }
      ],
      orderBy: { field: 'signedUpAt', direction: 'asc' }
    };
    return this.subscribeToCollection<Participant>(collectionPath, callback, options);
  }

  // Advanced participant operations for progress tracking
  async getParticipantsWithSubmissions(
    programId: string,
    currentDay: number,
    selectedDay?: number
  ): Promise<ServiceResponse<{
    currentDayParticipants: Array<{
      id: string;
      fname: string;
      livesLeft: number;
      livesPurchaseLeft: number;
      status: string;
      borderColor: string;
    }>;
    selectedDayParticipants?: Array<{
      id: string;
      fname: string;
      livesLeft: number;
      livesPurchaseLeft: number;
      status: string;
      borderColor: string;
    }>;
  }>> {
    try {
      const participantsResult = await this.getAllParticipants(programId);
      if (!participantsResult.success || !participantsResult.data) {
        return this.createErrorResponse(participantsResult as any);
      }

      const participants = participantsResult.data;
      const currentDayParticipants = [];
      const selectedDayParticipants = [];

      // Optimized: Fetch specific day submissions for all participants in parallel
      const currentDayId = `Day ${currentDay}`;
      const selectedDayId = selectedDay !== undefined ? `Day ${selectedDay}` : null;

      // Create parallel promises for fetching specific day submissions
      const submissionPromises = participants.map(async (participant) => {
        const promises = [
          this.getSpecificDaySubmission(programId, participant.userId, currentDayId)
        ];

        if (selectedDayId && selectedDayId !== currentDayId) {
          promises.push(this.getSpecificDaySubmission(programId, participant.userId, selectedDayId));
        }

        const results = await Promise.all(promises);
        return {
          participant,
          currentDaySubmission: results[0],
          selectedDaySubmission: results[1] || results[0] // Use current day if same day
        };
      });

      // Wait for all submissions to be fetched in parallel
      const participantSubmissions = await Promise.all(submissionPromises);

      // Process results
      for (const { participant, currentDaySubmission, selectedDaySubmission } of participantSubmissions) {
        // Get current day status
        const currentStatus = currentDaySubmission?.status || 'upcoming';

        currentDayParticipants.push({
          id: participant.userId,
          fname: participant.fname || 'Unknown',
          livesLeft: participant.livesLeft || 0,
          livesPurchaseLeft: participant.livesPurchaseLeft || 0,
          status: currentStatus,
          borderColor: currentStatus === 'submitted' ? '#4CAF50' : '#FFA500',
        });

        // Get selected day status if specified
        if (selectedDay !== undefined) {
          const selectedStatus = selectedDaySubmission?.status || 'upcoming';

          selectedDayParticipants.push({
            id: participant.userId,
            fname: participant.fname || 'Unknown',
            livesLeft: participant.livesLeft || 0,
            livesPurchaseLeft: participant.livesPurchaseLeft || 0,
            status: selectedStatus,
            borderColor: selectedStatus === 'submitted' ? '#4CAF50' : '#FFA500',
          });
        }
      }

      return this.createSuccessResponse({
        currentDayParticipants,
        selectedDayParticipants: selectedDay !== undefined ? selectedDayParticipants : undefined,
      });
    } catch (error) {
      const dbError = this.handleError('read', this.getParticipantPath(programId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Optimized method to get specific day submission for a participant
  private async getSpecificDaySubmission(programId: string, userId: string, dayId: string): Promise<any | null> {
    try {
      const submissionPath = `${COLLECTIONS.PROGRAMS}/${programId}/${SUBCOLLECTIONS.PROGRAM_PARTICIPANTS}/${userId}/${SUBCOLLECTIONS.PARTICIPANT_SUBMISSIONS}`;
      const result = await this.getDocument(submissionPath, dayId);
      return result.success ? result.data : null;
    } catch (error) {
      console.error(`Error fetching submission for ${userId}, day ${dayId}:`, error);
      return null;
    }
  }

  // Legacy method - kept for backward compatibility but not used in optimized flow
  private async getSubmissionsForParticipant(programId: string, userId: string): Promise<ServiceResponse<any[]>> {
    try {
      const submissionPath = `${COLLECTIONS.PROGRAMS}/${programId}/${SUBCOLLECTIONS.PROGRAM_PARTICIPANTS}/${userId}/${SUBCOLLECTIONS.PARTICIPANT_SUBMISSIONS}`;
      return this.getCollection(submissionPath);
    } catch (error) {
      const dbError = this.handleError('read', `submissions for ${userId}`, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Helper methods for individual program status
  private calculatePersonalStartDate(programStartDate: string, userTimezone: string): string {
    try {
      console.log('(NOBRIDGE) DEBUG calculatePersonalStartDate inputs:', { programStartDate, userTimezone });

      // Validate input
      if (!programStartDate || typeof programStartDate !== 'string') {
        throw new Error(`Invalid programStartDate: ${programStartDate}`);
      }

      if (!userTimezone || typeof userTimezone !== 'string') {
        throw new Error(`Invalid userTimezone: ${userTimezone}`);
      }

      // Create a date object for the program start date at midnight UTC
      const startDate = new Date(programStartDate + 'T00:00:00.000Z');

      console.log('(NOBRIDGE) DEBUG created startDate:', startDate, 'isValid:', !isNaN(startDate.getTime()));

      // Validate the created date
      if (isNaN(startDate.getTime())) {
        throw new Error(`Invalid date created from: ${programStartDate}`);
      }

      const result = startDate.toISOString();
      console.log('(NOBRIDGE) DEBUG calculatePersonalStartDate result:', result);
      return result;
    } catch (error) {
      console.error('Error calculating personal start date:', error);
      // Fallback to a safe default date
      const fallbackDate = new Date();
      fallbackDate.setHours(0, 0, 0, 0);
      const fallbackResult = fallbackDate.toISOString();
      console.log('(NOBRIDGE) DEBUG using fallback date:', fallbackResult);
      return fallbackResult;
    }
  }

  private calculatePersonalEndDate(personalStartDate: string, durationDays: number | string): string {
    try {
      console.log('(NOBRIDGE) DEBUG calculatePersonalEndDate inputs:', { personalStartDate, durationDays, type: typeof durationDays });

      // Validate inputs
      if (!personalStartDate || typeof personalStartDate !== 'string') {
        throw new Error(`Invalid personalStartDate: ${personalStartDate}`);
      }

      // Convert duration to number if it's a string
      const duration = typeof durationDays === 'string' ? parseInt(durationDays, 10) : durationDays;

      if (!duration || typeof duration !== 'number' || duration <= 0 || isNaN(duration)) {
        throw new Error(`Invalid durationDays after conversion: ${duration} (original: ${durationDays})`);
      }

      const startDate = new Date(personalStartDate);

      // Validate the start date
      if (isNaN(startDate.getTime())) {
        throw new Error(`Invalid start date: ${personalStartDate}`);
      }

      const endDate = new Date(startDate.getTime() + (duration * 24 * 60 * 60 * 1000));

      console.log('(NOBRIDGE) DEBUG calculated endDate:', endDate, 'isValid:', !isNaN(endDate.getTime()));

      // Validate the end date
      if (isNaN(endDate.getTime())) {
        throw new Error(`Invalid end date calculated from start: ${personalStartDate}, duration: ${duration}`);
      }

      const result = endDate.toISOString();
      console.log('(NOBRIDGE) DEBUG calculatePersonalEndDate result:', result);
      return result;
    } catch (error) {
      console.error('Error calculating personal end date:', error);
      // Fallback to a safe default (start date + 7 days)
      const fallbackStart = new Date();
      fallbackStart.setDate(fallbackStart.getDate() + 7);
      const fallbackResult = fallbackStart.toISOString();
      console.log('(NOBRIDGE) DEBUG using fallback end date:', fallbackResult);
      return fallbackResult;
    }
  }

  // Validation
  validateParticipantData(participantData: Partial<Participant>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (participantData.fname && participantData.fname.trim().length < 2) {
      errors.push('First name must be at least 2 characters');
    }

    if (participantData.livesLeft !== undefined && participantData.livesLeft < 0) {
      errors.push('Lives left cannot be negative');
    }

    if (participantData.livesPurchaseLeft !== undefined && participantData.livesPurchaseLeft < 0) {
      errors.push('Lives purchase left cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
