import { BaseService } from './BaseService';
import {
  Commit,
  UserCommit,
  CommitSubmission,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
} from './types';

export class CommitService extends BaseService {
  private readonly COLLECTION_PATH = COLLECTIONS.COMMITS;

  private getUserCommitPath(userId: string): string {
    return `${COLLECTIONS.USERS}/${userId}/${SUBCOLLECTIONS.USER_COMMITS}`;
  }

  private getCommitSubmissionsPath(commitId: string): string {
    return `${this.COLLECTION_PATH}/${commitId}/${SUBCOLLECTIONS.COMMIT_SUBMISSIONS}`;
  }

  // Main commit CRUD operations
  async createCommit(commitData: Omit<Commit, 'id'>): Promise<ServiceResponse<string>> {
    // Sanitize the data to remove undefined values before saving to Firebase
    const sanitizedData = this.sanitizeCommitData(commitData);
    return this.createDocument<Commit>(this.COLLECTION_PATH, sanitizedData);
  }

  async getCommitById(commitId: string): Promise<ServiceResponse<Commit | null>> {
    return this.getDocument<Commit>(this.COLLECTION_PATH, commitId);
  }

  async updateCommit(commitId: string, commitData: Partial<Commit>): Promise<ServiceResponse<void>> {
    return this.updateDocument<Commit>(this.COLLECTION_PATH, commitId, commitData);
  }

  async deleteCommit(commitId: string): Promise<ServiceResponse<void>> {
    return this.deleteDocument(this.COLLECTION_PATH, commitId);
  }

  async getAllCommits(options?: QueryOptions): Promise<ServiceResponse<Commit[]>> {
    return this.getCollection<Commit>(this.COLLECTION_PATH, options);
  }

  async getCommitsByUser(userId: string): Promise<ServiceResponse<Commit[]>> {
    // Query without orderBy to avoid composite index requirement, then sort client-side
    const result = await this.getCollection<Commit>(this.COLLECTION_PATH, {
      where: [{ field: 'userId', operator: '==', value: userId }]
    });

    if (result.success && result.data) {
      // Sort client-side by createdAt descending
      const sortedCommits = this.sortCommitsByDate(result.data);
      return this.createSuccessResponse(sortedCommits);
    }

    return result;
  }

  async getActiveCommitsByUser(userId: string): Promise<ServiceResponse<Commit[]>> {
    // Query without orderBy to avoid composite index requirement, then filter and sort client-side
    const result = await this.getCollection<Commit>(this.COLLECTION_PATH, {
      where: [{ field: 'userId', operator: '==', value: userId }]
    });

    if (result.success && result.data) {
      // Filter for active status and sort client-side by createdAt descending
      const activeCommits = this.sortCommitsByDate(
        result.data.filter(commit => commit.status === 'active')
      );
      return this.createSuccessResponse(activeCommits);
    }

    return result;
  }

  // User commit subcollection operations
  async createUserCommit(
    userId: string,
    commitId: string,
    userCommitData: Omit<UserCommit, 'id'>
  ): Promise<ServiceResponse<string>> {
    const collectionPath = this.getUserCommitPath(userId);
    // Sanitize the user commit data to remove undefined values
    const sanitizedData = this.sanitizeCommitData(userCommitData);
    return this.createDocument<UserCommit>(collectionPath, sanitizedData, commitId);
  }

  async getUserCommit(userId: string, commitId: string): Promise<ServiceResponse<UserCommit | null>> {
    const collectionPath = this.getUserCommitPath(userId);
    return this.getDocument<UserCommit>(collectionPath, commitId);
  }

  async updateUserCommit(
    userId: string,
    commitId: string,
    userCommitData: Partial<UserCommit>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getUserCommitPath(userId);
    return this.updateDocument<UserCommit>(collectionPath, commitId, userCommitData);
  }

  async deleteUserCommit(userId: string, commitId: string): Promise<ServiceResponse<void>> {
    const collectionPath = this.getUserCommitPath(userId);
    return this.deleteDocument(collectionPath, commitId);
  }

  async getUserCommits(userId: string): Promise<ServiceResponse<UserCommit[]>> {
    const collectionPath = this.getUserCommitPath(userId);
    return this.getCollection<UserCommit>(collectionPath, {
      orderBy: { field: 'createdAt', direction: 'desc' }
    });
  }

  // Submissions subcollection operations
  async createCommitSubmissions(
    commitId: string,
    frequency: 'daily' | 'weekly' | 'monthly' | 'once',
    totalReports: number,
    commitData?: any // Optional commit data for weekly/monthly frequency details
  ): Promise<ServiceResponse<void>> {
    try {
      const submissionsPath = this.getCommitSubmissionsPath(commitId);

      if (frequency === 'daily' || frequency === 'once') {
        // For daily and once modes, create individual day documents
        const dayCount = frequency === 'once' ? 1 : totalReports;

        for (let i = 1; i <= dayCount; i++) {
          const dayDocumentId = `Day ${i}`;
          const submissionData: Omit<CommitSubmission, 'id'> = {
            attachment: '',
            status: 'upcoming',
            timestamp: ''
          };

          const result = await this.createDocument<CommitSubmission>(
            submissionsPath,
            submissionData,
            dayDocumentId
          );

          if (!result.success) {
            return this.createErrorResponse({
              operation: 'create',
              collection: 'commit_submissions',
              originalError: new Error(`Failed to create submission for ${dayDocumentId}: ${result.error}`),
              timestamp: new Date()
            });
          }
        }
      } else if (frequency === 'weekly') {
        // For weekly mode, create Week X documents with Submission Y subcollections
        // Handle both old and new data formats for duration
        const duration = commitData?.schedule?.duration || commitData?.weekLength || 1;
        // Get timesPerWeek from schedule (new format) or root level (old format), or calculate from totalReports and duration
        const timesPerWeek = commitData?.schedule?.timesPerWeek || commitData?.timesPerWeek || Math.ceil(totalReports / duration);



        // Create all weeks and their submissions
        for (let week = 1; week <= duration; week++) {
          const weekDocumentId = `Week ${week}`;

          try {
            // Create the week document first (empty document to establish the structure)
            const weekResult = await this.createDocument<any>(
              submissionsPath,
              {
                createdAt: new Date().toISOString(),
                weekNumber: week,
                totalSubmissions: timesPerWeek
              },
              weekDocumentId
            );

            if (!weekResult.success) {
              throw new Error(`Failed to create week document for ${weekDocumentId}: ${weekResult.error}`);
            }

            // Create submissions subcollection under this week
            const weekSubmissionsPath = `${submissionsPath}/${weekDocumentId}/submissions`;

            // Create all submissions for this week
            for (let submission = 1; submission <= timesPerWeek; submission++) {
              const submissionDocumentId = `Submission ${submission}`;
              const submissionData: Omit<CommitSubmission, 'id'> = {
                attachment: '',
                status: 'upcoming',
                timestamp: ''
              };

              const submissionResult = await this.createDocument<CommitSubmission>(
                weekSubmissionsPath,
                submissionData,
                submissionDocumentId
              );

              if (!submissionResult.success) {
                throw new Error(`Failed to create submission for ${weekDocumentId} ${submissionDocumentId}: ${submissionResult.error}`);
              }
            }
          } catch (error) {
            return this.createErrorResponse({
              operation: 'create',
              collection: 'commit_submissions',
              originalError: error as Error,
              timestamp: new Date()
            });
          }
        }
      } else if (frequency === 'monthly') {
        // For monthly mode, create Month X documents with Submission Y subcollections
        // Handle both old and new data formats for duration
        const duration = commitData?.schedule?.duration || commitData?.weekLength || 1;
        // Get timesPerMonth from schedule (new format) or root level (old format), or calculate from totalReports and duration
        const timesPerMonth = commitData?.schedule?.timesPerMonth || commitData?.timesPerMonth || Math.ceil(totalReports / duration);

        for (let month = 1; month <= duration; month++) {
          const monthDocumentId = `Month ${month}`;

          // Create the month document first (empty document to establish the structure)
          const monthResult = await this.createDocument<any>(
            submissionsPath,
            { createdAt: new Date().toISOString() },
            monthDocumentId
          );

          if (!monthResult.success) {
            return this.createErrorResponse({
              operation: 'create',
              collection: 'commit_submissions',
              originalError: new Error(`Failed to create month document for ${monthDocumentId}: ${monthResult.error}`),
              timestamp: new Date()
            });
          }

          // Create submissions subcollection under this month
          const monthSubmissionsPath = `${submissionsPath}/${monthDocumentId}/submissions`;

          for (let submission = 1; submission <= timesPerMonth; submission++) {
            const submissionDocumentId = `Submission ${submission}`;
            const submissionData: Omit<CommitSubmission, 'id'> = {
              attachment: '',
              status: 'upcoming',
              timestamp: ''
            };

            const submissionResult = await this.createDocument<CommitSubmission>(
              monthSubmissionsPath,
              submissionData,
              submissionDocumentId
            );

            if (!submissionResult.success) {
              return this.createErrorResponse({
                operation: 'create',
                collection: 'commit_submissions',
                originalError: new Error(`Failed to create submission for ${monthDocumentId} ${submissionDocumentId}: ${submissionResult.error}`),
                timestamp: new Date()
              });
            }
          }
        }
      }

      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('create', 'commit_submissions', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Get all submissions for a commit
  async getCommitSubmissions(commitId: string): Promise<ServiceResponse<CommitSubmission[]>> {
    const submissionsPath = this.getCommitSubmissionsPath(commitId);
    return this.getCollection<CommitSubmission>(submissionsPath);
  }

  // Get a specific submission for a commit (for daily/once modes)
  async getCommitSubmission(commitId: string, submissionId: string): Promise<ServiceResponse<CommitSubmission | null>> {
    const submissionsPath = this.getCommitSubmissionsPath(commitId);
    return this.getDocument<CommitSubmission>(submissionsPath, submissionId);
  }

  // Get submissions for a specific week (for weekly mode)
  async getWeekSubmissions(commitId: string, weekId: string): Promise<ServiceResponse<CommitSubmission[]>> {
    const weekSubmissionsPath = `${this.getCommitSubmissionsPath(commitId)}/${weekId}/submissions`;
    return this.getCollection<CommitSubmission>(weekSubmissionsPath);
  }

  // Get submissions for a specific month (for monthly mode)
  async getMonthSubmissions(commitId: string, monthId: string): Promise<ServiceResponse<CommitSubmission[]>> {
    const monthSubmissionsPath = `${this.getCommitSubmissionsPath(commitId)}/${monthId}/submissions`;
    return this.getCollection<CommitSubmission>(monthSubmissionsPath);
  }

  // Get a specific submission within a week (for weekly mode)
  async getWeekSubmission(commitId: string, weekId: string, submissionId: string): Promise<ServiceResponse<CommitSubmission | null>> {
    const weekSubmissionsPath = `${this.getCommitSubmissionsPath(commitId)}/${weekId}/submissions`;
    return this.getDocument<CommitSubmission>(weekSubmissionsPath, submissionId);
  }

  // Get a specific submission within a month (for monthly mode)
  async getMonthSubmission(commitId: string, monthId: string, submissionId: string): Promise<ServiceResponse<CommitSubmission | null>> {
    const monthSubmissionsPath = `${this.getCommitSubmissionsPath(commitId)}/${monthId}/submissions`;
    return this.getDocument<CommitSubmission>(monthSubmissionsPath, submissionId);
  }

  // Update a specific submission for a commit (for daily/once modes)
  async updateCommitSubmission(
    commitId: string,
    submissionId: string,
    submissionData: Partial<CommitSubmission>
  ): Promise<ServiceResponse<void>> {
    const submissionsPath = this.getCommitSubmissionsPath(commitId);
    return this.updateDocument<CommitSubmission>(submissionsPath, submissionId, submissionData);
  }

  // Update a specific submission within a week (for weekly mode)
  async updateWeekSubmission(
    commitId: string,
    weekId: string,
    submissionId: string,
    submissionData: Partial<CommitSubmission>
  ): Promise<ServiceResponse<void>> {
    const weekSubmissionsPath = `${this.getCommitSubmissionsPath(commitId)}/${weekId}/submissions`;
    return this.updateDocument<CommitSubmission>(weekSubmissionsPath, submissionId, submissionData);
  }

  // Update a specific submission within a month (for monthly mode)
  async updateMonthSubmission(
    commitId: string,
    monthId: string,
    submissionId: string,
    submissionData: Partial<CommitSubmission>
  ): Promise<ServiceResponse<void>> {
    const monthSubmissionsPath = `${this.getCommitSubmissionsPath(commitId)}/${monthId}/submissions`;
    return this.updateDocument<CommitSubmission>(monthSubmissionsPath, submissionId, submissionData);
  }

  // Get all week documents for a commit (for weekly mode)
  async getCommitWeeks(commitId: string): Promise<ServiceResponse<any[]>> {
    const submissionsPath = this.getCommitSubmissionsPath(commitId);
    return this.getCollection<any>(submissionsPath, {
      orderBy: { field: 'createdAt', direction: 'asc' }
    });
  }



  // Get all month documents for a commit (for monthly mode)
  async getCommitMonths(commitId: string): Promise<ServiceResponse<any[]>> {
    const submissionsPath = this.getCommitSubmissionsPath(commitId);
    return this.getCollection<any>(submissionsPath, {
      orderBy: { field: 'createdAt', direction: 'asc' }
    });
  }

  // Helper method to get submission details for a specific period
  async getSubmissionDetails(
    commitId: string,
    frequency: 'daily' | 'weekly' | 'monthly' | 'once',
    periodId: string, // e.g., "Day 1", "Week 1", "Month 1"
    submissionId?: string // e.g., "Submission 1" for weekly/monthly
  ): Promise<ServiceResponse<CommitSubmission | null>> {
    if (frequency === 'daily' || frequency === 'once') {
      return this.getCommitSubmission(commitId, periodId);
    } else if (frequency === 'weekly') {
      return this.getWeekSubmission(commitId, periodId, submissionId || 'Submission 1');
    } else if (frequency === 'monthly') {
      return this.getMonthSubmission(commitId, periodId, submissionId || 'Submission 1');
    }

    return this.createErrorResponse({
      operation: 'read',
      collection: 'commit_submissions',
      originalError: new Error('Invalid frequency type'),
      timestamp: new Date()
    });
  }

  // Helper method to update submission details for a specific period
  async updateSubmissionDetails(
    commitId: string,
    frequency: 'daily' | 'weekly' | 'monthly' | 'once',
    periodId: string, // e.g., "Day 1", "Week 1", "Month 1"
    submissionData: Partial<CommitSubmission>,
    submissionId?: string // e.g., "Submission 1" for weekly/monthly
  ): Promise<ServiceResponse<void>> {
    if (frequency === 'daily' || frequency === 'once') {
      return this.updateCommitSubmission(commitId, periodId, submissionData);
    } else if (frequency === 'weekly') {
      return this.updateWeekSubmission(commitId, periodId, submissionId || 'Submission 1', submissionData);
    } else if (frequency === 'monthly') {
      return this.updateMonthSubmission(commitId, periodId, submissionId || 'Submission 1', submissionData);
    }

    return this.createErrorResponse({
      operation: 'update',
      collection: 'commit_submissions',
      originalError: new Error('Invalid frequency type'),
      timestamp: new Date()
    });
  }

  // Weekly submission tracking utilities with daily limit enforcement
  async getWeeklySubmissionStatus(
    commitId: string,
    weekId: string,
    timesPerWeek: number,
    targetDate?: string // Optional target date in YYYY-MM-DD format, defaults to today
  ): Promise<ServiceResponse<{
    submittedCount: number;
    pendingCount: number;
    canSubmit: boolean;
    canSubmitToday: boolean;
    nextSubmissionId: string | null;
    submissions: CommitSubmission[];
    todaySubmissionExists: boolean;
  }>> {
    try {
      // Get all submissions for this week
      const submissionsResult = await this.getWeekSubmissions(commitId, weekId);

      if (!submissionsResult.success) {
        return this.createErrorResponse({
          operation: 'read',
          collection: 'commit_submissions',
          originalError: new Error(`Failed to get week submissions: ${submissionsResult.error}`),
          timestamp: new Date()
        });
      }

      const submissions = submissionsResult.data || [];
      const submittedCount = submissions.filter(s => s.status === 'submitted').length;
      const pendingCount = timesPerWeek - submittedCount;
      const canSubmit = submittedCount < timesPerWeek;

      // Check if user has already submitted on the target date (today by default)
      const checkDate = targetDate || new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const todaySubmissionExists = submissions.some(s =>
        s.status === 'submitted' &&
        s.timestamp &&
        s.timestamp.split('T')[0] === checkDate
      );

      const canSubmitToday = canSubmit && !todaySubmissionExists;

      // Find next available submission slot
      let nextSubmissionId: string | null = null;
      if (canSubmitToday) {
        for (let i = 1; i <= timesPerWeek; i++) {
          const submissionId = `Submission ${i}`;
          const submission = submissions.find(s => s.id === submissionId);
          if (!submission || submission.status === 'upcoming') {
            nextSubmissionId = submissionId;
            break;
          }
        }
      }

      return {
        success: true,
        data: {
          submittedCount,
          pendingCount,
          canSubmit,
          canSubmitToday,
          nextSubmissionId,
          submissions,
          todaySubmissionExists
        }
      };
    } catch (error) {
      return this.createErrorResponse({
        operation: 'read',
        collection: 'commit_submissions',
        originalError: error as Error,
        timestamp: new Date()
      });
    }
  }

  // Calculate current week for a commit
  getCurrentWeekForCommit(startDate: string): { weekNumber: number; weekId: string } {
    const start = new Date(startDate);
    const today = new Date();

    // Set both dates to start of day to avoid time issues
    start.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);

    // Calculate weeks difference
    const weeksDiff = Math.floor((today.getTime() - start.getTime()) / (1000 * 60 * 60 * 24 * 7));
    const weekNumber = Math.max(1, weeksDiff + 1);
    const weekId = `Week ${weekNumber}`;

    return { weekNumber, weekId };
  }

  // Validate if a weekly submission is allowed (with daily limit check)
  async validateWeeklySubmission(
    commitId: string,
    weekId: string,
    timesPerWeek: number
  ): Promise<ServiceResponse<{ canSubmit: boolean; reason?: string }>> {
    try {
      const statusResult = await this.getWeeklySubmissionStatus(commitId, weekId, timesPerWeek);

      if (!statusResult.success) {
        return this.createErrorResponse({
          operation: 'read',
          collection: 'commit_submissions',
          originalError: new Error(`Failed to get weekly status: ${statusResult.error}`),
          timestamp: new Date()
        });
      }

      const status = statusResult.data!;

      if (!status.canSubmit) {
        return {
          success: true,
          data: {
            canSubmit: false,
            reason: `You have already completed all ${status.submittedCount} submissions for this week (limit: ${timesPerWeek})`
          }
        };
      }

      if (status.todaySubmissionExists) {
        return {
          success: true,
          data: {
            canSubmit: false,
            reason: `You have already submitted once today. You can submit again tomorrow.`
          }
        };
      }

      return {
        success: true,
        data: { canSubmit: true }
      };
    } catch (error) {
      return this.createErrorResponse({
        operation: 'read',
        collection: 'commit_submissions',
        originalError: error as Error,
        timestamp: new Date()
      });
    }
  }

  // Get comprehensive weekly commit progress
  async getWeeklyCommitProgress(
    commitId: string,
    startDate: string,
    duration: number,
    timesPerWeek: number
  ): Promise<ServiceResponse<{
    currentWeek: number;
    totalWeeks: number;
    weeklyProgress: Array<{
      weekNumber: number;
      weekId: string;
      submittedCount: number;
      pendingCount: number;
      isCurrentWeek: boolean;
      isCompleted: boolean;
    }>;
  }>> {
    try {
      const { weekNumber: currentWeekNumber } = this.getCurrentWeekForCommit(startDate);
      const weeklyProgress = [];

      // Get progress for all weeks
      for (let week = 1; week <= duration; week++) {
        const weekId = `Week ${week}`;
        const isCurrentWeek = week === currentWeekNumber;

        if (week <= currentWeekNumber) {
          // Get actual submission status for past and current weeks
          const statusResult = await this.getWeeklySubmissionStatus(commitId, weekId, timesPerWeek);

          if (statusResult.success && statusResult.data) {
            const status = statusResult.data;
            weeklyProgress.push({
              weekNumber: week,
              weekId,
              submittedCount: status.submittedCount,
              pendingCount: status.pendingCount,
              isCurrentWeek,
              isCompleted: status.submittedCount >= timesPerWeek
            });
          } else {
            // Week exists but no submissions yet
            weeklyProgress.push({
              weekNumber: week,
              weekId,
              submittedCount: 0,
              pendingCount: timesPerWeek,
              isCurrentWeek,
              isCompleted: false
            });
          }
        } else {
          // Future weeks
          weeklyProgress.push({
            weekNumber: week,
            weekId,
            submittedCount: 0,
            pendingCount: timesPerWeek,
            isCurrentWeek: false,
            isCompleted: false
          });
        }
      }

      return {
        success: true,
        data: {
          currentWeek: currentWeekNumber,
          totalWeeks: duration,
          weeklyProgress
        }
      };
    } catch (error) {
      return this.createErrorResponse({
        operation: 'read',
        collection: 'commit_submissions',
        originalError: error as Error,
        timestamp: new Date()
      });
    }
  }

  // Combined operations
  async createFullCommit(
    userId: string,
    commitData: any // Accept both old and new structures
  ): Promise<ServiceResponse<string>> {
    try {
      // Transform old structure to new structure if needed
      const transformedData = commitData.schedule ? commitData : this.transformToNewStructure(commitData);

      // Create the main commit with userId and setupStatus
      const fullCommitData: Omit<Commit, 'id'> = {
        ...transformedData,
        userId,
        status: 'active',
        setupStatus: false
      } as Omit<Commit, 'id'>;

      const commitResult = await this.createCommit(fullCommitData);
      if (!commitResult.success) {
        return commitResult;
      }

      const commitId = commitResult.data;

      if (!commitId) {
        return this.createErrorResponse({
          operation: 'create',
          collection: 'commits',
          originalError: new Error('Failed to get commit ID from creation result'),
          timestamp: new Date()
        });
      }

      // Create the user commit reference with new structure
      const userCommitData: Omit<UserCommit, 'id'> = {
        commitId,
        status: 'active',
        progress: {
          totalReports: this.calculateTotalReports(commitData),
          completedReports: 0,
          missedReports: 0,
          currentStreak: 0,
          longestStreak: 0
          // lastSubmissionDate and nextDueDate will be set when first submission is made
        },
        financial: {
          totalLost: 0,
          totalAtRisk: commitData.stake?.totalAtRisk || 0
        }
      };

      const userCommitResult = await this.createUserCommit(userId, commitId, userCommitData);
      if (!userCommitResult.success) {
        // Rollback: delete the main commit if user commit creation fails
        await this.deleteCommit(commitId);
        return this.createErrorResponse({
          operation: 'create',
          collection: 'user_commits',
          originalError: new Error(userCommitResult.error || 'Failed to create user commit'),
          timestamp: new Date()
        });
      }

      // Create submissions subcollection based on frequency mode
      const frequency = transformedData.schedule?.frequency || 'daily';
      const totalReports = this.calculateTotalReports(commitData);

      const submissionsResult = await this.createCommitSubmissions(commitId, frequency, totalReports, commitData);
      if (!submissionsResult.success) {
        // Rollback: delete the main commit and user commit if submissions creation fails
        await this.deleteCommit(commitId);
        await this.deleteUserCommit(userId, commitId);
        return this.createErrorResponse({
          operation: 'create',
          collection: 'commit_submissions',
          originalError: new Error(submissionsResult.error || 'Failed to create commit submissions'),
          timestamp: new Date()
        });
      }

      return this.createSuccessResponse(commitId);
    } catch (error) {
      const dbError = this.handleError('create', 'commits', error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async updateCommitStatus(
    userId: string,
    commitId: string,
    status: 'active' | 'completed' | 'failed' | 'cancelled'
  ): Promise<ServiceResponse<void>> {
    try {
      // Update main commit
      const commitResult = await this.updateCommit(commitId, { status });
      if (!commitResult.success) {
        return commitResult;
      }

      // Update user commit
      const userCommitResult = await this.updateUserCommit(userId, commitId, { status });
      if (!userCommitResult.success) {
        return userCommitResult;
      }

      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('update', 'commits', error as Error, commitId);
      return this.createErrorResponse(dbError);
    }
  }

  // Real-time subscriptions
  subscribeToUserCommits(
    userId: string,
    callback: (commits: UserCommit[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getUserCommitPath(userId);
    return this.subscribeToCollection<UserCommit>(collectionPath, callback, {
      orderBy: { field: 'createdAt', direction: 'desc' }
    });
  }

  subscribeToCommit(
    commitId: string,
    callback: (commit: Commit | null) => void
  ): UnsubscribeFunction {
    return this.subscribeToDocument<Commit>(this.COLLECTION_PATH, commitId, callback);
  }

  // Helper methods
  private calculateTotalReports(commitData: any): number {
    // Handle both old and new data structures
    const frequency = commitData.schedule?.frequency || commitData.reportingFrequency;
    const duration = commitData.schedule?.duration || commitData.weekLength;
    const startDate = commitData.schedule?.startDate || commitData.startDate;
    const endDate = commitData.schedule?.endDate || commitData.endDate;

    if (frequency === 'daily') {
      if (endDate && startDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      }
      if (duration) {
        return duration; // For daily, duration is number of days
      }
    } else if (frequency === 'weekly') {
      // For weekly, calculate total reports from financial data if available
      const timesPerWeek = commitData.schedule?.timesPerWeek || commitData.timesPerWeek;
      if (timesPerWeek && duration) {
        return duration * timesPerWeek;
      }
      // Fallback: calculate from stake information
      const totalAtRisk = commitData.stake?.totalAtRisk || commitData.totalStake;
      const amountPerReport = commitData.stake?.amount || commitData.amountPerReport;
      if (totalAtRisk && amountPerReport) {
        return Math.ceil(totalAtRisk / amountPerReport);
      }
      return duration || 1; // Fallback to number of weeks
    } else if (frequency === 'monthly') {
      // For monthly, calculate total reports from financial data if available
      const timesPerMonth = commitData.schedule?.timesPerMonth || commitData.timesPerMonth;
      if (timesPerMonth && duration) {
        return duration * timesPerMonth;
      }
      // Fallback: calculate from stake information
      const totalAtRisk = commitData.stake?.totalAtRisk || commitData.totalStake;
      const amountPerReport = commitData.stake?.amount || commitData.amountPerReport;
      if (totalAtRisk && amountPerReport) {
        return Math.ceil(totalAtRisk / amountPerReport);
      }
      return duration || 1; // Fallback to number of months
    } else if (frequency === 'once') {
      return 1;
    }
    return 0;
  }

  /**
   * Transforms old commit data structure to new structure
   * This helps with backward compatibility and migration
   */
  private transformToNewStructure(oldData: any): Partial<Commit> {
    const scheduleData: any = {
      frequency: oldData.reportingFrequency,
      duration: oldData.weekLength,
      startDate: oldData.startDate,
      endDate: oldData.endDate || oldData.calculatedEndDate,
      deadline: oldData.timingType ? {
        type: oldData.timingType === 'mid-night' ? 'midnight' : oldData.timingType,
        time: oldData.beforeTime || oldData.afterTime,
        startTime: oldData.startTime,
        endTime: oldData.endTime
      } : undefined
    };

    // Add frequency-specific fields to schedule
    if (oldData.reportingFrequency === 'weekly' && oldData.timesPerWeek) {
      scheduleData.timesPerWeek = oldData.timesPerWeek;
    }
    if (oldData.reportingFrequency === 'monthly' && oldData.timesPerMonth) {
      scheduleData.timesPerMonth = oldData.timesPerMonth;
    }

    return {
      title: oldData.commitment,
      schedule: scheduleData,
      evidence: {
        type: oldData.evidenceType,
        config: {
          ...(oldData.locationData && { location: oldData.locationData }),
          ...(oldData.appName && { appName: oldData.appName }),
          ...(oldData.stravaActivity && { activityType: oldData.stravaActivity })
        }
      },
      stake: {
        amount: oldData.amountPerReport || 0,
        destination: this.mapRecipientToDestination(oldData.recipient),
        totalAtRisk: oldData.totalStake || 0,
        paymentRequired: true
      },
      accountability: {
        referee: oldData.referee || 'honor',
        reminderChannels: oldData.programPreferences?.reminderChannels,
        strictnessLevel: oldData.programPreferences?.strictnessLevel
      }
    };
  }

  private mapRecipientToDestination(recipient: string): 'charity' | 'platform' {
    switch (recipient) {
      case 'charity': return 'charity';
      case 'accustom': return 'platform';
      default: return 'platform';
    }
  }

  /**
   * Sorts commits by createdAt date in descending order (newest first)
   * @param commits Array of commits to sort
   * @returns Sorted array of commits
   */
  private sortCommitsByDate(commits: Commit[]): Commit[] {
    return commits.sort((a, b) => {
      const dateA = a.createdAt ? (typeof a.createdAt === 'string' ? new Date(a.createdAt) : a.createdAt.toDate()) : new Date(0);
      const dateB = b.createdAt ? (typeof b.createdAt === 'string' ? new Date(b.createdAt) : b.createdAt.toDate()) : new Date(0);
      return dateB.getTime() - dateA.getTime();
    });
  }

  /**
   * Sanitizes commit data by removing undefined values that would cause Firebase errors
   * @param commitData Raw commit data that may contain undefined values
   * @returns Sanitized commit data safe for Firebase
   */
  private sanitizeCommitData(commitData: any): any {
    const sanitized: any = {};

    // Copy all defined values
    Object.keys(commitData).forEach(key => {
      const value = commitData[key];
      if (value !== undefined && value !== null) {
        // For objects, recursively sanitize
        if (typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
          const sanitizedObject = this.sanitizeCommitData(value);
          // Only add the object if it has properties after sanitization
          if (Object.keys(sanitizedObject).length > 0) {
            sanitized[key] = sanitizedObject;
          }
        } else {
          sanitized[key] = value;
        }
      }
    });

    return sanitized;
  }
}
