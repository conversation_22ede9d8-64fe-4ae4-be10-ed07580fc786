import { BaseService } from './BaseService';
import {
  User,
  UserProgram,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
  DocumentSubscriptionCallback,
} from './types';

export class UserService extends BaseService {
  private readonly COLLECTION_PATH = COLLECTIONS.USERS;

  // User CRUD operations
  async createUser(userData: Omit<User, 'id'>): Promise<ServiceResponse<string>> {
    return this.createDocument<User>(this.COLLECTION_PATH, userData, userData.email);
  }

  async getUserById(userId: string): Promise<ServiceResponse<User | null>> {
    return this.getDocument<User>(this.COLLECTION_PATH, userId);
  }

  async getUserByEmail(email: string): Promise<ServiceResponse<User | null>> {
    return this.getUserById(email); // In this app, email is used as user ID
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<ServiceResponse<void>> {
    return this.updateDocument<User>(this.COLLECTION_PATH, userId, userData);
  }

  async deleteUser(userId: string): Promise<ServiceResponse<void>> {
    return this.deleteDocument(this.COLLECTION_PATH, userId);
  }

  // User profile operations
  async updateUserProfile(
    userId: string,
    profileData: {
      fname?: string;
      lname?: string;
      dateOfBirth?: string;
      gender?: string;
    }
  ): Promise<ServiceResponse<void>> {
    return this.updateUser(userId, profileData);
  }

  async updateUserStreak(userId: string, streakValue: number): Promise<ServiceResponse<void>> {
    return this.updateUser(userId, { streak: streakValue });
  }

  async incrementUserStreak(userId: string, incrementBy: number = 1): Promise<ServiceResponse<void>> {
    try {
      const docRef = this.getDocRef(this.COLLECTION_PATH, userId);
      await this.updateDocument(this.COLLECTION_PATH, userId, {
        streak: this.incrementField(incrementBy),
      });
      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('update', this.COLLECTION_PATH, error as Error, userId);
      return this.createErrorResponse(dbError);
    }
  }

  async setActiveProgram(userId: string, isActive: boolean): Promise<ServiceResponse<void>> {
    return this.updateUser(userId, { active_program: isActive });
  }

  // User programs operations
  async getUserPrograms(userId: string): Promise<ServiceResponse<UserProgram[]>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    return this.getCollection<UserProgram>(collectionPath);
  }

  async addUserProgram(userId: string, programId: string): Promise<ServiceResponse<string>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    const programData: Omit<UserProgram, 'id'> = { programId };
    return this.createDocument<UserProgram>(collectionPath, programData, programId);
  }

  async removeUserProgram(userId: string, programId: string): Promise<ServiceResponse<void>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    return this.deleteDocument(collectionPath, programId);
  }

  async isUserEnrolledInProgram(userId: string, programId: string): Promise<ServiceResponse<boolean>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    const result = await this.getDocument<UserProgram>(collectionPath, programId);

    if (!result.success) {
      return this.createErrorResponse(result as any);
    }

    return this.createSuccessResponse(result.data !== null);
  }

  async archiveUserProgram(userId: string, programId: string): Promise<ServiceResponse<void>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    return this.updateDocument<UserProgram>(collectionPath, programId, { isArchived: true });
  }

  async unarchiveUserProgram(userId: string, programId: string): Promise<ServiceResponse<void>> {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    return this.updateDocument<UserProgram>(collectionPath, programId, { isArchived: false });
  }

  // User search and filtering
  async searchUsersByName(
    searchTerm: string,
    limitCount: number = 20
  ): Promise<ServiceResponse<User[]>> {
    // Note: Firestore doesn't support full-text search natively
    // This is a basic implementation that searches by fname starting with searchTerm
    const options: QueryOptions = {
      where: [
        { field: 'fname', operator: '>=', value: searchTerm },
        { field: 'fname', operator: '<=', value: searchTerm + '\uf8ff' }
      ],
      limit: limitCount,
      orderBy: { field: 'fname', direction: 'asc' }
    };

    return this.getCollection<User>(this.COLLECTION_PATH, options);
  }

  async getUsersWithHighestStreak(limitCount: number = 10): Promise<ServiceResponse<User[]>> {
    const options: QueryOptions = {
      orderBy: { field: 'streak', direction: 'desc' },
      limit: limitCount
    };

    return this.getCollection<User>(this.COLLECTION_PATH, options);
  }

  // Real-time subscriptions
  subscribeToUser(
    userId: string,
    callback: DocumentSubscriptionCallback<User>
  ): UnsubscribeFunction {
    return this.subscribeToDocument<User>(this.COLLECTION_PATH, userId, callback);
  }

  subscribeToUserPrograms(
    userId: string,
    callback: (programs: UserProgram[]) => void
  ): UnsubscribeFunction {
    const collectionPath = `${this.COLLECTION_PATH}/${userId}/${SUBCOLLECTIONS.USER_PROGRAMS}`;
    return this.subscribeToCollection<UserProgram>(collectionPath, callback);
  }

  // Batch operations for user management
  async createMultipleUsers(users: Omit<User, 'id'>[]): Promise<ServiceResponse<string[]>> {
    try {
      const results: string[] = [];
      
      for (const userData of users) {
        const result = await this.createUser(userData);
        if (result.success && result.data) {
          results.push(result.data);
        } else {
          return this.createErrorResponse({
            operation: 'create',
            collection: this.COLLECTION_PATH,
            originalError: new Error(result.error || 'Failed to create user'),
            timestamp: new Date(),
          });
        }
      }
      
      return this.createSuccessResponse(results);
    } catch (error) {
      const dbError = this.handleError('create', this.COLLECTION_PATH, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // User statistics
  async getUserStats(userId: string): Promise<ServiceResponse<{
    totalPrograms: number;
    streak: number;
    joinDate: string;
  }>> {
    try {
      const userResult = await this.getUserById(userId);
      if (!userResult.success || !userResult.data) {
        return this.createErrorResponse({
          operation: 'read',
          collection: this.COLLECTION_PATH,
          originalError: new Error('User not found'),
          timestamp: new Date(),
        });
      }

      const programsResult = await this.getUserPrograms(userId);
      if (!programsResult.success) {
        return this.createErrorResponse(programsResult as any);
      }

      const stats = {
        totalPrograms: programsResult.data?.length || 0,
        streak: userResult.data.streak,
        joinDate: userResult.data.createdAt?.toString() || '',
      };

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.COLLECTION_PATH, error as Error, userId);
      return this.createErrorResponse(dbError);
    }
  }

  // Helper method to validate user data
  validateUserData(userData: Partial<User>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (userData.email && !this.isValidEmail(userData.email)) {
      errors.push('Invalid email format');
    }

    if (userData.fname && userData.fname.trim().length < 2) {
      errors.push('First name must be at least 2 characters');
    }

    if (userData.lname && userData.lname.trim().length < 2) {
      errors.push('Last name must be at least 2 characters');
    }

    if (userData.streak !== undefined && userData.streak < 0) {
      errors.push('Streak cannot be negative');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
