import {
  doc,
  collection,
  getDoc,
  getDocs,
  setDoc,
  updateDoc,
  deleteDoc,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  writeBatch,
  increment,
  DocumentReference,
  CollectionReference,
  Query,
  QuerySnapshot,
  DocumentSnapshot,
  Unsubscribe,
} from 'firebase/firestore';
import { db } from '../../config/firebase';
import {
  ServiceResponse,
  BatchServiceResponse,
  QueryOptions,
  DatabaseError,
  DatabaseOperation,
  BatchOperation,
  SubscriptionCallback,
  DocumentSubscriptionCallback,
  UnsubscribeFunction,
} from './types';

export class BaseService {
  protected db = db;

  // Document reference helpers
  protected getDocRef(collectionPath: string, documentId: string): DocumentReference {
    return doc(this.db, collectionPath, documentId);
  }

  protected getCollectionRef(collectionPath: string): CollectionReference {
    return collection(this.db, collectionPath);
  }

  // Error handling
  protected handleError(
    operation: DatabaseOperation,
    collectionPath: string,
    error: Error,
    documentId?: string
  ): DatabaseError {
    const dbError: DatabaseError = {
      operation,
      collection: collectionPath,
      documentId,
      originalError: error,
      timestamp: new Date(),
    };

    // Safe error logging to prevent indexOf errors
    const errorMessage = (() => {
      try {
        // Handle null/undefined error
        if (!error) {
          return 'Null or undefined error';
        }

        // Handle error objects
        if (typeof error === 'object') {
          // Check if it has a message property
          if ('message' in error && error.message) {
            // Ensure message is a string to prevent indexOf errors
            const message = String(error.message);
            return message || 'Empty error message';
          }

          // Check if it has a toString method
          if (typeof error.toString === 'function') {
            const stringified = error.toString();
            return stringified !== '[object Object]' ? stringified : 'Error object without readable message';
          }

          return 'Error object without message property';
        }

        // Handle string errors
        if (typeof error === 'string') {
          return error || 'Empty string error';
        }

        // Handle other types
        return `Unknown error type: ${typeof error}`;
      } catch (e) {
        return 'Error processing error message';
      }
    })();

    console.error(`(NOBRIDGE) ERROR Database ${operation} error in ${collectionPath}:`, errorMessage);
    return dbError;
  }

  protected createErrorResponse<T>(error: DatabaseError): ServiceResponse<T> {
    // Safe error message creation to prevent indexOf errors
    const errorMessage = (() => {
      try {
        const operation = error.operation || 'operation';

        // Safe handling of original error message
        let originalMessage = 'Unknown error';
        if (error.originalError) {
          if (typeof error.originalError === 'object' && 'message' in error.originalError) {
            originalMessage = String(error.originalError.message || 'Error without message');
          } else if (typeof error.originalError === 'string') {
            originalMessage = error.originalError;
          } else {
            originalMessage = 'Non-string error object';
          }
        }

        return `${operation} failed: ${originalMessage}`;
      } catch (e) {
        return 'Database operation failed: Error creating error message';
      }
    })();

    return {
      success: false,
      error: errorMessage,
    };
  }

  protected createSuccessResponse<T>(data: T): ServiceResponse<T> {
    return {
      success: true,
      data,
    };
  }

  // Query builder
  protected buildQuery(collectionRef: CollectionReference, options?: QueryOptions): Query {
    let q: Query = collectionRef;

    if (options?.where) {
      options.where.forEach(({ field, operator, value }) => {
        q = query(q, where(field, operator, value));
      });
    }

    if (options?.orderBy) {
      q = query(q, orderBy(options.orderBy.field, options.orderBy.direction));
    }

    if (options?.limit) {
      q = query(q, limit(options.limit));
    }

    return q;
  }

  // Basic CRUD operations
  protected async getDocument<T>(
    collectionPath: string,
    documentId: string
  ): Promise<ServiceResponse<T | null>> {
    try {
      const docRef = this.getDocRef(collectionPath, documentId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return this.createSuccessResponse({ id: docSnap.id, ...docSnap.data() } as T);
      } else {
        return this.createSuccessResponse(null);
      }
    } catch (error) {
      const dbError = this.handleError('read', collectionPath, error as Error, documentId);
      return this.createErrorResponse(dbError);
    }
  }

  protected async getCollection<T>(
    collectionPath: string,
    options?: QueryOptions
  ): Promise<ServiceResponse<T[]>> {
    try {
      const collectionRef = this.getCollectionRef(collectionPath);
      const q = this.buildQuery(collectionRef, options);
      const querySnapshot = await getDocs(q);
      
      const documents: T[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as T[];

      return this.createSuccessResponse(documents);
    } catch (error) {
      const dbError = this.handleError('query', collectionPath, error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  protected async createDocument<T>(
    collectionPath: string,
    data: Partial<T>,
    documentId?: string
  ): Promise<ServiceResponse<string>> {
    try {
      const timestamp = serverTimestamp();
      const documentData = {
        ...data,
        createdAt: timestamp,
        updatedAt: timestamp,
      };

      if (documentId) {
        const docRef = this.getDocRef(collectionPath, documentId);
        await setDoc(docRef, documentData);
        return this.createSuccessResponse(documentId);
      } else {
        const collectionRef = this.getCollectionRef(collectionPath);
        const docRef = await addDoc(collectionRef, documentData);
        return this.createSuccessResponse(docRef.id);
      }
    } catch (error) {
      const dbError = this.handleError('create', collectionPath, error as Error, documentId);
      return this.createErrorResponse(dbError);
    }
  }

  protected async updateDocument<T>(
    collectionPath: string,
    documentId: string,
    data: Partial<T>
  ): Promise<ServiceResponse<void>> {
    try {
      const docRef = this.getDocRef(collectionPath, documentId);
      const updateData = {
        ...data,
        updatedAt: serverTimestamp(),
      };
      
      await updateDoc(docRef, updateData);
      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('update', collectionPath, error as Error, documentId);
      return this.createErrorResponse(dbError);
    }
  }

  protected async deleteDocument(
    collectionPath: string,
    documentId: string
  ): Promise<ServiceResponse<void>> {
    try {
      const docRef = this.getDocRef(collectionPath, documentId);
      await deleteDoc(docRef);
      return this.createSuccessResponse(undefined);
    } catch (error) {
      const dbError = this.handleError('delete', collectionPath, error as Error, documentId);
      return this.createErrorResponse(dbError);
    }
  }

  // Batch operations
  protected async executeBatch(operations: BatchOperation[]): Promise<BatchServiceResponse> {
    try {
      const batch = writeBatch(this.db);
      const results: Array<{ success: boolean; error?: string }> = [];

      operations.forEach((operation, index) => {
        try {
          const docRef = operation.documentId 
            ? this.getDocRef(operation.collection, operation.documentId)
            : doc(this.getCollectionRef(operation.collection));

          switch (operation.type) {
            case 'set':
              batch.set(docRef, {
                ...operation.data,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp(),
              });
              break;
            case 'update':
              batch.update(docRef, {
                ...operation.data,
                updatedAt: serverTimestamp(),
              });
              break;
            case 'delete':
              batch.delete(docRef);
              break;
          }
          results.push({ success: true });
        } catch (error) {
          results.push({ 
            success: false, 
            error: `Operation ${index} failed: ${(error as Error).message}` 
          });
        }
      });

      await batch.commit();
      return { success: true, results };
    } catch (error) {
      return {
        success: false,
        results: [],
        error: `Batch operation failed: ${(error as Error).message}`,
      };
    }
  }

  // Real-time subscriptions
  protected subscribeToCollection<T>(
    collectionPath: string,
    callback: SubscriptionCallback<T>,
    options?: QueryOptions
  ): UnsubscribeFunction {
    const collectionRef = this.getCollectionRef(collectionPath);
    const q = this.buildQuery(collectionRef, options);

    return onSnapshot(q, (querySnapshot) => {
      const documents: T[] = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
      })) as T[];
      callback(documents);
    });
  }

  protected subscribeToDocument<T>(
    collectionPath: string,
    documentId: string,
    callback: DocumentSubscriptionCallback<T>
  ): UnsubscribeFunction {
    const docRef = this.getDocRef(collectionPath, documentId);

    return onSnapshot(docRef, (docSnap) => {
      if (docSnap.exists()) {
        callback({ id: docSnap.id, ...docSnap.data() } as T);
      } else {
        callback(null);
      }
    });
  }

  // Utility methods
  protected incrementField(value: number = 1) {
    return increment(value);
  }

  protected getServerTimestamp() {
    return serverTimestamp();
  }
}
