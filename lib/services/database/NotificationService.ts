import { BaseService } from './BaseService';
import {
  Notification,
  ServiceResponse,
  QueryOptions,
  COLLECTIONS,
  SUBCOLLECTIONS,
  UnsubscribeFunction,
} from './types';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { getApp } from 'firebase/app';

export class NotificationService extends BaseService {
  private getNotificationPath(userId: string): string {
    return `${COLLECTIONS.USERS}/${userId}/${SUBCOLLECTIONS.USER_NOTIFICATIONS}`;
  }

  // Notification CRUD operations
  async createNotification(
    userId: string,
    notificationData: Omit<Notification, 'id' | 'time' | 'read'>,
    options?: {
      sendPush?: boolean; // Whether to send push notification (default: true)
      pushMessageType?: 'notification' | 'data'; // FCM message type (default: 'notification')
    }
  ): Promise<ServiceResponse<string>> {
    const { sendPush = true, pushMessageType = 'notification' } = options || {};

    const collectionPath = this.getNotificationPath(userId);
    const fullNotificationData: Omit<Notification, 'id'> = {
      ...notificationData,
      time: new Date().toISOString(),
      read: false,
      pushSent: false,
    };

    // First create the in-app notification
    const result = await this.createDocument<Notification>(collectionPath, fullNotificationData);

    // If successful and push notifications are enabled, send push notification
    if (result.success && sendPush) {
      try {
        await this.sendPushNotification(userId, {
          title: notificationData.title,
          message: notificationData.message,
          type: notificationData.type,
          priority: notificationData.priority,
          data: notificationData.data,
          imageUrl: notificationData.imageUrl,
        }, pushMessageType);

        // Update the notification to mark push as sent
        if (result.data) {
          await this.updateNotificationPushStatus(userId, result.data, true);
        }
      } catch (error) {
        console.error('Error sending push notification:', error);
        // Don't fail the entire operation if push notification fails
      }
    }

    return result;
  }

  /**
   * Send a push notification via Firebase Cloud Functions
   */
  private async sendPushNotification(
    userId: string,
    notification: {
      title: string;
      message: string;
      type: string;
      priority: string;
      data?: Record<string, string>;
      imageUrl?: string;
    },
    messageType: 'notification' | 'data' = 'notification'
  ): Promise<void> {
    try {
      const app = getApp();
      const functions = getFunctions(app);
      const sendPushNotification = httpsCallable(functions, 'sendPushNotification');

      await sendPushNotification({
        userId,
        notification,
        messageType,
      });
    } catch (error) {
      console.error('Error calling sendPushNotification function:', error);
      throw error;
    }
  }

  /**
   * Update the push notification status for a notification
   */
  private async updateNotificationPushStatus(
    userId: string,
    notificationId: string,
    pushSent: boolean
  ): Promise<void> {
    try {
      const collectionPath = this.getNotificationPath(userId);
      const updateData = {
        pushSent,
        pushSentAt: pushSent ? new Date().toISOString() : undefined,
      };

      await this.updateDocument(collectionPath, notificationId, updateData);
    } catch (error) {
      console.error('Error updating notification push status:', error);
    }
  }

  async getNotification(
    userId: string,
    notificationId: string
  ): Promise<ServiceResponse<Notification | null>> {
    const collectionPath = this.getNotificationPath(userId);
    return this.getDocument<Notification>(collectionPath, notificationId);
  }

  async updateNotification(
    userId: string,
    notificationId: string,
    notificationData: Partial<Notification>
  ): Promise<ServiceResponse<void>> {
    const collectionPath = this.getNotificationPath(userId);
    return this.updateDocument<Notification>(collectionPath, notificationId, notificationData);
  }

  async deleteNotification(userId: string, notificationId: string): Promise<ServiceResponse<void>> {
    const collectionPath = this.getNotificationPath(userId);
    return this.deleteDocument(collectionPath, notificationId);
  }

  // Notification management
  async markAsRead(userId: string, notificationId: string): Promise<ServiceResponse<void>> {
    return this.updateNotification(userId, notificationId, { read: true });
  }

  async markAsUnread(userId: string, notificationId: string): Promise<ServiceResponse<void>> {
    return this.updateNotification(userId, notificationId, { read: false });
  }

  async markAllAsRead(userId: string): Promise<ServiceResponse<void>> {
    try {
      const unreadResult = await this.getUnreadNotifications(userId);
      if (!unreadResult.success || !unreadResult.data) {
        return this.createErrorResponse(unreadResult as any);
      }

      const operations = unreadResult.data.map(notification => ({
        type: 'update' as const,
        collection: this.getNotificationPath(userId),
        documentId: notification.id!,
        data: { read: true },
      }));

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'update',
          collection: this.getNotificationPath(userId),
          originalError: new Error(result.error || 'Failed to mark all as read'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('update', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Notification querying
  async getAllNotifications(userId: string): Promise<ServiceResponse<Notification[]>> {
    const collectionPath = this.getNotificationPath(userId);
    const options: QueryOptions = {
      orderBy: { field: 'time', direction: 'desc' }
    };
    return this.getCollection<Notification>(collectionPath, options);
  }

  async getUnreadNotifications(userId: string): Promise<ServiceResponse<Notification[]>> {
    try {
      // Get all notifications and filter client-side to avoid index requirements
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const unreadNotifications = (allResult.data || [])
        .filter(notification => !notification.read)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return this.createSuccessResponse(unreadNotifications);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getNotificationsByType(
    userId: string,
    type: Notification['type']
  ): Promise<ServiceResponse<Notification[]>> {
    try {
      // Get all notifications and filter client-side to avoid index requirements
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const filteredNotifications = (allResult.data || [])
        .filter(notification => notification.type === type)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return this.createSuccessResponse(filteredNotifications);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getNotificationsByPriority(
    userId: string,
    priority: Notification['priority']
  ): Promise<ServiceResponse<Notification[]>> {
    try {
      // Get all notifications and filter client-side to avoid index requirements
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const filteredNotifications = (allResult.data || [])
        .filter(notification => notification.priority === priority)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());

      return this.createSuccessResponse(filteredNotifications);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async getRecentNotifications(
    userId: string,
    limitCount: number = 20
  ): Promise<ServiceResponse<Notification[]>> {
    const collectionPath = this.getNotificationPath(userId);
    const options: QueryOptions = {
      orderBy: { field: 'time', direction: 'desc' },
      limit: limitCount
    };
    return this.getCollection<Notification>(collectionPath, options);
  }

  // Notification statistics
  async getUnreadCount(userId: string): Promise<ServiceResponse<number>> {
    const result = await this.getUnreadNotifications(userId);
    if (!result.success) {
      return this.createErrorResponse(result as any);
    }
    return this.createSuccessResponse(result.data?.length || 0);
  }

  async getNotificationStats(userId: string): Promise<ServiceResponse<{
    total: number;
    unread: number;
    byType: Record<Notification['type'], number>;
    byPriority: Record<Notification['priority'], number>;
  }>> {
    try {
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success) {
        return this.createErrorResponse(allResult as any);
      }

      const notifications = allResult.data || [];
      const total = notifications.length;
      const unread = notifications.filter(n => !n.read).length;

      const byType: Record<Notification['type'], number> = {
        account: 0,
        program: 0,
        points: 0,
        reminder: 0,
      };

      const byPriority: Record<Notification['priority'], number> = {
        low: 0,
        medium: 0,
        high: 0,
      };

      notifications.forEach(notification => {
        byType[notification.type]++;
        byPriority[notification.priority]++;
      });

      const stats = {
        total,
        unread,
        byType,
        byPriority,
      };

      return this.createSuccessResponse(stats);
    } catch (error) {
      const dbError = this.handleError('read', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Real-time subscriptions
  subscribeToNotifications(
    userId: string,
    callback: (notifications: Notification[]) => void
  ): UnsubscribeFunction {
    const collectionPath = this.getNotificationPath(userId);
    const options: QueryOptions = {
      orderBy: { field: 'time', direction: 'desc' }
    };
    return this.subscribeToCollection<Notification>(collectionPath, callback, options);
  }

  subscribeToUnreadNotifications(
    userId: string,
    callback: (notifications: Notification[]) => void
  ): UnsubscribeFunction {
    // Subscribe to all notifications and filter client-side to avoid index requirements
    return this.subscribeToNotifications(userId, (allNotifications) => {
      const unreadNotifications = allNotifications
        .filter(notification => !notification.read)
        .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
      callback(unreadNotifications);
    });
  }

  subscribeToUnreadCount(
    userId: string,
    callback: (count: number) => void
  ): UnsubscribeFunction {
    return this.subscribeToUnreadNotifications(userId, (notifications) => {
      callback(notifications.length);
    });
  }

  // Bulk operations
  async deleteAllNotifications(userId: string): Promise<ServiceResponse<void>> {
    try {
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success || !allResult.data) {
        return this.createErrorResponse(allResult as any);
      }

      const operations = allResult.data.map(notification => ({
        type: 'delete' as const,
        collection: this.getNotificationPath(userId),
        documentId: notification.id!,
      }));

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'delete',
          collection: this.getNotificationPath(userId),
          originalError: new Error(result.error || 'Failed to delete all notifications'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('delete', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  async deleteReadNotifications(userId: string): Promise<ServiceResponse<void>> {
    try {
      const allResult = await this.getAllNotifications(userId);
      if (!allResult.success || !allResult.data) {
        return this.createErrorResponse(allResult as any);
      }

      const readNotifications = allResult.data.filter(n => n.read);
      const operations = readNotifications.map(notification => ({
        type: 'delete' as const,
        collection: this.getNotificationPath(userId),
        documentId: notification.id!,
      }));

      if (operations.length === 0) {
        return this.createSuccessResponse(undefined);
      }

      const result = await this.executeBatch(operations);
      if (result.success) {
        return this.createSuccessResponse(undefined);
      } else {
        return this.createErrorResponse({
          operation: 'delete',
          collection: this.getNotificationPath(userId),
          originalError: new Error(result.error || 'Failed to delete read notifications'),
          timestamp: new Date(),
        });
      }
    } catch (error) {
      const dbError = this.handleError('delete', this.getNotificationPath(userId), error as Error);
      return this.createErrorResponse(dbError);
    }
  }

  // Helper methods for common notification types
  async createWelcomeNotification(userId: string, userName: string): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Welcome to Habit Royale!",
      message: `Hello ${userName}! Your account has been created successfully! Welcome onboard!`,
      type: "account",
      priority: "high",
      data: {
        action: "view_profile",
        userId: userId,
      }
    });
  }

  async createProgramJoinedNotification(
    userId: string,
    programName: string,
    startDate: string,
    programId?: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Program Joined Successfully!",
      message: `You've joined "${programName}". The challenge begins on ${startDate}. Get ready!`,
      type: "program",
      priority: "medium",
      data: {
        action: "view_program",
        programId: programId || "",
        programName: programName,
      }
    });
  }

  async createSetupReminderNotification(
    userId: string,
    programName: string,
    programId?: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Set up your Account Now",
      message: `Go to Progress and connect your account for "${programName}" now!`,
      type: "reminder",
      priority: "high",
      data: {
        action: "setup_integration",
        programName: programName,
        programId: programId || "",
      }
    });
  }

  async createSetupCompleteNotification(
    userId: string,
    category: string,
    programId?: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Account Setup Complete!",
      message: `Your ${category} account is now connected and ready to go. Good luck for the challenge!`,
      type: "account",
      priority: "high",
      data: {
        action: "view_progress",
        category: category,
        programId: programId || "",
      }
    });
  }

  async createDisputeSubmittedNotification(
    userId: string,
    subject: string,
    programName: string
  ): Promise<ServiceResponse<string>> {
    return this.createNotification(userId, {
      title: "Dispute Submitted Successfully",
      message: `Your dispute regarding "${subject}" for ${programName} has been recorded. We'll review it shortly.`,
      type: "program",
      priority: "medium"
    });
  }
}
