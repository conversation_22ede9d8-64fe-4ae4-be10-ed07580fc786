import { firestoreService } from './database/FirestoreService';
import { getId } from '../utils/variables';

export interface SubscriptionStatus {
  isPremium: boolean;
  subscriptionStatus?: 'active' | 'inactive' | 'cancelled' | 'expired';
  subscriptionId?: string;
  subscriptionStartDate?: string;
  subscriptionEndDate?: string;
}

class SubscriptionService {
  private cache: Map<string, { data: SubscriptionStatus; timestamp: number }> = new Map();
  private currentUserCache: { data: SubscriptionStatus; timestamp: number } | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache
  private subscribers: Set<(status: SubscriptionStatus) => void> = new Set();
  /**
   * Check if the current user has an active premium subscription (with caching)
   */
  async checkUserPremiumStatus(forceRefresh = false): Promise<SubscriptionStatus> {
    try {
      const userId = await getId();
      if (!userId) {
        return { isPremium: false };
      }

      // Check cache first
      if (!forceRefresh) {
        const cached = this.cache.get(userId);
        if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
          return cached.data;
        }
      }

      const userResult = await firestoreService.users.getUserById(userId);
      if (!userResult.success || !userResult.data) {
        const fallbackStatus = { isPremium: false };
        this.updateCache(userId, fallbackStatus);
        return fallbackStatus;
      }

      const user = userResult.data;
      const isPremium = user.isPremium === true && user.subscriptionStatus === 'active';

      const status: SubscriptionStatus = {
        isPremium,
        subscriptionStatus: user.subscriptionStatus,
        subscriptionId: user.subscriptionId,
        subscriptionStartDate: user.subscriptionStartDate,
        subscriptionEndDate: user.subscriptionEndDate,
      };

      // Update cache and notify subscribers
      this.updateCache(userId, status);
      this.updateCurrentUserCache(status);
      this.notifySubscribers(status);

      return status;
    } catch (error) {
      console.error('Error checking premium status:', error);
      return { isPremium: false };
    }
  }

  /**
   * Get cached subscription status (instant, no database call)
   */
  getCachedStatus(): SubscriptionStatus | null {
    try {
      if (this.currentUserCache && Date.now() - this.currentUserCache.timestamp < this.CACHE_DURATION) {
        return this.currentUserCache.data;
      }
      return null;
    } catch {
      return null;
    }
  }

  /**
   * Get cached subscription status for specific user
   */
  getCachedStatusForUser(userId: string): SubscriptionStatus | null {
    try {
      const cached = this.cache.get(userId);
      if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
        return cached.data;
      }
      return null;
    } catch {
      return null;
    }
  }

  /**
   * Update user subscription status after successful payment
   */
  async updateUserSubscription(
    subscriptionId: string,
    status: 'active' | 'inactive' | 'cancelled' | 'expired',
    startDate?: string,
    endDate?: string
  ): Promise<boolean> {
    try {
      const userId = await getId();
      if (!userId) {
        return false;
      }

      const updateData: any = {
        isPremium: status === 'active',
        subscriptionStatus: status,
        subscriptionId,
        updatedAt: new Date().toISOString(),
      };

      if (startDate) {
        updateData.subscriptionStartDate = startDate;
      }

      if (endDate) {
        updateData.subscriptionEndDate = endDate;
      }

      const result = await firestoreService.users.updateUser(userId, updateData);

      if (result.success) {
        // Update cache immediately
        const newStatus: SubscriptionStatus = {
          isPremium: status === 'active',
          subscriptionStatus: status,
          subscriptionId,
          subscriptionStartDate: startDate,
          subscriptionEndDate: endDate,
        };
        this.updateCache(userId, newStatus);
        this.updateCurrentUserCache(newStatus);
        this.notifySubscribers(newStatus);
      }

      return result.success;
    } catch (error) {
      console.error('Error updating subscription:', error);
      return false;
    }
  }

  /**
   * Cancel user subscription
   */
  async cancelUserSubscription(): Promise<boolean> {
    try {
      const userId = await getId();
      if (!userId) {
        return false;
      }

      const result = await firestoreService.users.updateUser(userId, {
        isPremium: false,
        subscriptionStatus: 'cancelled',
        updatedAt: new Date().toISOString(),
      });

      if (result.success) {
        // Update cache immediately
        const newStatus: SubscriptionStatus = {
          isPremium: false,
          subscriptionStatus: 'cancelled',
        };
        this.updateCache(userId, newStatus);
        this.updateCurrentUserCache(newStatus);
        this.notifySubscribers(newStatus);
      }

      return result.success;
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      return false;
    }
  }

  /**
   * Check if subscription is expired and update status if needed
   */
  async checkAndUpdateExpiredSubscription(): Promise<void> {
    try {
      const status = await this.checkUserPremiumStatus();

      if (status.subscriptionEndDate && status.subscriptionStatus === 'active') {
        const endDate = new Date(status.subscriptionEndDate);
        const now = new Date();

        if (now > endDate) {
          // Subscription has expired, update status
          await this.updateUserSubscription(
            status.subscriptionId || '',
            'expired'
          );
        }
      }
    } catch (error) {
      console.error('Error checking expired subscription:', error);
    }
  }

  /**
   * Subscribe to subscription status changes
   */
  subscribe(callback: (status: SubscriptionStatus) => void): () => void {
    this.subscribers.add(callback);

    // Return unsubscribe function
    return () => {
      this.subscribers.delete(callback);
    };
  }

  /**
   * Clear cache for user
   */
  clearCache(userId?: string): void {
    if (userId) {
      this.cache.delete(userId);
    } else {
      this.cache.clear();
      this.currentUserCache = null;
    }
  }

  /**
   * Private method to update cache
   */
  private updateCache(userId: string, status: SubscriptionStatus): void {
    this.cache.set(userId, {
      data: status,
      timestamp: Date.now(),
    });
  }

  /**
   * Private method to update current user cache
   */
  private updateCurrentUserCache(status: SubscriptionStatus): void {
    this.currentUserCache = {
      data: status,
      timestamp: Date.now(),
    };
  }

  /**
   * Private method to notify subscribers
   */
  private notifySubscribers(status: SubscriptionStatus): void {
    this.subscribers.forEach(callback => {
      try {
        callback(status);
      } catch (error) {
        console.error('Error notifying subscription subscriber:', error);
      }
    });
  }
}

export const subscriptionService = new SubscriptionService();
