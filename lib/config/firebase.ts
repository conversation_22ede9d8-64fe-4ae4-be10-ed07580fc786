import { Platform } from "react-native";
import { initializeApp } from "firebase/app";
import { getAuth, initializeAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import ReactNativeAsyncStorage from "@react-native-async-storage/async-storage";
import firebase from "firebase/compat/app";
import { getStorage } from "firebase/storage";

// Your firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDOBL2VqjFQmYy4hv2qdLnoJdGhT9VMTwE",
  authDomain: "betonself.firebaseapp.com",
  databaseURL:
    "https://betonself-default-rtdb.asia-southeast1.firebasedatabase.app",
  projectId: "betonself",
  storageBucket: "betonself.appspot.com",
  messagingSenderId: "851427087834",
  appId: "1:851427087834:web:fb4af79375d924ae3b9ce3",
  measurementId: "G-P2VGJ99CSK",
};

// Initialize Firebase app
const app = initializeApp(firebaseConfig);

// Conditionally require the correct version of getReactNativePersistence
let getReactNativePersistenceFunc: any;
if (Platform.OS === "web") {
  // When running on desktop PC browser (web), use the module from '@firebase/auth/dist/rn/index.js'
  getReactNativePersistenceFunc = require("@firebase/auth/dist/rn/index.js")
    .getReactNativePersistence;
} else {
  // Otherwise (mobile), use the one from 'firebase/auth'
  getReactNativePersistenceFunc = require("firebase/auth").getReactNativePersistence;
}

// Initialize auth with the chosen persistence
initializeAuth(app, {
  persistence: getReactNativePersistenceFunc(ReactNativeAsyncStorage),
});

export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage();

export { firebase };
