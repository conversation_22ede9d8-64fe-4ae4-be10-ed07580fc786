// Payment configuration
// Replace these with your actual Stripe keys from the Stripe Dashboard

export const STRIPE_CONFIG = {
  // Publishable key (safe to expose in client-side code)
  PUBLISHABLE_KEY: process.env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY || 'pk_test_51PT0BiEaVxDXnS46eQNplkbLNVbpT6oEBQR3sEtVJk5djbYo6H9ejkbfBiPqTNgCdzD85DpyxArml1edaErA53Mo00efIG4BsP',

  // Merchant identifier for Apple Pay (iOS only)
  MERCHANT_IDENTIFIER: 'merchant.com.accustom',

  // Test environment flag
  TEST_ENV: __DEV__, // Use test environment in development
};

// Backend API configuration
export const API_CONFIG = {
  // Replace with your actual backend API URL
  BASE_URL: process.env.EXPO_PUBLIC_API_URL || 'https://your-backend-api.com',
  
  // API endpoints
  ENDPOINTS: {
    CREATE_PAYMENT_INTENT: '/create-payment-intent',
    CONFIRM_PAYMENT: '/confirm-payment',
    WEBHOOK: '/stripe-webhook',
  },
};

// Payment method configuration
export const PAYMENT_METHODS = {
  STRIPE: {
    enabled: true,
    name: 'Credit/Debit Card',
    icon: 'credit-card',
  },
  APPLE_PAY: {
    enabled: true,
    name: 'Apple Pay',
    icon: 'apple',
    platforms: ['ios'],
  },
  GOOGLE_PAY: {
    enabled: true,
    name: 'Google Pay',
    icon: 'google',
    platforms: ['android'],
  },
};

// Currency configuration
export const CURRENCY_CONFIG = {
  DEFAULT: 'usd',
  SUPPORTED: ['usd', 'eur', 'gbp'],
  DISPLAY_SYMBOL: '$',
};

// Fee configuration
export const FEE_CONFIG = {
  // Platform fee percentage (e.g., 0.12 = 12%)
  PLATFORM_FEE_PERCENTAGE: 0.12,
  
  // Stripe processing fee (varies by region and payment method)
  STRIPE_FEE_PERCENTAGE: 0.029, // 2.9%
  STRIPE_FEE_FIXED: 30, // 30 cents in cents
};

// Validation helpers
export const validateStripeConfig = (): boolean => {
  const { PUBLISHABLE_KEY } = STRIPE_CONFIG;
  
  if (!PUBLISHABLE_KEY || PUBLISHABLE_KEY === 'pk_test_51234567890abcdef') {
    console.warn('⚠️ Stripe publishable key not configured properly');
    return false;
  }
  
  if (!PUBLISHABLE_KEY.startsWith('pk_')) {
    console.error('❌ Invalid Stripe publishable key format');
    return false;
  }
  
  return true;
};

export const validateAPIConfig = (): boolean => {
  const { BASE_URL } = API_CONFIG;
  
  if (!BASE_URL || BASE_URL === 'https://your-backend-api.com') {
    console.warn('⚠️ Backend API URL not configured properly');
    return false;
  }
  
  try {
    new URL(BASE_URL);
    return true;
  } catch {
    console.error('❌ Invalid backend API URL format');
    return false;
  }
};

// Helper function to calculate total amount including fees
export const calculateTotalAmount = (baseAmount: number): {
  baseAmount: number;
  platformFee: number;
  processingFee: number;
  totalAmount: number;
} => {
  const platformFee = Math.round(baseAmount * FEE_CONFIG.PLATFORM_FEE_PERCENTAGE);
  const processingFee = Math.round(baseAmount * FEE_CONFIG.STRIPE_FEE_PERCENTAGE) + FEE_CONFIG.STRIPE_FEE_FIXED;
  const totalAmount = baseAmount + platformFee + processingFee;
  
  return {
    baseAmount,
    platformFee,
    processingFee,
    totalAmount,
  };
};

// Helper function to format currency
export const formatCurrency = (amountInCents: number, currency: string = CURRENCY_CONFIG.DEFAULT): string => {
  const amount = amountInCents / 100;
  
  switch (currency.toLowerCase()) {
    case 'usd':
      return `$${amount.toFixed(2)}`;
    case 'eur':
      return `€${amount.toFixed(2)}`;
    case 'gbp':
      return `£${amount.toFixed(2)}`;
    default:
      return `${amount.toFixed(2)} ${currency.toUpperCase()}`;
  }
};
