// GitHub OAuth configuration
// To set up GitHub OAuth:
// 1. Go to GitHub Settings > Developer settings > OAuth Apps
// 2. Create a new OAuth App
// 3. Set Authorization callback URL to your app's redirect URI
// 4. Copy the Client ID and Client Secret
// 5. Add them to your environment variables or replace the defaults below

export const GITHUB_CONFIG = {
  // Use single OAuth App for both development and production
  CLIENT_ID: process.env.EXPO_PUBLIC_GITHUB_CLIENT_ID || '********************',
  CLIENT_SECRET: process.env.EXPO_PUBLIC_GITHUB_CLIENT_SECRET || '47ed9ccda4febbb4891a715064030a61136f9cb1',

  // OAuth scopes required for the app
  SCOPES: ['repo', 'user:email'],

  // Minimum commits required for verification
  MIN_COMMITS: 1,

  // Verification window in hours (how far back to check for commits)
  VERIFICATION_WINDOW_HOURS: 24,

  // Cloud Function URL for OAuth callback
  CLOUD_FUNCTION_URL: 'https://githuboauthcallback-5zdo6ysy2a-uc.a.run.app',
};

// Instructions for setting up GitHub OAuth:
/*
1. Go to https://github.com/settings/developers
2. Click "New OAuth App"
3. Fill in the application details:
   - Application name: "Accustom"
   - Homepage URL: Your app's homepage
   - Authorization callback URL:
     - For mobile: Use your app's custom scheme (e.g., "accustom://github-auth")
     - For web: Use your domain (e.g., "https://yourapp.com/auth/github/callback")
4. Click "Register application"
5. Copy the Client ID and generate a Client Secret
6. Add these to your environment variables:
   - EXPO_PUBLIC_GITHUB_CLIENT_ID=your_client_id
   - EXPO_PUBLIC_GITHUB_CLIENT_SECRET=your_client_secret

Note: Keep your Client Secret secure and never commit it to version control.
For production, use environment variables or a secure configuration service.
*/
