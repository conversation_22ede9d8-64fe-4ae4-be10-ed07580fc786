// Mock payment API for development and testing
// This simulates the backend API responses for payment processing

export interface MockPaymentIntentResponse {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'succeeded' | 'canceled';
  metadata: {
    programId: string;
    userId: string;
  };
}

export interface MockPaymentConfirmationResponse {
  id: string;
  status: 'succeeded' | 'failed' | 'canceled';
  amount_received: number;
  currency: string;
  payment_method: {
    id: string;
    type: string;
  };
}

// Mock delay to simulate network latency
const mockDelay = (ms: number = 1000): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// Generate mock payment intent ID
const generateMockPaymentIntentId = (): string => {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `pi_mock_${timestamp}_${random}`;
};

// Generate mock client secret
const generateMockClientSecret = (paymentIntentId: string): string => {
  return `${paymentIntentId}_secret_${Math.random().toString(36).substring(2, 10)}`;
};

// Mock create payment intent
export const mockCreatePaymentIntent = async (
  amount: number,
  currency: string,
  description: string,
  metadata: { programId: string; userId: string }
): Promise<MockPaymentIntentResponse> => {
  await mockDelay(800); // Simulate API call delay

  // Simulate occasional API errors for testing
  if (Math.random() < 0.05) { // 5% chance of error
    throw new Error('Mock API Error: Payment intent creation failed');
  }

  const paymentIntentId = generateMockPaymentIntentId();
  const clientSecret = generateMockClientSecret(paymentIntentId);

  return {
    id: paymentIntentId,
    client_secret: clientSecret,
    amount,
    currency,
    status: 'requires_payment_method',
    metadata,
  };
};

// Mock confirm payment
export const mockConfirmPayment = async (
  paymentIntentId: string,
  paymentMethodType: 'card' | 'google_pay' | 'apple_pay' = 'card'
): Promise<MockPaymentConfirmationResponse> => {
  await mockDelay(1500); // Simulate payment processing delay

  // Simulate payment failures for testing
  if (Math.random() < 0.1) { // 10% chance of failure
    return {
      id: paymentIntentId,
      status: 'failed',
      amount_received: 0,
      currency: 'usd',
      payment_method: {
        id: `pm_mock_${Math.random().toString(36).substring(2, 8)}`,
        type: paymentMethodType,
      },
    };
  }

  // Simulate successful payment
  return {
    id: paymentIntentId,
    status: 'succeeded',
    amount_received: 1000, // This would be the actual amount from the payment intent
    currency: 'usd',
    payment_method: {
      id: `pm_mock_${Math.random().toString(36).substring(2, 8)}`,
      type: paymentMethodType,
    },
  };
};

// Mock webhook event (for testing webhook handling)
export const mockWebhookEvent = (
  paymentIntentId: string,
  eventType: 'payment_intent.succeeded' | 'payment_intent.payment_failed'
) => {
  return {
    id: `evt_mock_${Math.random().toString(36).substring(2, 8)}`,
    object: 'event',
    api_version: '2020-08-27',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: paymentIntentId,
        object: 'payment_intent',
        status: eventType === 'payment_intent.succeeded' ? 'succeeded' : 'failed',
        amount: 1000,
        currency: 'usd',
      },
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: `req_mock_${Math.random().toString(36).substring(2, 8)}`,
      idempotency_key: null,
    },
    type: eventType,
  };
};

// Mock payment methods for testing
export const mockPaymentMethods = {
  // Test card numbers from Stripe documentation
  VISA_SUCCESS: '****************',
  VISA_DECLINE: '****************',
  MASTERCARD_SUCCESS: '****************',
  AMEX_SUCCESS: '***************',
  
  // Mock Google Pay token
  GOOGLE_PAY_TOKEN: 'tok_mock_google_pay_123456',
  
  // Mock Apple Pay token
  APPLE_PAY_TOKEN: 'tok_mock_apple_pay_123456',
};

// Utility function to simulate different payment scenarios
export const simulatePaymentScenario = (scenario: 'success' | 'decline' | 'error' | 'network_error') => {
  switch (scenario) {
    case 'success':
      return { shouldSucceed: true, errorMessage: null };
    case 'decline':
      return { shouldSucceed: false, errorMessage: 'Your card was declined.' };
    case 'error':
      return { shouldSucceed: false, errorMessage: 'An error occurred while processing your payment.' };
    case 'network_error':
      return { shouldSucceed: false, errorMessage: 'Network error. Please check your connection and try again.' };
    default:
      return { shouldSucceed: true, errorMessage: null };
  }
};

// Development helper to log payment events
export const logPaymentEvent = (event: string, data: any) => {
  if (__DEV__) {
    // Payment event logged for development
  }
};

// Export all mock functions for easy testing
export const mockPaymentAPI = {
  createPaymentIntent: mockCreatePaymentIntent,
  confirmPayment: mockConfirmPayment,
  webhookEvent: mockWebhookEvent,
  paymentMethods: mockPaymentMethods,
  simulateScenario: simulatePaymentScenario,
  logEvent: logPaymentEvent,
};
