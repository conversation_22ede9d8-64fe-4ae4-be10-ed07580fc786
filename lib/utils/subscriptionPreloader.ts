import { subscriptionService } from '../services/subscriptionService';

/**
 * Preload subscription status on app startup
 * Call this early in your app initialization
 */
export const preloadSubscriptionStatus = async (): Promise<void> => {
  try {
    // This will cache the subscription status for immediate use
    await subscriptionService.checkUserPremiumStatus();
  } catch (error) {
    console.error('Error preloading subscription status:', error);
  }
};

/**
 * Initialize subscription status checking with periodic updates
 * Call this once in your app root
 */
export const initializeSubscriptionService = (): (() => void) => {
  // Preload immediately
  preloadSubscriptionStatus();

  // Set up periodic cache refresh (every 5 minutes)
  const interval = setInterval(() => {
    subscriptionService.checkUserPremiumStatus(true); // Force refresh
  }, 5 * 60 * 1000);

  // Return cleanup function
  return () => {
    clearInterval(interval);
  };
};

/**
 * Clear subscription cache (useful for logout)
 */
export const clearSubscriptionCache = (): void => {
  subscriptionService.clearCache();
};
