/**
 * Individual User Program Status Utilities
 *
 * This module handles program status calculations for individual users based on their timezone,
 * eliminating the need for centralized program status coordination.
 */

import { Participant } from '../services/database/types';

export interface UserProgramStatus {
  status: 'upcoming' | 'ongoing' | 'ended';
  currentDay: number;
  daysUntilStart?: number;
  daysUntilEnd?: number;
  isSubmissionDeadlinePassed: boolean;
  nextDeadline?: Date;
  message: string;
}

export interface TransitionalProgramStatus extends UserProgramStatus {
  // Information about global vs individual status mismatch
  globalProgramStatus?: 'upcoming' | 'ongoing' | 'ended' | 'disqualified';
  isTransitional: boolean;
  transitionalMessage?: string;
  // Only for the specific mismatch: individual ended, global ongoing
  showWaitingMessage: boolean;
}

/**
 * Calculate the current program status for an individual user
 */
export const calculateUserProgramStatus = (
  participant: Participant,
  currentTime: Date = new Date()
): UserProgramStatus => {
  const personalStartDate = new Date(participant.personalStartDate);
  const personalEndDate = new Date(participant.personalEndDate);
  const userTimezone = participant.timezone;

  // Get current time in user's timezone
  const userCurrentTime = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));
  
  // Calculate days since program start
  const daysSinceStart = Math.floor(
    (userCurrentTime.getTime() - personalStartDate.getTime()) / (1000 * 60 * 60 * 24)
  );
  
  const currentDay = Math.max(1, daysSinceStart + 1);
  
  // Determine status based on current time vs personal dates
  let status: 'upcoming' | 'ongoing' | 'ended';
  let message: string;
  let daysUntilStart: number | undefined;
  let daysUntilEnd: number | undefined;
  
  if (userCurrentTime < personalStartDate) {
    status = 'upcoming';
    daysUntilStart = Math.ceil((personalStartDate.getTime() - userCurrentTime.getTime()) / (1000 * 60 * 60 * 24));
    message = daysUntilStart === 1 ? 'Starts tomorrow' : `Starts in ${daysUntilStart} days`;
  } else if (userCurrentTime >= personalEndDate) {
    status = 'ended';
    const daysAfterEnd = Math.floor((userCurrentTime.getTime() - personalEndDate.getTime()) / (1000 * 60 * 60 * 24));
    message = daysAfterEnd === 0 ? 'Just ended' : `Ended ${daysAfterEnd} day${daysAfterEnd === 1 ? '' : 's'} ago`;
  } else {
    status = 'ongoing';
    daysUntilEnd = Math.ceil((personalEndDate.getTime() - userCurrentTime.getTime()) / (1000 * 60 * 60 * 24));
    message = `Day ${currentDay} - ${daysUntilEnd} day${daysUntilEnd === 1 ? '' : 's'} remaining`;
  }

  // Calculate submission deadline (midnight of current day in user's timezone)
  const todayMidnight = new Date(userCurrentTime);
  todayMidnight.setHours(23, 59, 59, 999); // End of today
  
  const isSubmissionDeadlinePassed = status === 'ongoing' && userCurrentTime > todayMidnight;
  
  // Calculate next deadline (midnight of next day)
  const nextDeadline = new Date(todayMidnight);
  nextDeadline.setDate(nextDeadline.getDate() + 1);
  nextDeadline.setHours(0, 0, 0, 0); // Start of next day

  return {
    status,
    currentDay,
    daysUntilStart,
    daysUntilEnd,
    isSubmissionDeadlinePassed,
    nextDeadline: status === 'ongoing' ? nextDeadline : undefined,
    message,
  };
};

/**
 * Update participant's personal program status based on current time
 */
export const updateParticipantProgramStatus = (
  participant: Participant,
  currentTime: Date = new Date()
): Partial<Participant> => {
  const statusInfo = calculateUserProgramStatus(participant, currentTime);
  
  return {
    personalProgramStatus: statusInfo.status,
    personalCurrentDay: statusInfo.currentDay,
    lastDayCheckup: currentTime.toISOString(),
  };
};

/**
 * Calculate transitional program status that considers both individual and global program status
 */
export const calculateTransitionalProgramStatus = (
  participant: Participant,
  globalProgramStatus: 'upcoming' | 'ongoing' | 'ended' | 'disqualified',
  currentTime: Date = new Date()
): TransitionalProgramStatus => {
  // First get the basic individual status
  const individualStatus = calculateUserProgramStatus(participant, currentTime);

  // For disqualified programs, we need special handling
  if (globalProgramStatus === 'disqualified') {
    return {
      ...individualStatus,
      status: 'ended', // Individual status is always 'ended' for disqualified programs
      globalProgramStatus,
      isTransitional: true, // Always transitional since global is 'disqualified' but individual is 'ended'
      transitionalMessage: 'This program has been disqualified and is no longer active.',
      showWaitingMessage: false,
    };
  }

  // Check if there's a mismatch between individual and global status
  const isTransitional = individualStatus.status !== globalProgramStatus;

  let transitionalMessage: string | undefined;
  let showWaitingMessage = false;

  if (isTransitional) {
    if (individualStatus.status === 'ended' && globalProgramStatus === 'ongoing') {
      // User ended but program is still ongoing - show waiting message
      showWaitingMessage = true;
      transitionalMessage = 'Program ended. Waiting for users from other timezones to wrap up for us to display final results.';
    }
    // For individual ongoing + global ended: continue as normal (no special handling needed)
  }

  return {
    ...individualStatus,
    globalProgramStatus,
    isTransitional,
    transitionalMessage,
    showWaitingMessage,
  };
};

/**
 * Check if it's time for a daily checkup for this user
 */
export const shouldRunDailyCheckup = (
  participant: Participant,
  currentTime: Date = new Date()
): boolean => {
  if (!participant.lastDayCheckup) {
    return true; // First time checkup
  }

  const lastCheckup = new Date(participant.lastDayCheckup);
  const userTimezone = participant.timezone;
  
  // Get current time and last checkup time in user's timezone
  const userCurrentTime = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));
  const userLastCheckup = new Date(lastCheckup.toLocaleString('en-US', { timeZone: userTimezone }));
  
  // Check if it's a new day since last checkup
  const currentDay = userCurrentTime.getDate();
  const lastCheckupDay = userLastCheckup.getDate();
  
  return currentDay !== lastCheckupDay;
};

/**
 * Get the submission deadline for a specific day
 */
export const getSubmissionDeadline = (
  participant: Participant,
  dayNumber: number
): Date => {
  const personalStartDate = new Date(participant.personalStartDate);
  const userTimezone = participant.timezone;
  
  // Calculate the date for the specific day
  const dayDate = new Date(personalStartDate);
  dayDate.setDate(dayDate.getDate() + (dayNumber - 1));
  
  // Set to end of day (23:59:59) in user's timezone
  const deadline = new Date(dayDate.toLocaleString('en-US', { timeZone: userTimezone }));
  deadline.setHours(23, 59, 59, 999);
  
  return deadline;
};

/**
 * Check if a submission deadline has passed for a specific day
 */
export const isSubmissionDeadlinePassed = (
  participant: Participant,
  dayNumber: number,
  currentTime: Date = new Date()
): boolean => {
  const deadline = getSubmissionDeadline(participant, dayNumber);
  const userTimezone = participant.timezone;
  const userCurrentTime = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));
  
  return userCurrentTime > deadline;
};

/**
 * Get time remaining until submission deadline
 */
export const getTimeUntilDeadline = (
  participant: Participant,
  dayNumber: number,
  currentTime: Date = new Date()
): number => {
  const deadline = getSubmissionDeadline(participant, dayNumber);
  const userTimezone = participant.timezone;
  const userCurrentTime = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));
  
  return Math.max(0, deadline.getTime() - userCurrentTime.getTime());
};

/**
 * Format time remaining as human-readable string
 */
export const formatTimeRemaining = (milliseconds: number): string => {
  if (milliseconds <= 0) return 'Deadline passed';
  
  const hours = Math.floor(milliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
  
  if (hours > 0) {
    return `${hours}h ${minutes}m remaining`;
  } else {
    return `${minutes}m remaining`;
  }
};
