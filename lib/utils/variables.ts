import AsyncStorage from "@react-native-async-storage/async-storage";

async function getToken(): Promise<boolean> {
  try {
    const value = await AsyncStorage.getItem("authToken");
    return value === "true"; // Explicitly check if the value is "true"
  } catch {
    return false; // Default to `false` in case of an error
  }
}


function updateToken(val: boolean): void {
  AsyncStorage.setItem("authToken", JSON.stringify(val));
}

function updateId(value: string): void {
  AsyncStorage.setItem("email", value);
}

const getId = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem("email");
  } catch {
    return null;
  }
};

function updateFname(value: string): void {
  AsyncStorage.setItem("fname", value);
}

const getFname = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem("fname");
  } catch {
    return null;
  }
};

function updateLname(value: string): void {
  AsyncStorage.setItem("lname", value);
}

const getLname = async (): Promise<string | null> => {
  try {
    return await AsyncStorage.getItem("lname");
  } catch {
    return null;
  }
};

function updateIsLoggedIn(value: boolean): void {
  AsyncStorage.setItem("isLoggedIn", JSON.stringify(value));
}

const getIsLoggedIn = async (): Promise<boolean> => {
  try {
    const temp = await AsyncStorage.getItem("isLoggedIn");
    return temp === "true";
  } catch {
    return false;
  }
};

const logout = async () => {
  try {
    await AsyncStorage.clear();
    updateToken(false); // Reset the token to false
  } catch {
    // Handle silent errors if needed
  }
};

export {
  getToken,
  updateToken,
  getId,
  updateId,
  updateFname,
  getFname,
  updateLname,
  getLname,
  updateIsLoggedIn,
  getIsLoggedIn,
  logout,
};
