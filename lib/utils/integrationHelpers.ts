import { firestoreService } from '@/lib/services/database';
import { getId } from './variables';
import { Integration } from '@/lib/services/database/types';

/**
 * Utility functions for managing user integrations
 */

export interface IntegrationStatus {
  isConnected: boolean;
  integration?: Integration;
  lastSyncAt?: string;
  error?: string;
}

/**
 * Check if user has GitHub integration connected and active
 */
export async function getGitHubIntegrationStatus(): Promise<IntegrationStatus> {
  try {
    const userId = await getId();
    if (!userId) {
      return { isConnected: false, error: 'User not authenticated' };
    }

    const result = await firestoreService.integrations.getIntegrationByProvider(userId, 'github');
    
    if (result.success && result.data && result.data.isActive) {
      return {
        isConnected: true,
        integration: result.data,
        lastSyncAt: result.data.lastSyncAt,
      };
    } else {
      return { isConnected: false };
    }
  } catch (error) {
    console.error('Error checking GitHub integration status:', error);
    return { isConnected: false, error: 'Failed to check integration status' };
  }
}

/**
 * Get all active integrations for the current user
 */
export async function getAllActiveIntegrations(): Promise<Integration[]> {
  try {
    const userId = await getId();
    if (!userId) {
      return [];
    }

    const result = await firestoreService.integrations.getActiveIntegrations(userId);
    
    if (result.success && result.data) {
      return result.data;
    } else {
      return [];
    }
  } catch (error) {
    console.error('Error getting active integrations:', error);
    return [];
  }
}

/**
 * Disconnect a specific integration
 */
export async function disconnectIntegration(provider: Integration['provider']): Promise<{ success: boolean; error?: string }> {
  try {
    const userId = await getId();
    if (!userId) {
      return { success: false, error: 'User not authenticated' };
    }

    const result = await firestoreService.integrations.getIntegrationByProvider(userId, provider);
    
    if (result.success && result.data) {
      const deactivateResult = await firestoreService.integrations.deactivateIntegration(userId, result.data.id!);
      
      if (deactivateResult.success) {
        return { success: true };
      } else {
        return { success: false, error: deactivateResult.error };
      }
    } else {
      return { success: false, error: 'Integration not found' };
    }
  } catch (error) {
    console.error('Error disconnecting integration:', error);
    return { success: false, error: 'Failed to disconnect integration' };
  }
}

/**
 * Check if any integration needs attention (e.g., expired tokens, sync issues)
 */
export async function checkIntegrationsHealth(): Promise<{
  healthy: Integration[];
  needsAttention: Integration[];
  errors: string[];
}> {
  try {
    const userId = await getId();
    if (!userId) {
      return { healthy: [], needsAttention: [], errors: ['User not authenticated'] };
    }

    const result = await firestoreService.integrations.getAllIntegrations(userId);
    
    if (!result.success || !result.data) {
      return { healthy: [], needsAttention: [], errors: [result.error || 'Failed to fetch integrations'] };
    }

    const healthy: Integration[] = [];
    const needsAttention: Integration[] = [];
    const errors: string[] = [];

    for (const integration of result.data) {
      try {
        // Check if integration is active
        if (!integration.isActive) {
          needsAttention.push(integration);
          continue;
        }

        // Check if token might be expired (if expiresAt is set)
        if (integration.expiresAt) {
          const expiryDate = new Date(integration.expiresAt);
          const now = new Date();
          if (expiryDate <= now) {
            needsAttention.push(integration);
            continue;
          }
        }

        // Check if last sync was too long ago (more than 7 days)
        if (integration.lastSyncAt) {
          const lastSync = new Date(integration.lastSyncAt);
          const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          if (lastSync < sevenDaysAgo) {
            needsAttention.push(integration);
            continue;
          }
        }

        healthy.push(integration);
      } catch (integrationError) {
        errors.push(`Error checking ${integration.provider}: ${integrationError}`);
      }
    }

    return { healthy, needsAttention, errors };
  } catch (error) {
    console.error('Error checking integrations health:', error);
    return { healthy: [], needsAttention: [], errors: ['Failed to check integrations health'] };
  }
}

/**
 * Get integration statistics
 */
export async function getIntegrationStats(): Promise<{
  total: number;
  active: number;
  byProvider: Record<string, number>;
}> {
  try {
    const userId = await getId();
    if (!userId) {
      return { total: 0, active: 0, byProvider: {} };
    }

    const result = await firestoreService.integrations.getAllIntegrations(userId);
    
    if (!result.success || !result.data) {
      return { total: 0, active: 0, byProvider: {} };
    }

    const total = result.data.length;
    const active = result.data.filter(integration => integration.isActive).length;
    const byProvider: Record<string, number> = {};

    for (const integration of result.data) {
      byProvider[integration.provider] = (byProvider[integration.provider] || 0) + 1;
    }

    return { total, active, byProvider };
  } catch (error) {
    console.error('Error getting integration stats:', error);
    return { total: 0, active: 0, byProvider: {} };
  }
}
