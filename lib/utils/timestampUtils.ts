/**
 * Utility functions for handling Firestore timestamps and date formatting
 * Provides consistent timestamp handling across the application
 */

/**
 * Converts various timestamp formats to a JavaScript Date object
 * Handles Firestore Timestamps, ISO strings, and Date objects
 */
export const convertToDate = (timestamp: any): Date => {
  try {
    // Handle Firestore Timestamp objects (with seconds property)
    if (timestamp && typeof timestamp === 'object' && timestamp.seconds !== undefined) {
      return new Date(timestamp.seconds * 1000);
    }
    
    // Handle Firestore Timestamp objects (with toDate method)
    if (timestamp && typeof timestamp === 'object' && typeof timestamp.toDate === 'function') {
      return timestamp.toDate();
    }
    
    // Handle ISO string dates
    if (typeof timestamp === 'string') {
      return new Date(timestamp);
    }
    
    // Handle Date objects
    if (timestamp instanceof Date) {
      return timestamp;
    }
    
    // Fallback to current date
    return new Date();
  } catch (error) {
    console.error('Error converting timestamp to date:', error);
    return new Date();
  }
};

/**
 * Formats a timestamp for display as "time ago" format
 * e.g., "2m ago", "1h ago", "3d ago", "Jan 15"
 */
export const formatTimeAgo = (timestamp: any): string => {
  try {
    const date = convertToDate(timestamp);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays}d ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
      });
    }
  } catch (error) {
    console.error('Error formatting time ago:', error);
    return 'Unknown time';
  }
};

/**
 * Formats a timestamp for display as a full date
 * e.g., "Jan 15, 2024"
 */
export const formatFullDate = (timestamp: any): string => {
  try {
    const date = convertToDate(timestamp);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (error) {
    console.error('Error formatting full date:', error);
    return 'Unknown date';
  }
};

/**
 * Formats a timestamp for display as time only
 * e.g., "2:30 PM"
 */
export const formatTime = (timestamp: any): string => {
  try {
    const date = convertToDate(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return 'Unknown time';
  }
};

/**
 * Formats a timestamp for display as date and time
 * e.g., "Jan 15, 2024 at 2:30 PM"
 */
export const formatDateTime = (timestamp: any): string => {
  try {
    const date = convertToDate(timestamp);
    const dateStr = date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
    const timeStr = date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
    return `${dateStr} at ${timeStr}`;
  } catch (error) {
    console.error('Error formatting date time:', error);
    return 'Unknown date';
  }
};

/**
 * Checks if a timestamp is valid
 */
export const isValidTimestamp = (timestamp: any): boolean => {
  try {
    const date = convertToDate(timestamp);
    return !isNaN(date.getTime());
  } catch (error) {
    return false;
  }
};

/**
 * Sorts an array of objects by timestamp field in descending order (newest first)
 * Handles both Firestore Timestamps and string dates
 */
export const sortByTimestamp = <T extends Record<string, any>>(
  items: T[],
  timestampField: keyof T
): T[] => {
  return items.sort((a, b) => {
    const dateA = convertToDate(a[timestampField]);
    const dateB = convertToDate(b[timestampField]);
    return dateB.getTime() - dateA.getTime();
  });
};
