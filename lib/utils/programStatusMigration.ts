/**
 * Program Status Migration Utilities
 * 
 * This module provides utilities to migrate existing programs from centralized
 * program status to individual user program status.
 */

import { ParticipantService } from '../services/database/ParticipantService';
import { ProgramService } from '../services/database/ProgramService';
import { Participant, Program } from '../services/database/types';

export interface MigrationResult {
  success: boolean;
  programId: string;
  participantsMigrated: number;
  errors: string[];
  message: string;
}

export interface BatchMigrationResult {
  totalPrograms: number;
  successfulMigrations: number;
  failedMigrations: number;
  results: MigrationResult[];
}

/**
 * Migrate a single program's participants to individual program status
 */
export const migrateProgramToIndividualStatus = async (
  programId: string
): Promise<MigrationResult> => {
  const participantService = new ParticipantService();
  const programService = new ProgramService();
  const errors: string[] = [];
  let participantsMigrated = 0;

  try {
    // Get program details
    const programResult = await programService.getProgramById(programId);
    if (!programResult.success || !programResult.data) {
      return {
        success: false,
        programId,
        participantsMigrated: 0,
        errors: ['Program not found'],
        message: 'Failed to get program details'
      };
    }

    const program = programResult.data;

    // Get all participants
    const participantsResult = await participantService.getAllParticipants(programId);
    if (!participantsResult.success || !participantsResult.data) {
      return {
        success: false,
        programId,
        participantsMigrated: 0,
        errors: ['Failed to get participants'],
        message: 'Failed to get program participants'
      };
    }

    const participants = participantsResult.data;

    // Migrate each participant
    for (const participant of participants) {
      try {
        // Check if participant already has individual status fields
        if (participant.personalProgramStatus) {
          console.log(`Participant ${participant.userId} already migrated, skipping`);
          continue;
        }

        // Calculate personal dates based on participant's timezone
        const personalStartDate = calculatePersonalStartDate(
          program.startDate,
          participant.timezone
        );
        
        const personalEndDate = calculatePersonalEndDate(
          personalStartDate,
          program.duration
        );

        // Calculate current status and day
        const currentTime = new Date();
        const { personalProgramStatus, personalCurrentDay } = calculateCurrentStatus(
          personalStartDate,
          personalEndDate,
          participant.timezone,
          currentTime
        );

        // Update participant with individual status fields
        const updateData: Partial<Participant> = {
          personalProgramStatus,
          personalStartDate,
          personalEndDate,
          personalCurrentDay,
          lastDayCheckup: currentTime.toISOString(),
        };

        const updateResult = await participantService.updateParticipant(
          programId,
          participant.userId,
          updateData
        );

        if (updateResult.success) {
          participantsMigrated++;
          console.log(`Migrated participant ${participant.userId} to individual status`);
        } else {
          errors.push(`Failed to update participant ${participant.userId}: ${updateResult.error}`);
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Error migrating participant ${participant.userId}: ${errorMessage}`);
      }
    }

    return {
      success: errors.length === 0,
      programId,
      participantsMigrated,
      errors,
      message: `Migrated ${participantsMigrated} participants${errors.length > 0 ? ` with ${errors.length} errors` : ''}`
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return {
      success: false,
      programId,
      participantsMigrated,
      errors: [errorMessage],
      message: `Migration failed: ${errorMessage}`
    };
  }
};

/**
 * Migrate multiple programs to individual status
 */
export const batchMigrateProgramsToIndividualStatus = async (
  programIds: string[]
): Promise<BatchMigrationResult> => {
  const results: MigrationResult[] = [];
  let successfulMigrations = 0;
  let failedMigrations = 0;

  for (const programId of programIds) {
    try {
      const result = await migrateProgramToIndividualStatus(programId);
      results.push(result);

      if (result.success) {
        successfulMigrations++;
      } else {
        failedMigrations++;
      }

      // Add small delay to avoid overwhelming the database
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      results.push({
        success: false,
        programId,
        participantsMigrated: 0,
        errors: [errorMessage],
        message: `Migration failed: ${errorMessage}`
      });
      failedMigrations++;
    }
  }

  return {
    totalPrograms: programIds.length,
    successfulMigrations,
    failedMigrations,
    results
  };
};

/**
 * Get all programs that need migration (don't have participants with individual status)
 */
export const getProgramsNeedingMigration = async (): Promise<{
  success: boolean;
  programIds: string[];
  error?: string;
}> => {
  try {
    const programService = new ProgramService();
    const participantService = new ParticipantService();

    // Get all programs
    const programsResult = await programService.getAllPrograms();
    if (!programsResult.success || !programsResult.data) {
      return {
        success: false,
        programIds: [],
        error: 'Failed to get programs'
      };
    }

    const programs = programsResult.data;
    const programsNeedingMigration: string[] = [];

    // Check each program
    for (const program of programs) {
      try {
        // Get first participant to check if they have individual status
        if (!program.id) continue; // Skip programs without id
        const participantsResult = await participantService.getAllParticipants(program.id);
        if (participantsResult.success && participantsResult.data && participantsResult.data.length > 0) {
          const firstParticipant = participantsResult.data[0];
          
          // If participant doesn't have individual status fields, program needs migration
          if (!firstParticipant.personalProgramStatus) {
            programsNeedingMigration.push(program.id!);
          }
        }
      } catch (error) {
        console.error(`Error checking program ${program.id}:`, error);
      }
    }

    return {
      success: true,
      programIds: programsNeedingMigration
    };

  } catch (error) {
    return {
      success: false,
      programIds: [],
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Helper functions

function calculatePersonalStartDate(programStartDate: string, userTimezone: string): string {
  try {
    // Create a date object for the program start date at midnight in user's timezone
    const startDate = new Date(programStartDate + 'T00:00:00');
    
    // Convert to user's timezone
    const userStartDate = new Date(startDate.toLocaleString('en-US', { timeZone: userTimezone }));
    
    return userStartDate.toISOString();
  } catch (error) {
    console.error('Error calculating personal start date:', error);
    // Fallback to program start date
    return new Date(programStartDate + 'T00:00:00').toISOString();
  }
}

function calculatePersonalEndDate(personalStartDate: string, durationDays: number): string {
  try {
    const startDate = new Date(personalStartDate);
    const endDate = new Date(startDate.getTime() + (durationDays * 24 * 60 * 60 * 1000));
    return endDate.toISOString();
  } catch (error) {
    console.error('Error calculating personal end date:', error);
    // Fallback calculation
    const fallbackStart = new Date(personalStartDate);
    fallbackStart.setDate(fallbackStart.getDate() + durationDays);
    return fallbackStart.toISOString();
  }
}

function calculateCurrentStatus(
  personalStartDate: string,
  personalEndDate: string,
  userTimezone: string,
  currentTime: Date
): {
  personalProgramStatus: 'upcoming' | 'ongoing' | 'ended';
  personalCurrentDay: number;
} {
  try {
    const startDate = new Date(personalStartDate);
    const endDate = new Date(personalEndDate);
    
    // Get current time in user's timezone
    const userCurrentTime = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));
    
    // Calculate days since program start
    const daysSinceStart = Math.floor(
      (userCurrentTime.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    const personalCurrentDay = Math.max(1, daysSinceStart + 1);
    
    // Determine status
    let personalProgramStatus: 'upcoming' | 'ongoing' | 'ended';
    if (userCurrentTime < startDate) {
      personalProgramStatus = 'upcoming';
    } else if (userCurrentTime >= endDate) {
      personalProgramStatus = 'ended';
    } else {
      personalProgramStatus = 'ongoing';
    }

    return {
      personalProgramStatus,
      personalCurrentDay
    };

  } catch (error) {
    console.error('Error calculating current status:', error);
    return {
      personalProgramStatus: 'upcoming',
      personalCurrentDay: 1
    };
  }
}
