import * as Localization from 'expo-localization';

/**
 * UPDATED: This file now includes both legacy complex timezone functions
 * and new simplified participant-level timezone functions.
 *
 * NEW FUNCTIONS (recommended):
 * - calculateSimpleProgramDay()
 * - getSimpleParticipantStatus()
 * - isSimpleSubmissionDeadlinePassed()
 *
 * LEGACY FUNCTIONS (deprecated):
 * - calculateTimezoneAwareProgramDay() - has edge cases
 * - getProgramStatusMessage() - complex logic
 */

/**
 * Get the user's current timezone using expo-localization
 * Falls back to UTC if detection fails
 */
export const getUserTimezone = (): string => {
  try {
    // Try different methods to get timezone from expo-localization
    let timezone: string | undefined;

    // Method 1: Try getCalendars (newer API)
    try {
      const calendars = Localization.getCalendars();
      timezone = calendars[0]?.timeZone || undefined;
    } catch (e) {
      // Ignore and try next method
    }

    // Method 2: Try getLocales (if available)
    if (!timezone) {
      try {
        const locales = Localization.getLocales();
        // Some versions might have timeZone property
        timezone = (locales[0] as any)?.timeZone;
      } catch (e) {
        // Ignore and try next method
      }
    }

    // Method 3: Fallback to Intl API
    if (!timezone) {
      try {
        timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      } catch (e) {
        // Ignore
      }
    }

    // Validate that we got a proper timezone string
    if (timezone && typeof timezone === 'string' && timezone.includes('/')) {
      return timezone;
    }

    // Fallback to UTC if we don't get a valid timezone
    console.warn('Could not detect user timezone, falling back to UTC');
    return 'UTC';
  } catch (error) {
    console.error('Error detecting timezone:', error);
    return 'UTC';
  }
};

/**
 * Format timezone for display
 */
export const formatTimezoneForDisplay = (timezone: string): string => {
  try {
    // Create a date to get timezone info
    const date = new Date();
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      timeZoneName: 'short'
    });
    
    const parts = formatter.formatToParts(date);
    const timeZoneName = parts.find(part => part.type === 'timeZoneName')?.value;
    
    // Return timezone with abbreviation if available
    if (timeZoneName) {
      return `${timezone} (${timeZoneName})`;
    }
    
    return timezone;
  } catch (error) {
    console.error('Error formatting timezone:', error);
    return timezone;
  }
};

/**
 * Get timezone offset in hours
 */
export const getTimezoneOffset = (timezone: string): number => {
  try {
    const date = new Date();
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    
    return (tzDate.getTime() - utcDate.getTime()) / (1000 * 60 * 60);
  } catch (error) {
    console.error('Error calculating timezone offset:', error);
    return 0;
  }
};

/**
 * Convert a date to a specific timezone
 */
export const convertToTimezone = (date: Date, timezone: string): Date => {
  try {
    const dateString = date.toLocaleString('en-US', { timeZone: timezone });
    return new Date(dateString);
  } catch (error) {
    console.error('Error converting to timezone:', error);
    return date;
  }
};

/**
 * Check if it's midnight (00:00) in a specific timezone
 */
export const isMidnightInTimezone = (timezone: string): boolean => {
  try {
    const now = new Date();
    const timeInTz = now.toLocaleString('en-US', { 
      timeZone: timezone,
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });
    
    return timeInTz === '00:00';
  } catch (error) {
    console.error('Error checking midnight in timezone:', error);
    return false;
  }
};

/**
 * Get current date in a specific timezone (YYYY-MM-DD format)
 */
export const getCurrentDateInTimezone = (timezone: string): string => {
  try {
    const now = new Date();
    const dateInTz = now.toLocaleDateString('en-CA', { timeZone: timezone }); // en-CA gives YYYY-MM-DD format
    return dateInTz;
  } catch (error) {
    console.error('Error getting current date in timezone:', error);
    return new Date().toISOString().split('T')[0];
  }
};

/**
 * Common timezone options for selection
 */
export const COMMON_TIMEZONES = [
  { label: 'UTC', value: 'UTC' },
  { label: 'New York (EST/EDT)', value: 'America/New_York' },
  { label: 'Los Angeles (PST/PDT)', value: 'America/Los_Angeles' },
  { label: 'Chicago (CST/CDT)', value: 'America/Chicago' },
  { label: 'Denver (MST/MDT)', value: 'America/Denver' },
  { label: 'London (GMT/BST)', value: 'Europe/London' },
  { label: 'Paris (CET/CEST)', value: 'Europe/Paris' },
  { label: 'Tokyo (JST)', value: 'Asia/Tokyo' },
  { label: 'Sydney (AEST/AEDT)', value: 'Australia/Sydney' },
  { label: 'Melbourne (AEST/AEDT)', value: 'Australia/Melbourne' },
  { label: 'Mumbai (IST)', value: 'Asia/Kolkata' },
  { label: 'Singapore (SGT)', value: 'Asia/Singapore' },
];

/**
 * Program Lifecycle Timezone Utilities
 * These functions help manage timezone-aware program start and end scheduling
 */

/**
 * Get timezone offset in minutes for a specific date
 * This accounts for daylight saving time changes
 */
export const getTimezoneOffsetForDate = (timezone: string, date: Date): number => {
  try {
    // Create a date formatter for the timezone
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));

    // Return offset in minutes (positive means ahead of UTC)
    return (tzDate.getTime() - utcDate.getTime()) / (1000 * 60);
  } catch (error) {
    console.error(`Error calculating timezone offset for ${timezone}:`, error);
    return 0;
  }
};

/**
 * Find the earliest timezone among a list of timezones for a specific date
 * Returns the timezone that experiences midnight first (most negative offset)
 */
export const findEarliestTimezone = (timezones: string[], referenceDate: Date): string => {
  if (timezones.length === 0) return 'UTC';
  if (timezones.length === 1) return timezones[0];

  let earliestTimezone = timezones[0];
  let earliestOffset = getTimezoneOffsetForDate(earliestTimezone, referenceDate);

  for (let i = 1; i < timezones.length; i++) {
    const currentOffset = getTimezoneOffsetForDate(timezones[i], referenceDate);
    // Earlier timezone has more negative offset (experiences midnight first)
    if (currentOffset < earliestOffset) {
      earliestOffset = currentOffset;
      earliestTimezone = timezones[i];
    }
  }

  return earliestTimezone;
};

/**
 * Find the latest timezone among a list of timezones for a specific date
 * Returns the timezone that experiences midnight last (most positive offset)
 */
export const findLatestTimezone = (timezones: string[], referenceDate: Date): string => {
  if (timezones.length === 0) return 'UTC';
  if (timezones.length === 1) return timezones[0];

  let latestTimezone = timezones[0];
  let latestOffset = getTimezoneOffsetForDate(latestTimezone, referenceDate);

  for (let i = 1; i < timezones.length; i++) {
    const currentOffset = getTimezoneOffsetForDate(timezones[i], referenceDate);
    // Later timezone has more positive offset (experiences midnight last)
    if (currentOffset > latestOffset) {
      latestOffset = currentOffset;
      latestTimezone = timezones[i];
    }
  }

  return latestTimezone;
};

/**
 * Calculate when midnight occurs in a specific timezone for a given date
 * Returns a UTC Date object representing midnight in the target timezone
 */
export const getMidnightInTimezone = (dateString: string, timezone: string): Date => {
  try {
    // Parse the date string (YYYY-MM-DD format)
    const [year, month, day] = dateString.split('-').map(Number);

    // Create a date object representing midnight in the target timezone
    // We use a temporary date to get the timezone offset
    const tempDate = new Date(year, month - 1, day, 0, 0, 0, 0);

    // Get the timezone offset for this date
    const offsetMinutes = getTimezoneOffsetForDate(timezone, tempDate);

    // Create UTC date representing midnight in the target timezone
    const utcMidnight = new Date(year, month - 1, day, 0, 0, 0, 0);
    utcMidnight.setMinutes(utcMidnight.getMinutes() - offsetMinutes);

    return utcMidnight;
  } catch (error) {
    console.error(`Error calculating midnight for ${timezone} on ${dateString}:`, error);
    // Fallback to UTC midnight
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  }
};

/**
 * Calculate program start time based on earliest participant timezone
 * Program should start when it's midnight in the earliest timezone
 */
export const calculateProgramStartTime = (startDateString: string, participantTimezones: string[]): Date => {
  if (participantTimezones.length === 0) {
    // No participants yet, default to UTC
    const [year, month, day] = startDateString.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  }

  const referenceDate = new Date(startDateString + 'T00:00:00Z');
  const earliestTimezone = findEarliestTimezone(participantTimezones, referenceDate);

  return getMidnightInTimezone(startDateString, earliestTimezone);
};

/**
 * Calculate program end time based on latest participant timezone
 * Program should end when it's midnight in the latest timezone
 */
export const calculateProgramEndTime = (endDateString: string, participantTimezones: string[]): Date => {
  if (participantTimezones.length === 0) {
    // No participants, default to UTC
    const [year, month, day] = endDateString.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  }

  const referenceDate = new Date(endDateString + 'T00:00:00Z');
  const latestTimezone = findLatestTimezone(participantTimezones, referenceDate);

  return getMidnightInTimezone(endDateString, latestTimezone);
};

/**
 * Timezone-Aware Day Calculation Functions
 * These functions calculate program days based on user timezone and program lifecycle
 */

/**
 * Calculate the current program day for a user in their timezone
 * UPDATED: Now uses simplified participant-level logic without edge cases
 */
export const calculateTimezoneAwareProgramDay = (
  programStartDate: string,
  userTimezone: string,
  programStatus: 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified',
  programDuration: number = 30
): number => {
  const result = calculateParticipantProgramDay(programStartDate, userTimezone, programDuration);
  return result.currentDay;
};

/**
 * Calculate days until program starts in user's timezone
 */
export const calculateDaysUntilStart = (
  programStartDate: string,
  userTimezone: string
): number => {
  try {
    // Get current date in user's timezone
    const userCurrentDate = getCurrentDateInTimezone(userTimezone);
    const userCurrentDateObj = new Date(userCurrentDate + 'T00:00:00');

    // Parse program start date
    const [startYear, startMonth, startDay] = programStartDate.split('-').map(Number);
    const programStartDateObj = new Date(startYear, startMonth - 1, startDay);

    // Calculate days difference
    const daysDiff = Math.ceil(
      (programStartDateObj.getTime() - userCurrentDateObj.getTime()) / (1000 * 60 * 60 * 24)
    );

    return Math.max(0, daysDiff);
  } catch (error) {
    console.error('Error calculating days until start:', error);
    return 0;
  }
};

/**
 * Get program status message based on participant timezone
 * UPDATED: Simplified without complex edge cases
 */
export const getProgramStatusMessage = (
  program: {
    status: 'upcoming' | 'ongoing' | 'ended' | 'active' | 'disqualified';
    startDate: string;
    duration?: number;
  },
  userTimezone: string
): {
  status: string;
  message: string;
  dayInfo: string;
} => {
  try {
    const duration = program.duration || 30;
    const result = calculateParticipantProgramDay(program.startDate, userTimezone, duration);

    let message: string;
    let status: string;

    switch (result.status) {
      case 'upcoming':
        const userDate = new Date(new Date().toLocaleString('en-US', { timeZone: userTimezone }));
        const [year, month, day] = program.startDate.split('-').map(Number);
        const startDate = new Date(year, month - 1, day);
        const daysUntilStart = Math.ceil((startDate.getTime() - userDate.getTime()) / (1000 * 60 * 60 * 24));

        status = 'upcoming';
        message = daysUntilStart === 0 ? 'Starting today!' : `Starts in ${daysUntilStart} day${daysUntilStart === 1 ? '' : 's'}`;
        break;

      case 'ongoing':
        status = 'ongoing';
        const daysRemaining = duration - result.currentDay + 1;
        message = `Day ${result.currentDay} of ${duration} - ${daysRemaining} day${daysRemaining === 1 ? '' : 's'} remaining`;
        break;

      case 'ended':
        status = 'ended';
        message = 'Program completed';
        break;

      default:
        status = 'unknown';
        message = 'Unknown status';
    }

    return {
      status,
      message,
      dayInfo: `Day ${result.currentDay} of ${duration}`
    };
  } catch (error) {
    console.error('Error getting program status message:', error);
    return {
      status: 'error',
      message: 'Unable to determine program status',
      dayInfo: 'Day ?'
    };
  }
};

// ============================================================================
// SIMPLIFIED PARTICIPANT TIMEZONE FUNCTIONS
// ============================================================================

/**
 * Calculate participant's program day and status based on their timezone
 * This replaces the complex timezone coordination with simple per-participant logic
 */
export const calculateParticipantProgramDay = (
  programStartDate: string,
  userTimezone: string,
  programDuration: number,
  currentTime: Date = new Date()
): { currentDay: number; status: 'upcoming' | 'ongoing' | 'ended' } => {
  try {
    // Get current date in user's timezone (date only, not time)
    const userDate = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));
    userDate.setHours(0, 0, 0, 0);

    // Parse program start date
    const [year, month, day] = programStartDate.split('-').map(Number);
    const startDate = new Date(year, month - 1, day);

    // Calculate days since start
    const daysDiff = Math.floor((userDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const currentDay = daysDiff + 1;

    // Determine status
    let status: 'upcoming' | 'ongoing' | 'ended';
    if (currentDay < 1) {
      status = 'upcoming';
    } else if (currentDay > programDuration) {
      status = 'ended';
    } else {
      status = 'ongoing';
    }

    return {
      currentDay: Math.max(1, currentDay),
      status
    };
  } catch (error) {
    console.error('Error calculating participant program day:', error);
    return { currentDay: 1, status: 'upcoming' };
  }
};
