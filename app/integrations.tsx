import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  Modal,
} from 'react-native';
import { MaterialCommunityIcons, MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { ErrorModal, SuccessModal } from '@/shared/components/modals';
import {
  getAllIntegrations,
  getActiveUserCommits,
} from '@/lib/services/database';
import { Integration } from '@/lib/services/database/types';
import { getId } from '@/lib/utils/variables';
import { githubService } from '@/lib/services/githubService';
import { firestoreService } from '@/lib/services/database';
import Header from '@/ui/common/Header';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { useCameraPermissions } from 'expo-camera';

interface IntegrationCardProps {
  provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava' | 'leetcode' | 'google_fit' | 'apple_health';
  integration: Integration | null;
  onConnect: () => void;
  onRevoke: () => void;
  connecting: boolean;
  revoking: boolean;
  comingSoon?: boolean;
  permissionGranted?: boolean;
}

interface RevokeModalData {
  provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava';
  hasActivePrograms: boolean;
  hasActiveCommits: boolean;
  details: string[];
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({
  provider,
  integration,
  onConnect,
  onRevoke,
  connecting,
  revoking,
  comingSoon = false,
  permissionGranted = false,
}) => {
  const { colors, isDark, designSystem } = useTheme();

  const getProviderInfo = () => {
    switch (provider) {
      case 'camera':
        return {
          name: 'Camera',
          icon: 'camera',
          color: '#4CAF50',
          gradientColors: ['#4CAF50', '#66BB6A'],
          description: 'Access camera for photo verification',
        };
      case 'gps':
        return {
          name: 'GPS Location',
          icon: 'map-marker',
          color: '#2196F3',
          gradientColors: ['#2196F3', '#42A5F5'],
          description: 'Access location for GPS verification',
        };
      case 'gallery':
        return {
          name: 'Gallery',
          icon: 'image-multiple',
          color: '#9C27B0',
          gradientColors: ['#9C27B0', '#BA68C8'],
          description: 'Access gallery for photo uploads',
        };
      case 'github':
        return {
          name: 'GitHub',
          icon: 'github',
          color: '#333',
          gradientColors: ['#24292e', '#586069'],
          description: 'Track your coding commits',
        };
      case 'strava':
        return {
          name: 'Strava',
          icon: 'run',
          color: '#FC4C02',
          gradientColors: ['#FC4C02', '#FF6B35'],
          description: 'Sync your workout activities',
        };
      case 'leetcode':
        return {
          name: 'LeetCode',
          icon: 'code-braces',
          color: '#FFA116',
          gradientColors: ['#FFA116', '#FF8C00'],
          description: 'Track your coding practice',
        };
      case 'google_fit':
        return {
          name: 'Google Fit',
          icon: 'google-fit',
          color: '#4285F4',
          gradientColors: ['#4285F4', '#34A853'],
          description: 'Monitor your fitness data',
        };
      case 'apple_health':
        return {
          name: 'Apple Health',
          icon: 'heart-pulse',
          color: '#FF3B30',
          gradientColors: ['#FF3B30', '#FF6B6B'],
          description: 'Sync your health metrics',
        };
      default:
        return {
          name: provider,
          icon: 'link',
          color: '#666',
          gradientColors: ['#666', '#888'],
          description: 'Connect your account',
        };
    }
  };

  const providerInfo = getProviderInfo();
  // For permission-based providers, use permissionGranted; for others, use integration status
  const isConnected = ['camera', 'gps', 'gallery'].includes(provider)
    ? permissionGranted
    : integration && integration.isActive;

  const formatCreationDate = (integration: Integration) => {
    if (!integration.createdAt) return 'Unknown date';

    let date: Date;

    // Handle Firestore Timestamp objects
    if (typeof integration.createdAt === 'object' && integration.createdAt && 'toDate' in integration.createdAt) {
      date = (integration.createdAt as any).toDate();
    } else if (typeof integration.createdAt === 'string') {
      date = new Date(integration.createdAt);
    } else {
      date = new Date();
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return 'Invalid date';
    }

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };

    return `Connected ${date.toLocaleDateString('en-US', options)}`;
  };

  return (
    <LinearGradient
      colors={isDark ? ['#1a1a1a', '#2a2a2a', '#1f1f1f'] as const : ['#ffffff', '#f8f8f8', '#f5f5f5'] as const}
      style={[
        styles.integrationCard,
        designSystem.shadows.md,
        { shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark }
      ]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
    >
      <View style={styles.integrationHeader}>
        <LinearGradient
          colors={isConnected ? providerInfo.gradientColors as [string, string, ...string[]] : ['#666', '#888'] as const}
          style={[
            styles.integrationIconGradient,
            designSystem.shadows.sm,
            { shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark }
          ]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <MaterialCommunityIcons
            name={providerInfo.icon as any}
            size={20}
            color="#fff"
          />
        </LinearGradient>

        <View style={styles.integrationInfo}>
          <View style={styles.integrationTitleRow}>
            <Text style={[styles.integrationName, { color: colors.text }]}>
              {providerInfo.name}
            </Text>
            {isConnected ? (
              <View style={[
                styles.statusBadge,
                { backgroundColor: colors.success }
              ]}>
                <Text style={styles.statusText}>Connected</Text>
              </View>
            ) : comingSoon ? (
              <View style={[
                styles.statusBadge,
                { backgroundColor: colors.textMuted + '20', borderColor: colors.textMuted, borderWidth: 1 }
              ]}>
                <Text style={[styles.statusText, { color: colors.textMuted }]}>Coming Soon</Text>
              </View>
            ) : (
              <TouchableOpacity
                style={[
                  styles.connectButtonSmall,
                  designSystem.shadows.sm,
                  { shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark }
                ]}
                onPress={onConnect}
                disabled={connecting}
              >
                <LinearGradient
                  colors={['#FFD700', '#FFA000']}
                  style={styles.connectButtonSmallGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  {connecting ? (
                    <ActivityIndicator size="small" color="#000" />
                  ) : (
                    <>
                      <MaterialCommunityIcons name="link" size={14} color="#000" />
                      <Text style={styles.connectButtonSmallText}>Connect</Text>
                    </>
                  )}
                </LinearGradient>
              </TouchableOpacity>
            )}
          </View>

          <Text style={[styles.integrationDescription, { color: colors.textMuted }]}>
            {providerInfo.description}
          </Text>

          {isConnected && integration && (
            <View style={styles.connectionDetails}>
              <View style={styles.connectionDetailsRow}>
                <View style={styles.connectionInfo}>
                  <Text style={[styles.detailText, { color: colors.textMuted }]}>
                    {integration.user.login || integration.user.name || integration.user.email}
                  </Text>
                  <Text style={[styles.syncText, { color: colors.textMuted }]}>
                    {formatCreationDate(integration)}
                  </Text>
                </View>
                <TouchableOpacity
                  style={[styles.unlinkButton, { backgroundColor: colors.error + '15', borderColor: colors.error }]}
                  onPress={onRevoke}
                  disabled={connecting || revoking}
                >
                  {(connecting || revoking) ? (
                    <ActivityIndicator size="small" color={colors.error} />
                  ) : (
                    <MaterialCommunityIcons name="link-off" size={14} color={colors.error} />
                  )}
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
      </View>


    </LinearGradient>
  );
};

export default function IntegrationsScreen() {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(true);
  const [connecting, setConnecting] = useState<string | null>(null);
  const [revoking, setRevoking] = useState<string | null>(null);
  const [showRevokeModal, setShowRevokeModal] = useState(false);
  const [revokeModalData, setRevokeModalData] = useState<RevokeModalData | null>(null);

  // Permission states
  const [cameraPermission, requestCameraPermission] = useCameraPermissions();
  const [galleryPermission, setGalleryPermission] = useState<ImagePicker.MediaLibraryPermissionResponse | null>(null);
  const [locationPermission, setLocationPermission] = useState<Location.LocationPermissionResponse | null>(null);

  // Modal states for replacing Alert dialogs
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '', onPress: () => {} });

  useEffect(() => {
    loadIntegrations();
    checkPermissions();
  }, []);

  const checkPermissions = async () => {
    try {
      // Check gallery permission
      const galleryPerm = await ImagePicker.getMediaLibraryPermissionsAsync();
      setGalleryPermission(galleryPerm);

      // Check location permission
      const locationPerm = await Location.getForegroundPermissionsAsync();
      setLocationPermission(locationPerm);
    } catch (error) {
      console.error('Error checking permissions:', error);
    }
  };

  const loadIntegrations = async () => {
    try {
      setLoading(true);
      const userId = await getId();
      if (!userId) return;

      const result = await getAllIntegrations(userId);
      if (result.success && result.data) {
        setIntegrations(result.data);
      }
    } catch (error) {
      console.error('Error loading integrations:', error);
    } finally {
      setLoading(false);
    }
  };

  const getIntegrationForProvider = (provider: 'github' | 'strava'): Integration | null => {
    return integrations.find(i => i.provider === provider && i.isActive) || null;
  };

  const handleConnect = async (provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava') => {
    setConnecting(provider);
    try {
      if (provider === 'camera') {
        const result = await requestCameraPermission();
        if (!result.granted) {
          setErrorModalData({
            title: 'Permission Denied',
            message: 'Camera access is required for photo verification.'
          });
          setErrorModalVisible(true);
        }
      } else if (provider === 'gallery') {
        const result = await ImagePicker.requestMediaLibraryPermissionsAsync();
        setGalleryPermission(result);
        if (!result.granted) {
          setErrorModalData({
            title: 'Permission Denied',
            message: 'Gallery access is required for photo uploads.'
          });
          setErrorModalVisible(true);
        }
      } else if (provider === 'gps') {
        const result = await Location.requestForegroundPermissionsAsync();
        setLocationPermission(result);
        if (!result.granted) {
          setErrorModalData({
            title: 'Permission Denied',
            message: 'Location access is required for GPS verification.'
          });
          setErrorModalVisible(true);
        }
      } else if (provider === 'github') {
        await githubService.authenticate();
        // The authentication flow will handle saving the integration
        await loadIntegrations(); // Refresh after connection
      } else if (provider === 'strava') {
        // TODO: Implement Strava authentication
        setSuccessModalData({
          title: 'Coming Soon',
          message: 'Strava integration is coming soon!',
          onPress: () => setSuccessModalVisible(false)
        });
        setSuccessModalVisible(true);
      }
    } catch (error) {
      console.error(`Error connecting ${provider}:`, error);
      setErrorModalData({
        title: 'Connection Error',
        message: `Failed to connect to ${provider}. Please try again.`
      });
      setErrorModalVisible(true);
    } finally {
      setConnecting(null);
    }
  };

  const checkDependentPrograms = async (provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava') => {
    try {
      const userId = await getId();
      if (!userId) return { hasActivePrograms: false, hasActiveCommits: false, details: [] };

      // Check for active commits that might depend on this integration
      const commitsResult = await getActiveUserCommits(userId);
      const activeCommits = commitsResult.success ? commitsResult.data || [] : [];

      // Check for user programs that might depend on this integration
      const userProgramsResult = await firestoreService.getUserProgramsWithParticipantData(userId);
      const userPrograms = userProgramsResult.success ? userProgramsResult.data || [] : [];

      const dependentCommits: string[] = [];
      const dependentPrograms: string[] = [];

      // For Camera, check if any commits use photo/video verification
      if (provider === 'camera') {
        activeCommits.forEach(commit => {
          if ((commit.evidence?.type === 'photo' || commit.evidence?.type === 'video' || commit.evidence?.type === 'camera-only') &&
              commit.status === 'active' &&
              (commit as any).active !== false) {
            const commitTitle = commit.title || 'Unknown commit';
            const frequency = commit.schedule?.frequency || 'daily';
            dependentCommits.push(`${commitTitle} (Personal ${frequency} commitment)`);
          }
        });

        // Check for programs with photo/video categories
        userPrograms.forEach(program => {
          if (program && (program.category === 'photo' || program.category === 'video') &&
              (program.status === 'ongoing' || program.status === 'active' || program.status === 'upcoming')) {
            const programName = program.name || 'Unknown program';
            const statusText = program.status === 'upcoming' ? 'Starting soon' : 'Currently active';
            const duration = program.duration ? `${program.duration} days` : '';
            const durationText = duration ? ` - ${duration}` : '';
            dependentPrograms.push(`${programName} (${statusText} program${durationText})`);
          }
        });
      }

      // For GPS, check if any commits use GPS verification
      if (provider === 'gps') {
        activeCommits.forEach(commit => {
          if ((commit.evidence?.type === 'gps-checkin' || commit.evidence?.type === 'gps-avoid') &&
              commit.status === 'active' &&
              (commit as any).active !== false) {
            const commitTitle = commit.title || 'Unknown commit';
            const frequency = commit.schedule?.frequency || 'daily';
            dependentCommits.push(`${commitTitle} (Personal ${frequency} commitment)`);
          }
        });

        // Check for programs with GPS-related categories
        userPrograms.forEach(program => {
          if (program && (program.category === 'gym' || program.category === 'location') &&
              (program.status === 'ongoing' || program.status === 'active' || program.status === 'upcoming')) {
            const programName = program.name || 'Unknown program';
            const statusText = program.status === 'upcoming' ? 'Starting soon' : 'Currently active';
            const duration = program.duration ? `${program.duration} days` : '';
            const durationText = duration ? ` - ${duration}` : '';
            dependentPrograms.push(`${programName} (${statusText} program${durationText})`);
          }
        });
      }

      // For Gallery, check if any commits use photo uploads from gallery
      if (provider === 'gallery') {
        activeCommits.forEach(commit => {
          if ((commit.evidence?.type === 'photo' || commit.evidence?.type === 'honor') &&
              commit.status === 'active' &&
              (commit as any).active !== false) {
            const commitTitle = commit.title || 'Unknown commit';
            const frequency = commit.schedule?.frequency || 'daily';
            dependentCommits.push(`${commitTitle} (Personal ${frequency} commitment)`);
          }
        });

        // Check for programs with photo categories
        userPrograms.forEach(program => {
          if (program && program.category === 'photo' &&
              (program.status === 'ongoing' || program.status === 'active' || program.status === 'upcoming')) {
            const programName = program.name || 'Unknown program';
            const statusText = program.status === 'upcoming' ? 'Starting soon' : 'Currently active';
            const duration = program.duration ? `${program.duration} days` : '';
            const durationText = duration ? ` - ${duration}` : '';
            dependentPrograms.push(`${programName} (${statusText} program${durationText})`);
          }
        });
      }

      // For GitHub, check if any commits use GitHub verification
      if (provider === 'github') {
        activeCommits.forEach(commit => {
          // Check both evidence type and ensure commit is active (double-check for active field if it exists)
          if (commit.evidence?.type === 'github' &&
              commit.status === 'active' &&
              (commit as any).active !== false) {
            const commitTitle = commit.title || 'Unknown commit';
            const frequency = commit.schedule?.frequency || 'daily';
            dependentCommits.push(`${commitTitle} (Personal ${frequency} commitment)`);
          }
        });

        // Check for programs with GitHub category and ongoing/upcoming status
        userPrograms.forEach(program => {
          if (program && program.category === 'github' &&
              (program.status === 'ongoing' || program.status === 'active' || program.status === 'upcoming')) {
            const programName = program.name || 'Unknown program';
            const statusText = program.status === 'upcoming' ? 'Starting soon' : 'Currently active';
            const duration = program.duration ? `${program.duration} days` : '';
            const durationText = duration ? ` - ${duration}` : '';
            dependentPrograms.push(`${programName} (${statusText} program${durationText})`);
          }
        });
      }

      // For Strava, check if any commits use Strava verification
      if (provider === 'strava') {
        activeCommits.forEach(commit => {
          // Check both evidence type and ensure commit is active (double-check for active field if it exists)
          if (commit.evidence?.type === 'strava' &&
              commit.status === 'active' &&
              (commit as any).active !== false) {
            const commitTitle = commit.title || 'Unknown commit';
            const frequency = commit.schedule?.frequency || 'daily';
            dependentCommits.push(`${commitTitle} (Personal ${frequency} commitment)`);
          }
        });

        // Check for programs with Strava category and ongoing/upcoming status
        userPrograms.forEach(program => {
          if (program && program.category === 'strava' &&
              (program.status === 'ongoing' || program.status === 'active' || program.status === 'upcoming')) {
            const programName = program.name || 'Unknown program';
            const statusText = program.status === 'upcoming' ? 'Starting soon' : 'Currently active';
            const duration = program.duration ? `${program.duration} days` : '';
            const durationText = duration ? ` - ${duration}` : '';
            dependentPrograms.push(`${programName} (${statusText} program${durationText})`);
          }
        });
      }

      return {
        hasActivePrograms: dependentPrograms.length > 0,
        hasActiveCommits: dependentCommits.length > 0,
        details: [...dependentCommits, ...dependentPrograms]
      };
    } catch (error) {
      console.error('Error checking dependencies:', error);
      return { hasActivePrograms: false, hasActiveCommits: false, details: [] };
    }
  };

  const handleRevoke = async (provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava') => {
    setRevoking(provider);
    try {
      // Check for dependent programs/commits
      const dependencies = await checkDependentPrograms(provider);

      setRevokeModalData({
        provider,
        hasActivePrograms: dependencies.hasActivePrograms,
        hasActiveCommits: dependencies.hasActiveCommits,
        details: dependencies.details,
      });
      setShowRevokeModal(true);
    } catch (error) {
      console.error('Error checking dependencies:', error);
      setErrorModalData({
        title: 'Error',
        message: 'Failed to check dependencies. Please try again.'
      });
      setErrorModalVisible(true);
    } finally {
      setRevoking(null);
    }
  };

  const resetDependentSetupStatus = async (
    userId: string,
    provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava'
  ) => {
    try {


      // Get fresh data to reset setup status
      const commitsResult = await getActiveUserCommits(userId);
      const activeCommits = commitsResult.success ? commitsResult.data || [] : [];

      const userProgramsResult = await firestoreService.getUserProgramsWithParticipantData(userId);
      const userPrograms = userProgramsResult.success ? userProgramsResult.data || [] : [];

      // Reset setup status for dependent commits
      const commitResetPromises = activeCommits
        .filter(commit => {
          if (provider === 'camera') {
            return (commit.evidence?.type === 'photo' || commit.evidence?.type === 'video' || commit.evidence?.type === 'camera-only') &&
                   commit.status === 'active' &&
                   (commit as any).active !== false;
          } else if (provider === 'gps') {
            return (commit.evidence?.type === 'gps-checkin' || commit.evidence?.type === 'gps-avoid') &&
                   commit.status === 'active' &&
                   (commit as any).active !== false;
          } else if (provider === 'gallery') {
            return (commit.evidence?.type === 'photo' || commit.evidence?.type === 'honor') &&
                   commit.status === 'active' &&
                   (commit as any).active !== false;
          } else if (provider === 'github') {
            return commit.evidence?.type === 'github' &&
                   commit.status === 'active' &&
                   (commit as any).active !== false;
          } else if (provider === 'strava') {
            return commit.evidence?.type === 'strava' &&
                   commit.status === 'active' &&
                   (commit as any).active !== false;
          }
          return false;
        })
        .map(commit => {

          return firestoreService.commits.updateCommit(commit.id!, { setupStatus: false });
        });

      // Reset setup status for dependent program participants
      const participantResetPromises = userPrograms
        .filter(program => {
          if (!program) return false;
          let categoryMatch = false;

          if (provider === 'camera') {
            categoryMatch = program.category === 'photo' || program.category === 'video';
          } else if (provider === 'gps') {
            categoryMatch = program.category === 'gym' || program.category === 'location';
          } else if (provider === 'gallery') {
            categoryMatch = program.category === 'photo';
          } else {
            categoryMatch = program.category === provider;
          }

          const statusMatch = program.status === 'ongoing' || program.status === 'active' || program.status === 'upcoming';
          return categoryMatch && statusMatch;
        })
        .map(program => {

          return firestoreService.participants.updateParticipant(program.id!, userId, { setupStatus: false });
        });

      // Execute all reset operations
      const allResetPromises = [...commitResetPromises, ...participantResetPromises];

      if (allResetPromises.length > 0) {
        await Promise.allSettled(allResetPromises);

      } else {

      }

    } catch (error) {
      console.error('Error resetting dependent setup status:', error);
      // Don't throw error to avoid breaking the main revoke flow
    }
  };

  const performRevoke = async (provider: 'camera' | 'gps' | 'gallery' | 'github' | 'strava') => {
    setRevoking(provider);
    setShowRevokeModal(false);

    try {
      const userId = await getId();
      if (!userId) return;

      // Handle permission-based integrations differently
      if (['camera', 'gps', 'gallery'].includes(provider)) {
        // Reset setup status for dependent commits and programs
        await resetDependentSetupStatus(userId, provider);

        // Clear permission state locally (user will need to re-grant when needed)
        if (provider === 'camera') {
          // Camera permissions can't be revoked programmatically, but we reset our state
          await checkPermissions(); // This will refresh the permission status
        } else if (provider === 'gallery') {
          setGalleryPermission(null);
        } else if (provider === 'gps') {
          setLocationPermission(null);
        }

        setSuccessModalData({
          title: 'Permission Access Reset',
          message: `${provider.charAt(0).toUpperCase() + provider.slice(1)} access has been reset. Setup status has been reset for affected enrollments. You'll need to re-grant permission when needed.`,
          onPress: () => {
            setSuccessModalVisible(false);
            checkPermissions();
          }
        });
        setSuccessModalVisible(true);
      } else {
        // Handle service-based integrations (GitHub, Strava)
        const integration = getIntegrationForProvider(provider as 'github' | 'strava');
        if (!integration) return;

        // Deactivate the integration
        const result = await firestoreService.integrations.deactivateIntegration(userId, integration.id!);

        if (result.success) {
          // Reset setup status for dependent commits and programs
          await resetDependentSetupStatus(userId, provider);

          // Also sign out from the service if it's GitHub
          if (provider === 'github') {
            await githubService.signOut();
          }

          setSuccessModalData({
            title: 'Access Revoked',
            message: `${provider} integration has been revoked successfully. Setup status has been reset for affected enrollments.`,
            onPress: () => {
              setSuccessModalVisible(false);
              loadIntegrations();
            }
          });
          setSuccessModalVisible(true);
        } else {
          setErrorModalData({
            title: 'Error',
            message: result.error || 'Failed to revoke access. Please try again.'
          });
          setErrorModalVisible(true);
        }
      }
    } catch (error) {
      console.error(`Error revoking ${provider}:`, error);
      setErrorModalData({
        title: 'Error',
        message: 'Failed to revoke access. Please try again.'
      });
      setErrorModalVisible(true);
    } finally {
      setRevoking(null);
    }
  };

  const renderRevokeModal = () => {
    if (!revokeModalData) return null;

    const { provider, hasActivePrograms, hasActiveCommits, details } = revokeModalData;
    const providerName = provider.charAt(0).toUpperCase() + provider.slice(1);
    const hasDependencies = hasActivePrograms || hasActiveCommits;
    const dependencyType = hasActiveCommits || hasActivePrograms ? 'enrollments' : 'programs';

    return (
      <Modal
        visible={showRevokeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowRevokeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={[styles.modalContent, {
              backgroundColor: colors.background,
              borderColor: '#FFD700'
            }]}>
              {/* Header with icon */}
              <View style={styles.modalIconContainer}>
                <View style={[styles.modalIconCircle, {
                  backgroundColor: isDark ? 'rgba(255, 107, 107, 0.15)' : 'rgba(255, 107, 107, 0.1)',
                  borderColor: '#FF6B6B'
                }]}>
                  <MaterialCommunityIcons
                    name="link-off"
                    size={24}
                    color="#FF6B6B"
                  />
                </View>
              </View>

              {/* Title */}
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {hasDependencies ? 'Active Enrollments Found' : `Revoke ${providerName} Access?`}
              </Text>

              {/* Message */}
              <Text style={[styles.modalMessage, { color: colors.textMuted }]}>
                {hasDependencies
                  ? `You have active ${dependencyType} that require ${provider} access. Revoking access may affect your ability to submit progress.`
                  : `This will disconnect your ${provider} account from Accustom. You can reconnect it anytime from the integrations page.`
                }
              </Text>

              {/* Dependency list */}
              {details.length > 0 && (
                <View style={[styles.dependencyList, {
                  backgroundColor: isDark ? 'rgba(255, 193, 7, 0.1)' : 'rgba(255, 193, 7, 0.05)',
                  borderColor: isDark ? 'rgba(255, 193, 7, 0.3)' : 'rgba(255, 193, 7, 0.2)'
                }]}>
                  <View style={styles.dependencyHeader}>
                    <MaterialCommunityIcons name="alert-circle-outline" size={16} color="#FFC107" />
                    <Text style={[styles.dependencyTitle, { color: colors.text }]}>Affected Enrollments</Text>
                  </View>
                  {details.slice(0, 3).map((item, index) => (
                    <Text key={index} style={[styles.dependencyItem, { color: colors.textMuted }]}>
                      • {item}
                    </Text>
                  ))}
                  {details.length > 3 && (
                    <Text style={[styles.dependencyItem, { color: colors.textMuted, fontStyle: 'italic' }]}>
                      ... and {details.length - 3} more
                    </Text>
                  )}
                </View>
              )}

              {/* Action buttons */}
              <View style={styles.modalButtons}>
                <TouchableOpacity
                  style={[styles.modalButton, styles.cancelButton, {
                    backgroundColor: isDark ? 'rgba(255, 255, 255, 0.06)' : 'rgba(0, 0, 0, 0.03)',
                    borderColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.12)'
                  }]}
                  onPress={() => setShowRevokeModal(false)}
                  activeOpacity={0.75}
                >
                  <Text style={[styles.cancelButtonText, { color: colors.text }]}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.revokeButtonContainer, {
                    backgroundColor: isDark ? '#FF4444' : '#FF5555',
                    shadowColor: '#FF6B6B',
                    shadowOffset: { width: 0, height: 2 },
                    shadowOpacity: 0.25,
                    shadowRadius: 4,
                    elevation: 4,
                  }]}
                  onPress={() => performRevoke(provider)}
                  activeOpacity={0.85}
                >
                  <View style={styles.revokeButton}>
                    <MaterialCommunityIcons name="link-off" size={16} color="#fff" />
                    <Text style={styles.revokeButtonText}>
                      {hasDependencies ? 'Revoke Anyway' : 'Revoke Access'}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
        <Header/>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={[styles.loadingText, { color: colors.textMuted }]}>Loading integrations...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <Header/>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.titleSection}>
          <View style={styles.titleRow}>
            <TouchableOpacity
              style={styles.titleButton}
              onPress={() => router.back()}
              activeOpacity={0.7}
            >
              <MaterialIcons name="arrow-back" size={20} color={colors.primary} />
            </TouchableOpacity>
            <Text style={[styles.pageTitle, { color: colors.primary }]}>Integrations</Text>
            <View style={styles.titleButtonPlaceholder} />
          </View>
          <Text style={[styles.pageSubtitle, { color: colors.textMuted }]}>
            Link your accounts for seamless verification
          </Text>
        </View>

        <IntegrationCard
          provider="camera"
          integration={null}
          onConnect={() => handleConnect('camera')}
          onRevoke={() => handleRevoke('camera')}
          connecting={connecting === 'camera'}
          revoking={revoking === 'camera'}
          permissionGranted={cameraPermission?.granted || false}
        />

        <IntegrationCard
          provider="gps"
          integration={null}
          onConnect={() => handleConnect('gps')}
          onRevoke={() => handleRevoke('gps')}
          connecting={connecting === 'gps'}
          revoking={revoking === 'gps'}
          permissionGranted={locationPermission?.status === 'granted'}
        />

        <IntegrationCard
          provider="gallery"
          integration={null}
          onConnect={() => handleConnect('gallery')}
          onRevoke={() => handleRevoke('gallery')}
          connecting={connecting === 'gallery'}
          revoking={revoking === 'gallery'}
          permissionGranted={galleryPermission?.granted || false}
        />

        <IntegrationCard
          provider="github"
          integration={getIntegrationForProvider('github')}
          onConnect={() => handleConnect('github')}
          onRevoke={() => handleRevoke('github')}
          connecting={connecting === 'github'}
          revoking={revoking === 'github'}
        />

        <IntegrationCard
          provider="strava"
          integration={null}
          onConnect={() => {}}
          onRevoke={() => {}}
          connecting={false}
          revoking={false}
          comingSoon={true}
        />

        <IntegrationCard
          provider="leetcode"
          integration={null}
          onConnect={() => {}}
          onRevoke={() => {}}
          connecting={false}
          revoking={false}
          comingSoon={true}
        />

        <IntegrationCard
          provider="google_fit"
          integration={null}
          onConnect={() => {}}
          onRevoke={() => {}}
          connecting={false}
          revoking={false}
          comingSoon={true}
        />

        <IntegrationCard
          provider="apple_health"
          integration={null}
          onConnect={() => {}}
          onRevoke={() => {}}
          connecting={false}
          revoking={false}
          comingSoon={true}
        />
      </ScrollView>

      {renderRevokeModal()}

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
        onButtonPress={successModalData.onPress}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  // Content styles
  content: {
    flex: 1,
    padding: 20,
  },
  titleSection: {
    marginBottom: 24,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  titleButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 215, 0, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleButtonPlaceholder: {
    width: 40,
    height: 40,
  },
  pageTitle: {
    fontSize: 24,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    flex: 1,
  },
  pageSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    lineHeight: 20,
  },
  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
  },
  // Integration card styles
  integrationCard: {
    marginBottom: 12,
    borderRadius: 12,
    padding: 16,
    // Shadow applied via designSystem.shadows.md in component
  },
  integrationHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  integrationIconGradient: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    // Shadow applied via designSystem.shadows.sm in component
  },
  integrationInfo: {
    flex: 1,
  },
  integrationTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  integrationName: {
    fontSize: 15,
    fontFamily: 'MontserratBold',
  },
  integrationDescription: {
    fontSize: 11,
    fontFamily: 'MontserratRegular',
    marginBottom: 6,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 10,
  },
  statusText: {
    fontSize: 10,
    fontFamily: 'MontserratBold',
    color: '#fff',
    textTransform: 'uppercase',
  },
  connectionDetails: {
    marginTop: 6,
  },
  connectionDetailsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  connectionInfo: {
    flex: 1,
  },
  detailText: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    marginBottom: 2,
  },
  syncText: {
    fontSize: 11,
    fontFamily: 'MontserratRegular',
    fontStyle: 'italic',
  },

  unlinkButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  connectButtonSmall: {
    borderRadius: 16,
    // Shadow will be applied via designSystem.shadows.sm when used
  },
  connectButtonSmallGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
    gap: 4,
  },
  connectButtonSmallText: {
    fontSize: 12,
    fontFamily: 'MontserratBold',
    color: '#000000', // Fallback color since this is outside component context
  },

  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  modalContainer: {
    width: '100%',
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  modalContent: {
    borderRadius: 20,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    padding: 28,
    width: '100%',
    borderWidth: 2,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 12,
    elevation: 8,
  },
  modalIconContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  modalIconCircle: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 12,
  },
  modalMessage: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 20,
  },
  dependencyList: {
    marginBottom: 20,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  dependencyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
    gap: 6,
  },
  dependencyTitle: {
    fontSize: 13,
    fontFamily: 'MontserratBold',
  },
  dependencyItem: {
    fontSize: 12,
    fontFamily: 'MontserratRegular',
    marginBottom: 4,
    paddingLeft: 8,
    lineHeight: 16,
  },
  modalButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  modalButton: {
    flex: 1,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    borderWidth: 1.5,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    letterSpacing: 0.3,
  },
  revokeButtonContainer: {
    flex: 1,
    borderRadius: 12,
  },
  revokeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 12,
    gap: 8,
  },
  revokeButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: '#FFFFFF',
    letterSpacing: 0.3,
  },
});
