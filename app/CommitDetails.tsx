import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  BackH<PERSON><PERSON>,
  <PERSON>ert,
} from "react-native";
import { useLocalSearchParams, useRouter } from "expo-router";
import { firestoreService } from "../lib/services/database";
import { MaterialCommunityIcons, FontAwesome5, MaterialIcons } from "@expo/vector-icons";
import { getId } from "@/lib/utils/variables";
import { ProtectedRoute } from "@/ui/common/ProtectedRoute";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { Commit, UserCommit } from "@/lib/services/database/types";

// Create category icons function that uses theme colors
const getCategoryIcons = (primaryColor: string): Record<string, JSX.Element> => ({
  fitness: <MaterialCommunityIcons name="weight-lifter" size={60} color={primaryColor} />,
  health: <MaterialCommunityIcons name="heart" size={60} color={primaryColor} />,
  productivity: <MaterialCommunityIcons name="laptop" size={60} color={primaryColor} />,
  learning: <FontAwesome5 name="book-open" size={60} color={primaryColor} />,
  mindfulness: <MaterialCommunityIcons name="meditation" size={60} color={primaryColor} />,
  creativity: <MaterialCommunityIcons name="pencil" size={60} color={primaryColor} />,
  social: <MaterialCommunityIcons name="account-group" size={60} color={primaryColor} />,
  finance: <MaterialCommunityIcons name="currency-usd" size={60} color={primaryColor} />,
  other: <MaterialCommunityIcons name="tag" size={60} color={primaryColor} />,
});

const CommitDetailsComponent: React.FC = () => {
  const { colors, isDark } = useTheme();
  const categoryIcons = getCategoryIcons(colors.primary);
  const styles = createStyles(colors, isDark);

  const { commitId } = useLocalSearchParams<{
    commitId?: string;
  }>();
  const router = useRouter();
  const [commit, setCommit] = useState<Commit | null>(null);
  const [userCommit, setUserCommit] = useState<UserCommit | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [userId, setUserId] = useState<string>("");

  // Combined fetching of userId and commit data
  useEffect(() => {
    const fetchData = async () => {
      try {
        const id = String(await getId());
        setUserId(id);
        if (!commitId) return;
        setLoading(true);

        // Fetch both commit and user commit data
        const [commitResult, userCommitResult] = await Promise.all([
          firestoreService.commits.getCommitById(commitId),
          firestoreService.commits.getUserCommit(id, commitId)
        ]);

        if (commitResult.success && commitResult.data) {
          setCommit(commitResult.data as Commit);
        } else {
          Alert.alert("Error", "Commit not found");
          router.back();
          return;
        }

        if (userCommitResult.success && userCommitResult.data) {
          setUserCommit(userCommitResult.data as UserCommit);
        }
      } catch (error) {
        console.error("Error fetching commit:", error);
        Alert.alert("Error", "Something went wrong");
        router.back();
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [commitId, router]);

  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  }, []);

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case 'daily': return 'Daily';
      case 'weekly': return 'Weekly';
      case 'monthly': return 'Monthly';
      case 'once': return 'One-time';
      default: return frequency;
    }
  };

  const getEvidenceTypeText = (evidenceType: string) => {
    switch (evidenceType) {
      case 'photo': return 'Photo';
      case 'camera-only': return 'Camera Only';
      case 'video': return 'Video';
      case 'video-timelapse': return 'Video Timelapse';
      case 'screen-time': return 'Screen Time';
      case 'github': return 'GitHub Commits';
      case 'strava': return 'Strava Activity';
      case 'gps-checkin': return 'GPS Check-in';
      case 'gps-avoid': return 'GPS Avoidance';
      case 'honor': return 'Honor System';
      default: return evidenceType;
    }
  };

  const getCategoryFromEvidence = (evidenceType: string) => {
    switch (evidenceType) {
      case 'photo':
      case 'camera-only':
      case 'video':
      case 'video-timelapse':
        return 'creativity';
      case 'github':
        return 'productivity';
      case 'strava':
      case 'gps-checkin':
      case 'gps-avoid':
        return 'fitness';
      case 'screen-time':
        return 'productivity';
      case 'honor':
        return 'other';
      default:
        return 'other';
    }
  };

  const calculateTotalDuration = () => {
    if (!commit) return 0;
    
    const frequency = commit.schedule.frequency;
    const duration = commit.schedule.duration;
    const endDate = commit.schedule.endDate;
    
    if (frequency === 'daily') {
      if (endDate && commit.schedule.startDate) {
        const start = new Date(commit.schedule.startDate);
        const end = new Date(endDate);
        return Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
      } else if (duration) {
        return duration;
      }
    } else if (frequency === 'weekly') {
      return duration || 1;
    } else if (frequency === 'monthly') {
      return duration || 1;
    } else if (frequency === 'once') {
      return 1;
    }
    
    return 1;
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!commit) return null;

  const totalDuration = calculateTotalDuration();
  const completionRate = userCommit ? 
    Math.round((userCommit.progress.completedReports / userCommit.progress.totalReports) * 100) : 0;

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.surface }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleWithIcon}>
            {categoryIcons[getCategoryFromEvidence(commit.evidence.type)] || (
              <MaterialCommunityIcons name="tag" size={40} color={colors.primary} />
            )}
            <Text style={[styles.commitTitle, { color: colors.text }]}>{commit.title}</Text>
          </View>
          
          {commit.description && (
            <Text style={[styles.commitDescription, { color: colors.textSecondary }]}>
              {commit.description}
            </Text>
          )}

          <View style={styles.statusContainer}>
            <View style={[styles.statusBadge, { 
              backgroundColor: commit.status === 'active' ? colors.success : 
                              commit.status === 'completed' ? colors.primary :
                              commit.status === 'failed' ? colors.error : colors.textMuted 
            }]}>
              <Text style={[styles.statusText, { 
                color: commit.status === 'active' || commit.status === 'completed' ? '#000' : '#fff' 
              }]}>
                {commit.status.charAt(0).toUpperCase() + commit.status.slice(1)}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Progress Stats */}
      {userCommit && (
        <View style={[styles.progressCard, { backgroundColor: colors.surface }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Progress Overview</Text>
          <View style={styles.progressStatsContainer}>
            <View style={styles.progressStat}>
              <Text style={[styles.progressStatValue, { color: colors.primary }]}>
                {completionRate}%
              </Text>
              <Text style={[styles.progressStatLabel, { color: colors.textMuted }]}>
                Completion Rate
              </Text>
            </View>
            <View style={styles.progressStat}>
              <Text style={[styles.progressStatValue, { color: colors.text }]}>
                {userCommit.progress.completedReports}
              </Text>
              <Text style={[styles.progressStatLabel, { color: colors.textMuted }]}>
                Completed
              </Text>
            </View>
            <View style={styles.progressStat}>
              <Text style={[styles.progressStatValue, { color: colors.text }]}>
                {userCommit.progress.currentStreak}
              </Text>
              <Text style={[styles.progressStatLabel, { color: colors.textMuted }]}>
                Current Streak
              </Text>
            </View>
            <View style={styles.progressStat}>
              <Text style={[styles.progressStatValue, { color: colors.text }]}>
                {userCommit.progress.longestStreak}
              </Text>
              <Text style={[styles.progressStatLabel, { color: colors.textMuted }]}>
                Best Streak
              </Text>
            </View>
          </View>
        </View>
      )}

      {/* Commitment Details */}
      <View style={[styles.detailsCard, { backgroundColor: colors.surface }]}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>Commitment Details</Text>
        
        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Frequency</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {getFrequencyText(commit.schedule.frequency)}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Evidence Type</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {getEvidenceTypeText(commit.evidence.type)}
          </Text>
        </View>

        <View style={styles.detailRow}>
          <Text style={[styles.detailLabel, { color: colors.textMuted }]}>Category</Text>
          <Text style={[styles.detailValue, { color: colors.text }]}>
            {getCategoryFromEvidence(commit.evidence.type).charAt(0).toUpperCase() + getCategoryFromEvidence(commit.evidence.type).slice(1)}
          </Text>
        </View>

        <View style={[styles.divider, { backgroundColor: colors.separator }]} />

        <View style={styles.dateInfoContainer}>
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>Start Date</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>
              {formatDate(commit.schedule.startDate)}
            </Text>
          </View>
          
          {commit.schedule.endDate && (
            <>
              <View style={[styles.verticalDivider, { backgroundColor: colors.separator }]} />
              <View style={styles.dateItem}>
                <Text style={[styles.dateLabel, { color: colors.textMuted }]}>End Date</Text>
                <Text style={[styles.dateValue, { color: colors.text }]}>
                  {formatDate(commit.schedule.endDate)}
                </Text>
              </View>
            </>
          )}
          
          <View style={[styles.verticalDivider, { backgroundColor: colors.separator }]} />
          <View style={styles.dateItem}>
            <Text style={[styles.dateLabel, { color: colors.textMuted }]}>Duration</Text>
            <Text style={[styles.dateValue, { color: colors.text }]}>
              {totalDuration} {commit.schedule.frequency === 'daily' ? 'Days' : 
                            commit.schedule.frequency === 'weekly' ? 'Weeks' :
                            commit.schedule.frequency === 'monthly' ? 'Months' : 'Time'}
            </Text>
          </View>
        </View>
      </View>

      <TouchableOpacity
        style={[styles.backButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
        onPress={() => router.back()}
      >
        <Text style={[styles.backButtonText, { color: colors.text }]}>Go Back</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const createStyles = (colors: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background
  },
  header: {
    backgroundColor: colors.surface,
    paddingTop: 40,
    paddingBottom: 12,
    paddingHorizontal: 20,
    alignItems: "center",
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  headerContent: { alignItems: "center" },
  titleWithIcon: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  commitTitle: {
    fontSize: 24,
    fontFamily: "MontserratBold",
    color: colors.text,
    marginLeft: 12,
    textAlign: "center",
    flex: 1,
  },
  commitDescription: {
    fontSize: 16,
    color: colors.textSecondary,
    marginTop: 4,
    textAlign: "center",
    fontFamily: "MontserratRegular",
    paddingHorizontal: 20,
  },
  statusContainer: {
    marginTop: 8,
    alignItems: "center",
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontFamily: "MontserratBold",
  },
  progressCard: {
    backgroundColor: colors.surface,
    marginHorizontal: 10,
    marginTop: 10,
    borderRadius: 15,
    padding: 15,
    shadowColor: isDark ? "#000" : "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: isDark ? 0.4 : 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  progressStatsContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 10,
  },
  progressStat: {
    alignItems: "center",
  },
  progressStatValue: {
    fontSize: 20,
    fontFamily: "MontserratBold",
  },
  progressStatLabel: {
    fontSize: 12,
    fontFamily: "MontserratRegular",
    marginTop: 2,
  },
  detailsCard: {
    backgroundColor: colors.surface,
    marginHorizontal: 10,
    marginTop: 10,
    marginBottom: 5,
    borderRadius: 15,
    padding: 15,
    shadowColor: isDark ? "#000" : "#000",
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: isDark ? 0.4 : 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: "MontserratBold",
    color: colors.primary,
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginVertical: 6,
  },
  detailLabel: {
    fontSize: 14,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  detailValue: {
    fontSize: 14,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  dateInfoContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    marginVertical: 6,
  },
  dateItem: { alignItems: "center", flex: 1 },
  dateLabel: {
    fontSize: 12,
    color: colors.textMuted,
    fontFamily: "MontserratRegular",
  },
  dateValue: {
    fontSize: 14,
    color: colors.text,
    fontFamily: "MontserratBold"
  },
  verticalDivider: {
    borderLeftWidth: 1,
    borderLeftColor: colors.separator,
    marginHorizontal: 10,
    height: 40,
  },
  divider: {
    borderBottomWidth: 1,
    borderBottomColor: colors.separator,
    marginVertical: 10,
  },
  backButton: {
    marginHorizontal: 10,
    marginTop: 15,
    paddingVertical: 12,
    borderRadius: 15,
    alignItems: "center",
    borderWidth: 1,
    borderColor: colors.border,
  },
  backButtonText: {
    color: colors.textMuted,
    fontSize: 16,
    fontFamily: "MontserratRegular",
  },
});

const CommitDetails: React.FC = () => {
  return (
    <ProtectedRoute>
      <CommitDetailsComponent />
    </ProtectedRoute>
  );
};

export default CommitDetails;
