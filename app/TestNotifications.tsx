import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { testNotificationService } from '@/lib/services/notifications/TestNotificationService';
import { getId } from '@/lib/utils/variables';

interface TestResult {
  test: string;
  passed: boolean;
  error?: string;
}

const TestNotifications: React.FC = () => {
  const router = useRouter();
  const { colors } = useTheme();
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [userId, setUserId] = useState<string>('');

  React.useEffect(() => {
    const fetchUserId = async () => {
      try {
        const id = await getId();
        if (id) {
          setUserId(id);
        }
      } catch (error) {
        console.error('Error getting user ID:', error);
      }
    };
    fetchUserId();
  }, []);

  const runSingleTest = async (testName: string, testFn: () => Promise<boolean>) => {
    if (!userId) {
      Alert.alert('Error', 'User ID not available. Please make sure you are logged in.');
      return;
    }

    setLoading(true);
    try {
      const success = await testFn();
      const result: TestResult = { test: testName, passed: success };
      setTestResults(prev => [...prev, result]);
      
      Alert.alert(
        success ? 'Test Passed' : 'Test Failed',
        `${testName}: ${success ? 'Success' : 'Failed'}`
      );
    } catch (error) {
      const result: TestResult = { 
        test: testName, 
        passed: false, 
        error: error instanceof Error ? error.message : String(error)
      };
      setTestResults(prev => [...prev, result]);
      
      Alert.alert('Test Error', `${testName} failed with error: ${result.error}`);
    } finally {
      setLoading(false);
    }
  };

  const runAllTests = async () => {
    if (!userId) {
      Alert.alert('Error', 'User ID not available. Please make sure you are logged in.');
      return;
    }

    setLoading(true);
    setTestResults([]);
    
    try {
      const results = await testNotificationService.runAllTests(userId);
      setTestResults(results.results);
      
      Alert.alert(
        'All Tests Complete',
        `Results: ${results.passed} passed, ${results.failed} failed`
      );
    } catch (error) {
      Alert.alert('Error', `Failed to run tests: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testButtons = [
    {
      title: 'Test Simple Push',
      onPress: () => runSingleTest('Simple Push', () => testNotificationService.testSimplePushNotification(userId)),
    },
    {
      title: 'Test Program Notification',
      onPress: () => runSingleTest('Program Notification', () => testNotificationService.testProgramNotification(userId, 'test-program-id')),
    },
    {
      title: 'Test Reminder',
      onPress: () => runSingleTest('Reminder', () => testNotificationService.testReminderNotification(userId)),
    },
    {
      title: 'Test High Priority',
      onPress: () => runSingleTest('High Priority', () => testNotificationService.testHighPriorityNotification(userId)),
    },
    {
      title: 'Test Data-Only',
      onPress: () => runSingleTest('Data-Only', () => testNotificationService.testDataOnlyNotification(userId)),
    },
    {
      title: 'Test Direct FCM',
      onPress: () => runSingleTest('Direct FCM', () => testNotificationService.testDirectFcmCall(userId)),
    },
    {
      title: 'Test Token Registration',
      onPress: () => runSingleTest('Token Registration', () => testNotificationService.testTokenRegistration(userId)),
    },
  ];

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          Push Notification Tests
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          Test the complete push notification flow
        </Text>
        {userId && (
          <Text style={[styles.userId, { color: colors.textSecondary }]}>
            User ID: {userId.substring(0, 8)}...
          </Text>
        )}
      </View>

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.runAllButton, { backgroundColor: colors.primary }]}
          onPress={runAllTests}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color={colors.background} />
          ) : (
            <Text style={[styles.buttonText, { color: colors.background }]}>
              Run All Tests
            </Text>
          )}
        </TouchableOpacity>

        <View style={styles.individualTests}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Individual Tests
          </Text>
          {testButtons.map((button, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.testButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
              onPress={button.onPress}
              disabled={loading}
            >
              <Text style={[styles.testButtonText, { color: colors.text }]}>
                {button.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {testResults.length > 0 && (
        <View style={styles.resultsContainer}>
          <View style={styles.resultsHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Test Results
            </Text>
            <TouchableOpacity
              style={[styles.clearButton, { backgroundColor: colors.error }]}
              onPress={clearResults}
            >
              <Text style={[styles.clearButtonText, { color: colors.background }]}>
                Clear
              </Text>
            </TouchableOpacity>
          </View>
          
          {testResults.map((result, index) => (
            <View
              key={index}
              style={[
                styles.resultItem,
                { 
                  backgroundColor: colors.surface,
                  borderColor: result.passed ? colors.success : colors.error,
                }
              ]}
            >
              <View style={styles.resultHeader}>
                <Text style={[styles.resultTest, { color: colors.text }]}>
                  {result.test}
                </Text>
                <Text style={[
                  styles.resultStatus,
                  { color: result.passed ? colors.success : colors.error }
                ]}>
                  {result.passed ? '✅ PASS' : '❌ FAIL'}
                </Text>
              </View>
              {result.error && (
                <Text style={[styles.resultError, { color: colors.error }]}>
                  Error: {result.error}
                </Text>
              )}
            </View>
          ))}
        </View>
      )}

      <TouchableOpacity
        style={[styles.backButton, { backgroundColor: colors.surface, borderColor: colors.border }]}
        onPress={() => router.back()}
      >
        <Text style={[styles.backButtonText, { color: colors.text }]}>
          Back
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 30,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 8,
  },
  userId: {
    fontSize: 12,
    fontFamily: 'monospace',
  },
  buttonContainer: {
    marginBottom: 30,
  },
  runAllButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  individualTests: {
    marginTop: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  testButton: {
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 8,
    alignItems: 'center',
  },
  testButtonText: {
    fontSize: 14,
  },
  resultsContainer: {
    marginBottom: 30,
  },
  resultsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  clearButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  clearButtonText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  resultItem: {
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    marginBottom: 8,
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  resultTest: {
    fontSize: 14,
    fontWeight: '500',
    flex: 1,
  },
  resultStatus: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  resultError: {
    fontSize: 12,
    marginTop: 4,
    fontStyle: 'italic',
  },
  backButton: {
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 16,
  },
});

export default TestNotifications;
