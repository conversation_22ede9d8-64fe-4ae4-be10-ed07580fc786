import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { firestoreService } from '../lib/services/database';
import { LinearGradient } from 'expo-linear-gradient';
import { ProtectedRoute } from '@/ui/common/ProtectedRoute';
import { useTheme } from '@/shared/contexts/ThemeContext';

interface UserProgram {
  id: string;
  name: string;
}

interface UserData {
  fname: string;
  lname: string;
  email: string;
  points: number;
}

function UserProfileComponent() {
  const { userId } = useLocalSearchParams();
  const router = useRouter();
  const { colors, isDark } = useTheme();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [userPrograms, setUserPrograms] = useState<UserProgram[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!userId || typeof userId !== 'string') return;

      try {
        const result = await firestoreService.getUserDashboardData(userId);

        if (result.success && result.data) {
          const { user, programs } = result.data;
          setUserData({
            fname: user.fname || '',
            lname: user.lname || '',
            email: user.email || '',
            points: user.streak || 0
          });

          setUserPrograms(programs.map(p => ({
            id: p.id,
            name: p.name || 'Unnamed Program'
          })));
        } else {
          console.error('User document not found');
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [userId]);

  const styles = createStyles(colors);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!userData) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>User not found</Text>
      </View>
    );
  }

  const initials = `${userData.fname.charAt(0)}${userData.lname.charAt(0)}`;

  return (
    <View style={styles.container}>
      <TouchableOpacity 
        style={styles.backButton} 
        onPress={() => router.back()}
      >
        <Text style={styles.backButtonText}>←</Text>
      </TouchableOpacity>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <LinearGradient
          colors={isDark ? ['#1e1e1e', '#101010'] : [colors.surface, colors.background]}
          style={styles.header}
        >
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>{initials}</Text>
          </View>
          <View style={styles.userInfo}>
            <Text style={styles.name}>{`${userData.fname} ${userData.lname}`}</Text>
            <Text style={styles.email}>{userData.email}</Text>
          </View>
        </LinearGradient>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{userData.points}</Text>
            <Text style={styles.statLabel}>Points</Text>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Enrolled Programs</Text>
          {userPrograms.length > 0 ? (
            userPrograms.map((program) => (
              <View key={program.id} style={styles.programItem}>
                <Text style={styles.programName}>{program.name}</Text>
              </View>
            ))
          ) : (
            <Text style={styles.noPrograms}>No programs enrolled</Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
    borderRadius: 10,
    marginBottom: 20,
  },
  avatar: {
    backgroundColor: colors.primary,
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
    elevation: 5,
  },
  avatarText: {
    fontSize: 32,
    fontFamily: 'MontserratBold',
    color: colors.background,
  },
  userInfo: {
    alignItems: 'center',
  },
  name: {
    fontSize: 26,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 4,
  },
  email: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 20,
    backgroundColor: colors.surface,
    marginHorizontal: 20,
    borderRadius: 10,
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
    elevation: 5,
  },
  statItem: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  statValue: {
    fontSize: 28,
    fontFamily: 'MontserratBold',
    color: colors.primary,
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    color: colors.textMuted,
    marginTop: 4,
  },
  section: {
    paddingHorizontal: 20,
    paddingTop: 30,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'MontserratBold',
    color: colors.primary,
    marginBottom: 16,
  },
  programItem: {
    backgroundColor: colors.surface,
    padding: 16,
    borderRadius: 10,
    marginBottom: 12,
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
    elevation: 3,
  },
  programName: {
    fontSize: 18,
    fontFamily: 'MontserratRegular',
    color: colors.text,
  },
  errorText: {
    color: colors.error,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    marginTop: 20,
  },
  noPrograms: {
    color: colors.textMuted,
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    marginTop: 10,
  },
  backButton: {
    position: 'absolute',
    top: 40,
    left: 20,
    zIndex: 10,
    padding: 10,
    backgroundColor: colors.surface,
    borderRadius: 20,
  },
  backButtonText: {
    color: colors.primary,
    fontSize: 24,
    fontFamily: 'MontserratBold',
  },
});

export default function UserProfile() {
  return (
    <ProtectedRoute>
      <UserProfileComponent />
    </ProtectedRoute>
  );
}
