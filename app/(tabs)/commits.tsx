// Commits Tab - Create personal commitments and challenge yourself at your own pace

import React, { useCallback, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Platform,
} from "react-native";
import { useRouter, useFocusEffect } from "expo-router";
import { BlurView } from 'expo-blur';
import Header from "@/ui/common/Header";
import { useTheme } from "@/shared/contexts/ThemeContext";
import TemplatesGrid, { CommitTemplate } from "@/ui/components/TemplatesGrid";
import { VantaDotsBackground } from "@/shared/components/ui/VantaDots";
import { ResponsiveFontSizes, useResponsiveFontSize } from "@/shared/utils/responsiveFonts";

// Platform-specific blur configuration with proper containment
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 8, // Lower intensity to prevent global effects
      tint: 'dark' as const
    };
  } else if (Platform.OS === 'android') {
    return {
      intensity: 12, // Lower intensity
      tint: 'dark' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  } else {
    // iOS
    return {
      intensity: 15, // Lower intensity
      tint: 'systemMaterialDark' as const
    };
  }
};

// Simple Floating Dot Component - COMPLETELY NEW APPROACH
const FloatingDot: React.FC<{
  x: number;
  y: number;
  size: number;
  delay: number;
}> = ({ x, y, size, delay }) => {
  const opacity = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const animate = () => {
      // Reset
      opacity.setValue(0);
      translateY.setValue(0);

      // Simple fade in, float up, fade out
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.6,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.parallel([
          Animated.timing(translateY, {
            toValue: -20,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        setTimeout(animate, 1000);
      });
    };

    setTimeout(animate, delay);
  }, [delay, opacity, translateY]);

  return (
    <Animated.View
      style={{
        position: 'absolute',
        left: x,
        top: y,
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: '#FFD700',
        opacity,
        transform: [{ translateY }],
        zIndex: 1,
      }}
    />
  );
};

// Animated Button Component with Vanta.js-style animation
const AnimatedCreateButton: React.FC<{
  onPress: () => void;
  disabled: boolean;
  loading: boolean;
  designSystem: any;
  text?: string;
}> = ({ onPress, disabled, loading, designSystem, text = "Create Commit" }) => {
  const [isPressed, setIsPressed] = React.useState(false);

  // Reset the pressed state when screen comes into focus (when coming back)
  useFocusEffect(
    React.useCallback(() => {
      setIsPressed(false);
    }, [])
  );

  const handlePress = () => {
    // Disable blur immediately when pressed
    setIsPressed(true);
    // Use requestAnimationFrame for smoother navigation
    requestAnimationFrame(() => {
      onPress();
    });
  };
  // Create dots with fixed positions across the FULL button width - BIGGER SIZES
  const dots = [
    // Left side
    { x: 20, y: 20, size: 20, delay: 0 },
    { x: 60, y: 35, size: 25, delay: 200 },
    { x: 40, y: 55, size: 18, delay: 400 },

    // Left-center
    { x: 100, y: 25, size: 22, delay: 600 },
    { x: 130, y: 50, size: 19, delay: 800 },
    { x: 80, y: 60, size: 24, delay: 1000 },

    // Center
    { x: 170, y: 30, size: 21, delay: 1200 },
    { x: 200, y: 45, size: 28, delay: 1400 },
    { x: 180, y: 15, size: 17, delay: 1600 },

    // Right-center
    { x: 240, y: 35, size: 23, delay: 1800 },
    { x: 270, y: 20, size: 20, delay: 2000 },
    { x: 250, y: 55, size: 19, delay: 2200 },

    // Far right side - EXTENDED
    { x: 310, y: 40, size: 26, delay: 2400 },
    { x: 340, y: 25, size: 22, delay: 2600 },
    { x: 370, y: 50, size: 18, delay: 2800 },
    { x: 400, y: 35, size: 24, delay: 3000 },
    { x: 430, y: 45, size: 27, delay: 3200 },
  ];

  return (
    <TouchableOpacity
      style={{
        borderRadius: designSystem.borderRadius.xl,
        overflow: 'hidden',
        marginVertical: 10,
        borderWidth: 2,
        borderColor: '#FFD700', // Golden border
        ...designSystem.shadows.xl,
        shadowColor: '#FFD700',
      }}
      onPress={handlePress}
      disabled={disabled || loading || isPressed}
      activeOpacity={0.8}
    >
      {/* Black Button Background */}
      <View
        style={{
          paddingVertical: designSystem.spacing.xxl * 1.5, // Much taller button
          paddingHorizontal: designSystem.spacing.xxl,
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          backgroundColor: '#000000', // Black background
          minHeight: 80, // Ensure minimum height
          width: '100%', // Full width
          overflow: 'hidden', // Contain particles within button
        }}
      >
        {/* Floating Dots Animation - Fixed positions */}
        {dots.map((dot, index) => (
          <FloatingDot
            key={index}
            x={dot.x}
            y={dot.y}
            size={dot.size}
            delay={dot.delay}
          />
        ))}

        {/* Properly contained BlurView overlay - only when not pressed */}
        {!isPressed && (
          <BlurView
            intensity={getBlurConfig().intensity}
            tint={getBlurConfig().tint}
            {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 2,
              // Ensure blur is contained within button bounds
              borderRadius: designSystem.borderRadius.xl,
              overflow: 'hidden',
            }}
          />
        )}

        {/* Button Content - Above blur */}
        <View style={{
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 30, // Above blur layer
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
        }}>
          <Text style={{
            color: "#FFD700", // Gold text color
            fontSize: designSystem.typography.fontSize.xl,
            fontFamily: "MontserratBold",
            letterSpacing: designSystem.typography.letterSpacing.wide,
            textShadowColor: 'rgba(0, 0, 0, 0.8)',
            textShadowOffset: { width: 0, height: 1 },
            textShadowRadius: 2,
            textAlign: 'center',
          }}>{text}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const Commits: React.FC = () => {
  const { colors, designSystem } = useTheme();
  const router = useRouter();
  const headerFontSize = useResponsiveFontSize(ResponsiveFontSizes.pageTitle);
  const styles = createStyles(colors);

  const handleCreateCommit = useCallback(() => {
    try {
      router.push('/CustomStake');
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback - try navigating to a known working page
      router.push('/MyPrograms');
    }
  }, [router]);

  const handleTemplateSelect = useCallback((template: CommitTemplate) => {
    try {
      // Navigate to CustomStake with template data as query parameters
      const params = new URLSearchParams({
        template: 'true',
        commitment: template.title,
        evidenceType: template.evidenceType,
        reportingFrequency: template.reportingFrequency,
        amountPerReport: template.amountPerReport.toString(),
        ...(template.timesPerWeek && { timesPerWeek: template.timesPerWeek.toString() }),
        ...(template.timesPerMonth && { timesPerMonth: template.timesPerMonth.toString() }),
        ...(template.weekLength && { weekLength: template.weekLength.toString() }),
      });

      router.push(`/CustomStake?${params.toString()}`);
    } catch (error) {
      console.error('Navigation error:', error);
      // Fallback to regular create commit
      handleCreateCommit();
    }
  }, [router, handleCreateCommit]);

  return (
    <View style={styles.container}>
      {/* Vanta Dots Background */}
      <VantaDotsBackground screenKey="commits" />

      <Header />
      <Text style={[styles.headerText, { color: colors.primary, fontSize: headerFontSize }]}>Commits</Text>

      {/* Description */}
      <View style={styles.descriptionContainer}>
        <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
          Create personal commitments at your own pace.
        </Text>
      </View>

      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Create Commit Button */}
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Personal Challenge</Text>
          <AnimatedCreateButton
            onPress={handleCreateCommit}
            disabled={false}
            loading={false}
            designSystem={designSystem}
            text="Create Commit"
          />
        </View>

        {/* Templates Section */}
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>Quick Start Templates</Text>
          <Text style={[styles.sectionSubtitle, { color: colors.textSecondary }]}>
            Choose from popular habits
          </Text>

          <TemplatesGrid onTemplateSelect={handleTemplateSelect} />
        </View>


      </ScrollView>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Transparent to show Vanta background
  },
  headerText: {
    // color and fontSize will be set dynamically
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginVertical: 6,
    // Ensure text doesn't get cut off on smaller screens
    maxWidth: '90%',
    alignSelf: 'center',
    flexShrink: 1,
  },
  descriptionContainer: {
    paddingHorizontal: 20,
    marginBottom: 15,
  },
  descriptionText: {
    color: colors.textSecondary,
    fontSize: 14,
    fontFamily: "MontserratRegular",
    textAlign: "center",
    lineHeight: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 12,
    paddingBottom: 20,
  },
  // Section styles
  sectionContainer: {
    marginVertical: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    marginBottom: 5,
    paddingHorizontal: 8,
    color: colors.text,
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    marginBottom: 15,
    paddingHorizontal: 8,
    lineHeight: 20,
    color: colors.textSecondary,
  },
  // Simple button styles
  simpleButton: {
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  simpleButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
  },
});

export default Commits;
