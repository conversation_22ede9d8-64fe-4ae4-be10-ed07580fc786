// Pool Challenges Tab - Earn money by joining pool challenges with other participants

import React, { useCallback, useEffect, useState, memo } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  Platform,
} from "react-native";
import { MaterialCommunityIcons, FontAwesome5 } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { firestoreService } from "../../lib/services/database";
import { getId } from "../../lib/utils/variables";
import { useRouter } from "expo-router";
import Header from "@/ui/common/Header";
import { FlashList } from "@shopify/flash-list";
import CategoryFilter from "@/ui/pages/Explore/CategoryFilter";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { ResponsiveFontSizes, useResponsiveFontSize } from "@/shared/utils/responsiveFonts";
import { VantaDotsBackground } from "@/shared/components/ui/VantaDots";

import { Program } from "@/shared/types/CommonInterface";

// Create category icons function that uses theme colors
const getCategoryIcons = (primaryColor: string): Record<string, JSX.Element> => ({
  gym: (
    <MaterialCommunityIcons name="weight-lifter" size={30} color={primaryColor} />
  ),
  cardio: <FontAwesome5 name="running" size={30} color={primaryColor} />,
  coding: <MaterialCommunityIcons name="laptop" size={30} color={primaryColor} />,
  journaling: <FontAwesome5 name="book-open" size={30} color={primaryColor} />,
  affirmations: (
    <MaterialCommunityIcons name="message-text" size={30} color={primaryColor} />
  ),
  writing: <MaterialCommunityIcons name="pencil" size={30} color={primaryColor} />,
});

// Memoized ProgramCard component
const ProgramCard = memo(
  ({ item, onPress, colors, styles }: { item: Program; onPress: (p: Program) => void; colors: any; styles: any }) => {
    const categoryIcons = getCategoryIcons(colors.primary);
    const formatDate = (dateString: string) => {
      const date = new Date(dateString);
      return `${date.getDate()} ${date.toLocaleString("default", {
        month: "short",
      })}`;
    };

    const dateRange = `${item.startDate ? formatDate(item.startDate) : 'TBD'} - ${item.endDate ? formatDate(item.endDate) : 'TBD'}`;

    return (
      <TouchableOpacity
        style={[
          styles.card,
          !item.registrationsOpen && { borderColor: colors.error, borderWidth: 2 },
        ]}
        onPress={() => onPress(item)}
        activeOpacity={0.9}
      >
        {/* Blur Background Container */}
        <View style={styles.cardBlurContainer}>
          <BlurView
            intensity={100}
            tint="dark"
            style={styles.blurEffect}
          />
          {/* Additional overlay for better visibility */}
          <View style={styles.cardOverlay} />

          {/* Card Content Container */}
          <View style={styles.cardContent}>
            {/* PROGRAM IMAGE THUMBNAIL */}
          {(item.image?.url || true) && (
            <View style={styles.imageContainer}>
              <Image
                source={{ uri: item.image?.url || 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=450&fit=crop' }}
                style={styles.programThumbnail}
                resizeMode="cover"
                loadingIndicatorSource={require('@/assets/images/icon.png')}
                fadeDuration={200}
              />
              {/* Smooth transition overlay from image to blur */}
              <LinearGradient
                colors={[
                  'rgba(0,0,0,0)',
                  'rgba(0,0,0,0.2)',
                  'rgba(0,0,0,0.5)',
                  'rgba(0,0,0,0.7)',
                  'rgba(0,0,0,0.2)' // Match the blur container background
                ]}
                locations={[0, 0.2, 0.5, 0.8, 1]}
                style={styles.imageGradientOverlay}
              />
            </View>
          )}

          {/* HEADER */}
          <View style={[styles.cardHeader, item.image?.url && { marginTop: 0, paddingTop: 8 }]}>
            {!item.image?.url && (
              <View style={[styles.iconContainer, { backgroundColor: colors.card }]}>
                {(item.category && categoryIcons[item.category]) || (
                  <MaterialCommunityIcons name="tag" size={30} color={colors.primary} />
                )}
              </View>
            )}
            <View style={styles.cardTextContainer}>
              <View style={styles.titleRow}>
                <Text
                  style={[styles.programName, { color: colors.text }]}
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {item.name}
                </Text>
                <View style={[styles.categoryBadge, { backgroundColor: colors.primary }]}>
                  <Text style={styles.categoryText}>
                    {String(item.category).charAt(0).toUpperCase() + String(item.category).slice(1)}
                  </Text>
                </View>
              </View>
              {item.headline ? (
                <Text
                  style={[styles.headlineText, { color: colors.textSecondary }]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {item.headline}
                </Text>
              ) : null}
            </View>
          </View>

          {/* DATE & CHALLENGE INFO */}
          <View style={styles.cardBody}>
            <View style={styles.cardInfoRow}>
              <Text style={[styles.programDetails, { color: colors.textSecondary }]}>
                {item.duration ? Math.round(Number(item.duration) / 7) : 0} Week Challenge
              </Text>
              <Text style={[styles.programDates, { color: colors.primary }]}>{dateRange}</Text>
            </View>
          </View>

          {/* FOOTER */}
          <View style={[styles.cardFooter, { borderTopColor: colors.separator }]}>
            <View style={styles.cardFooterItem}>
              <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Bet Amount</Text>
              <Text style={[styles.footerValue, { color: colors.text }]}>${item.betAmount}</Text>
            </View>
            <View style={styles.cardFooterItem}>
              <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Pool Amount</Text>
              <Text style={[styles.footerValue, { color: colors.text }]}>
                ${(item.participantsCount || 0) * (item.betAmount || 0)}
              </Text>
            </View>
            <View style={styles.cardFooterItem}>
              <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Current Size</Text>
              <Text style={[styles.footerValue, { color: colors.text }]}>{item.participantsCount}</Text>
            </View>
            <View style={styles.cardFooterItem}>
              <Text style={[styles.footerLabel, { color: colors.textMuted }]}>Registration</Text>
              <Text
                style={[
                  styles.footerValue,
                  { color: item.registrationsOpen ? "#4CAF50" : "#FF5252" },
                ]}
              >
                {item.registrationsOpen ? "Open" : "Closed"}
              </Text>
            </View>
          </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  }
);

const PoolChallenges: React.FC = () => {
  const { colors, designSystem, isDark } = useTheme();
  const headerFontSize = useResponsiveFontSize(ResponsiveFontSizes.pageTitle);
  const styles = createStyles(colors, designSystem, isDark);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [filteredPrograms, setFilteredPrograms] = useState<Program[]>([]);
  const [userId, setUserId] = useState<string>("");
  const [isLoadingPrograms, setIsLoadingPrograms] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>("All");

  const router = useRouter();

  const fetchPrograms = useCallback(async () => {
    setIsLoadingPrograms(true);
    try {
      const id = String(await getId());
      setUserId(id);

      // Parallel fetch for better performance
      const [programsResult, userProgramsResult] = await Promise.all([
        firestoreService.programs.getAllPrograms(),
        firestoreService.users.getUserPrograms(id)
      ]);

      if (!programsResult.success) {
        throw new Error(programsResult.error || 'Failed to fetch programs');
      }

      const programList = (programsResult.data || []) as Program[];
      setPrograms(programList);

      const signedUpProgramIds = userProgramsResult.success
        ? (userProgramsResult.data || []).map(p => p.programId)
        : [];

      const notJoined = programList.filter(
        (program) => !signedUpProgramIds.includes(program.id || '')
      );

      // Filter out programs with unwanted statuses
      const availablePrograms = notJoined.filter((program) => {
        // Exclude programs with these statuses
        const excludedStatuses = ['ongoing', 'active', 'ended'];
        if (excludedStatuses.includes(program.status || '')) {
          return false;
        }

        // Exclude programs with closed registrations
        if (program.registrationsOpen === false) {
          return false;
        }

        return true;
      });

      setFilteredPrograms(availablePrograms);
    } catch (error) {
      console.error("Error fetching programs:", error);
    } finally {
      setIsLoadingPrograms(false);
    }
  }, []);

  useEffect(() => {
    fetchPrograms();
  }, [fetchPrograms]);

  // Re-filter programs based on selected category
  const displayedPrograms = filteredPrograms.filter((p) =>
    selectedCategory === "All" ? true : p.category === selectedCategory
  );

  const handleProgramPress = useCallback(
    (program: Program) => {
      router.push(`/ProgramDetails?programId=${program.id}`);
    },
    [router]
  );

  const renderProgramCard = useCallback(
    ({ item }: { item: Program }) => (
      <ProgramCard item={item} onPress={handleProgramPress} colors={colors} styles={styles} />
    ),
    [handleProgramPress, colors, styles]
  );

  return (
    <View style={styles.container}>
      {/* Vanta Dots Background */}
      <VantaDotsBackground screenKey="pool" />

      <Header />
      <Text style={[styles.headerText, { color: colors.primary, fontSize: headerFontSize }]}>Pool Challenges</Text>

      {/* Description */}
      <View style={styles.descriptionContainer}>
        <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>
          Compete with others and earn from the shared pool!
        </Text>
      </View>

      {/* Horizontal Scroll for Category Filter Buttons */}
      <View>
        <CategoryFilter
          selectedCategory={selectedCategory}
          onSelectCategory={setSelectedCategory}
        />
      </View>

      {isLoadingPrograms ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : displayedPrograms.length === 0 ? (
        <View style={styles.noProgramsContainer}>
          <Text style={[styles.noProgramsText, { color: colors.primary }]}>Coming soon... Stay tuned!</Text>
        </View>
      ) : (
        <FlashList
          data={displayedPrograms}
          renderItem={renderProgramCard}
          keyExtractor={(item) => item.id}
          contentContainerStyle={{ paddingBottom: 20 }}
          estimatedItemSize={200}
          getItemType={() => 'program-card'}
        />
      )}


    </View>
  );
};

const createStyles = (colors: any, designSystem: any, isDark: boolean) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent', // Transparent to show Vanta background
  },
  headerText: {
    // color and fontSize will be set dynamically
    fontFamily: "MontserratBold",
    textAlign: "center",
    marginVertical: 6,
    // Ensure text doesn't get cut off on smaller screens
    maxWidth: '90%',
    alignSelf: 'center',
    flexShrink: 1,
  },
  descriptionContainer: {
    paddingHorizontal: 20,
    marginBottom: 10,
  },
  descriptionText: {
    color: colors.textSecondary,
    fontSize: 14,
    fontFamily: "MontserratRegular",
    textAlign: "center",
    lineHeight: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  noProgramsContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 20,
  },
  noProgramsText: {
    color: colors.text,
    fontSize: 18,
    fontFamily: "MontserratBold",
    textAlign: "center",
  },
  card: {
    backgroundColor: 'transparent', // Make card transparent
    borderColor: 'rgba(255, 255, 255, 0.2)', // More visible glass border
    borderRadius: 15,
    marginVertical: 10,
    marginHorizontal: 12, // Reduced from 20 to 12 to make cards wider
    padding: 0, // Remove padding to allow image to touch edges
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    borderWidth: 1.5, // Slightly thicker border
    overflow: "hidden", // Ensure image respects card border radius
  },
  cardBlurContainer: {
    flex: 1,
    position: 'relative',
    borderRadius: 15,
    overflow: 'hidden',
  },
  blurEffect: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.3)', // Fallback background
  },
  cardOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)', // Additional dark overlay for glass effect
    borderRadius: 15,
  },
  cardContent: {
    position: 'relative',
    zIndex: 1,
    flex: 1,
  },
  cardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
    paddingHorizontal: 15, // Add horizontal padding back for content
    paddingTop: 15, // Add top padding for content
  },
  iconContainer: {
    width: 50,
    height: 50,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 25,
    backgroundColor: colors.primary,
    marginRight: 10,
  },
  imageContainer: {
    position: 'relative',
    width: "100%",
  },
  programThumbnail: {
    width: "100%",
    aspectRatio: 16 / 9, // This ensures 16:9 ratio with no cropping
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    marginBottom: 0, // Remove margin to touch card edges
  },
  imageGradientOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%', // Cover bottom 70% of the image for smoother transition
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
  },
  cardTextContainer: {
    flex: 1,
  },
  titleRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: 3, // Reduced from 6 to 3 to decrease gap
  },
  categoryBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 6, // Reduced from 8 to 6
    paddingVertical: 2, // Reduced from 4 to 2
    borderRadius: 10, // Reduced from 12 to 10
    alignSelf: "flex-start",
    marginTop: 2, // Added top padding/margin
  },
  categoryText: {
    color: colors.background,
    fontSize: 12,
    fontFamily: "MontserratBold",
    textTransform: "capitalize",
  },
  programName: {
    color: colors.text,
    fontSize: 16,
    fontFamily: "MontserratBold",
    flex: 1,
    marginRight: 8,
  },
  headlineText: {
    color: colors.textSecondary,
    fontSize: 13,
    fontFamily: "MontserratRegular",
    marginTop: 2,
  },
  cardBody: {
    marginVertical: 8,
    paddingHorizontal: 15, // Add horizontal padding for content
  },
  cardInfoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 5,
  },
  programDetails: {
    color: colors.textSecondary,
    fontSize: 14,
    fontFamily: "MontserratRegular",
  },
  programDates: {
    color: colors.text,
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    borderTopWidth: 1,
    borderTopColor: colors.border,
    paddingTop: 8,
    paddingHorizontal: 15, // Add horizontal padding for content
    paddingBottom: 15, // Add bottom padding for content
    marginTop: 8,
  },
  cardFooterItem: {
    alignItems: "center",
    flex: 1,
  },
  footerLabel: {
    color: colors.textMuted,
    fontSize: 12,
    fontFamily: "MontserratRegular",
  },
  footerValue: {
    color: colors.text,
    fontSize: 14,
    fontFamily: "MontserratBold",
  },
});

export default PoolChallenges;

