

import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Animated,
  Platform,
} from "react-native";
import { getId, getFname, getLname } from "../../lib/utils/variables";
import { useRouter } from "expo-router";
import { MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons";

import { useAuth } from "@/shared/contexts/AuthContext";
import { useTheme, ThemeMode } from "@/shared/contexts/ThemeContext";
import { useFocusEffect } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { BlurView } from "expo-blur";
import Header from "@/ui/common/Header";
import { SubscriptionModal } from "@/ui/forms/SubscriptionModal";

// Platform-specific blur configuration - Balanced intensity
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 15,
      tint: 'dark' as const
    };
  } else if (Platform.OS === 'android') {
    return {
      intensity: 25,
      tint: 'dark' as const,
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  } else {
    // iOS
    return {
      intensity: 30,
      tint: 'systemMaterialDark' as const
    };
  }
};

// Floating Dot Component for Premium Button Animation
const FloatingDot: React.FC<{
  x: number;
  y: number;
  size: number;
  delay: number;
}> = ({ x, y, size, delay }) => {
  const opacity = React.useRef(new Animated.Value(0)).current;
  const translateY = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const animate = () => {
      // Reset
      opacity.setValue(0);
      translateY.setValue(0);

      // Simple fade in, float up, fade out
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.6,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.parallel([
          Animated.timing(translateY, {
            toValue: -15,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        setTimeout(animate, 1000);
      });
    };

    setTimeout(animate, delay);
  }, [delay, opacity, translateY]);

  return (
    <Animated.View
      style={{
        position: 'absolute',
        left: x,
        top: y,
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: '#FFD700',
        opacity,
        transform: [{ translateY }],
        zIndex: 1,
      }}
    />
  );
};

function Settings() {
  const { signOut } = useAuth();
  const { themeMode, setThemeMode, colors, isDark, designSystem } = useTheme();
  const [email, setEmail] = useState("");
  const [fname, setFname] = useState("");
  const [lname, setLname] = useState("");
  const [showPremiumModal, setShowPremiumModal] = useState(false);



  const router = useRouter();

  // Add caching for profile data
  const [isInitialized, setIsInitialized] = useState(false);
  const lastFetchTime = useRef<number>(0);
  const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes cache for profile data

  const shouldFetchData = () => {
    const now = Date.now();
    return !isInitialized || (now - lastFetchTime.current) > CACHE_DURATION;
  };

  const fetchData = async () => {
    if (!shouldFetchData()) return;

    lastFetchTime.current = Date.now();
      const fetchedEmail = String(await getId());
      setEmail(fetchedEmail);

      const fetchedfName = String(await getFname());
      setFname(fetchedfName);

      const fetchedLname = String(await getLname());
      setLname(fetchedLname);



      setIsInitialized(true);
    };

  // Initial fetch
  useEffect(() => {
    if (!isInitialized) {
      fetchData();
    }
  }, [isInitialized]);

  // Lazy loading on tab focus
  useFocusEffect(
    React.useCallback(() => {
      if (shouldFetchData()) {
        fetchData();
      }
    }, [])
  );

  const handleLogout = async () => {
    await signOut();
    // The AuthContext will automatically handle the redirect to SignIn page
  };

  // Instead of a modal, navigate to a separate ConfirmDelete screen
  const confirmDelete = () => {
    router.push({
      pathname: "/ConfirmDelete",
      params: { userEmail: email },
    });
  };

  // Example navigation placeholders for other items
  const handleNav = (destination: string) => {
    // Adjust as needed for your actual screens or logic
    router.push(destination as any);
  };

  const handleFAQPress = () => {
    // Navigate to FAQ screen which will show FAQ content in webview
    router.push("/FAQ");
  };

  // Theme selection handler
  const handleThemeSelection = () => {
    const themes: ThemeMode[] = ["light", "dark", "system"];
    const currentIndex = themes.indexOf(themeMode);
    const nextIndex = (currentIndex + 1) % themes.length;
    setThemeMode(themes[nextIndex]);
  };

  // Get theme mode display text
  const getThemeModeText = (mode: ThemeMode) => {
    switch (mode) {
      case 'light':
        return 'Light';
      case 'dark':
        return 'Dark';
      case 'system':
        return 'System';
      default:
        return 'System';
    }
  };



  const styles = createStyles(colors, isDark, designSystem);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Header />
      <ScrollView contentContainerStyle={styles.scrollContent} showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <View style={styles.profileCard}>
          <BlurView intensity={30} tint={isDark ? "dark" : "light"} style={styles.profileBlur}>
            <LinearGradient
              colors={isDark
                ? ['rgba(255, 255, 255, 0.05)', 'rgba(255, 255, 255, 0.02)', 'rgba(255, 255, 255, 0.05)']
                : ['rgba(255, 255, 255, 0.8)', 'rgba(255, 255, 255, 0.6)', 'rgba(255, 255, 255, 0.8)']
              }
              style={styles.profileGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <View style={styles.profileHeaderContent}>
            {/* Clean Avatar Section */}
            <View style={styles.avatarSection}>
              <View style={styles.avatarWrapper}>
                <View style={[styles.avatarContainer, {
                  backgroundColor: colors.surface,
                  borderColor: colors.border
                }]}>
                  <Text style={[styles.avatarText, { color: colors.text }]}>
                    {fname?.charAt(0)?.toUpperCase() || email?.charAt(0)?.toUpperCase() || "U"}
                  </Text>
                </View>
                {/* Status Indicator */}
                <View style={[styles.statusIndicator, { backgroundColor: colors.success, borderColor: colors.surface }]} />
              </View>
            </View>

            {/* Profile Information Section */}
            <View style={styles.profileInfo}>
              <Text style={[styles.nameText, { color: colors.text }]}>
                {`${fname} ${lname}` || "User"}
              </Text>

              <View style={styles.emailContainer}>
                <MaterialCommunityIcons name="email-outline" size={14} color={colors.textMuted} />
                <Text style={[styles.emailText, { color: colors.textMuted }]}>
                  {email || "<EMAIL>"}
                </Text>
              </View>
            </View>
              </View>
            </LinearGradient>
          </BlurView>
        </View>

        {/* Upgrade Button - Create Commit Style with Animations */}
        <TouchableOpacity
          style={styles.premiumButton}
          onPress={() => {
            setShowPremiumModal(true);
          }}
          activeOpacity={0.8}
        >
          <View style={styles.premiumContainer}>
            {/* Floating Dots Animation - Full Width Coverage */}
            {[
              // Left side
              { x: 20, y: 15, size: 16, delay: 0 },
              { x: 60, y: 25, size: 20, delay: 200 },
              { x: 40, y: 40, size: 14, delay: 400 },

              // Left-center
              { x: 100, y: 20, size: 18, delay: 600 },
              { x: 130, y: 35, size: 15, delay: 800 },
              { x: 80, y: 45, size: 19, delay: 1000 },

              // Center
              { x: 170, y: 25, size: 17, delay: 1200 },
              { x: 200, y: 35, size: 22, delay: 1400 },
              { x: 180, y: 10, size: 13, delay: 1600 },

              // Right-center
              { x: 240, y: 30, size: 18, delay: 1800 },
              { x: 270, y: 15, size: 16, delay: 2000 },
              { x: 250, y: 45, size: 15, delay: 2200 },

              // Right side - Extended to cover full width
              { x: 310, y: 25, size: 21, delay: 2400 },
              { x: 340, y: 40, size: 17, delay: 2600 },
              { x: 370, y: 20, size: 19, delay: 2800 },
              { x: 400, y: 35, size: 16, delay: 3000 },
              { x: 430, y: 15, size: 18, delay: 3200 },
              { x: 460, y: 30, size: 14, delay: 3400 },
            ].map((dot, index) => (
              <FloatingDot
                key={index}
                x={dot.x}
                y={dot.y}
                size={dot.size}
                delay={dot.delay}
              />
            ))}

            <BlurView
              intensity={getBlurConfig().intensity}
              tint={getBlurConfig().tint}
              {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
              style={styles.premiumBlur}
            />
            <View style={styles.premiumContent}>
              <MaterialCommunityIcons name="crown" size={18} color="#FFD700" />
              <Text style={styles.premiumText}>Upgrade to Premium</Text>
              <MaterialCommunityIcons name="arrow-right" size={16} color="#FFD700" />
            </View>
          </View>
        </TouchableOpacity>

        {/* Account */}
        <View style={styles.settingsCard}>
          <BlurView intensity={30} tint="dark" style={styles.settingsBlur}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.settingsGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="account-star" size={20} color={colors.info} />
            <Text style={[styles.sectionTitle, { color: colors.info }]}>Account</Text>
          </View>

          <View style={[styles.actionItem, styles.disabledActionItem, { borderBottomColor: colors.border }]}>
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="account" size={20} color={colors.info} />
              </View>
              <View style={styles.actionTextContainer}>
                <View style={styles.titleWithTag}>
                  <Text style={[styles.actionTitle, { color: colors.text }]}>View Profile</Text>
                  <View style={styles.comingSoonTag}>
                    <Text style={styles.comingSoonTagText}>Coming Soon</Text>
                  </View>
                </View>
                <Text style={[styles.actionSubtitle, styles.comingSoonSubtitle, { color: colors.textMuted }]}>Let others see your progress, winnings and badges</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.textMuted} style={styles.blurredChevron} />
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={() => handleNav("/PointsPayouts")}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="wallet" size={20} color={colors.warning} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Points & Payouts</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Track your earnings and rewards</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={() => router.push("/MyPrograms")}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="view-list" size={20} color={colors.info} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>My Enrolment</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Your commits and challenges</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.info} />
          </TouchableOpacity>
            </LinearGradient>
          </BlurView>
        </View>

        {/* Integrations Section */}
        <View style={styles.settingsCard}>
          <BlurView intensity={30} tint="dark" style={styles.settingsBlur}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.settingsGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="link-variant" size={20} color={colors.success} />
            <Text style={[styles.sectionTitle, { color: colors.success }]}>Integrations</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 1, borderBottomColor: colors.border }]}
            onPress={() => handleNav("/integrations")}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="link-variant" size={20} color={colors.success} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Manage Integrations</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Connect GitHub, Strava and more</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.success} />
          </TouchableOpacity>
            </LinearGradient>
          </BlurView>
        </View>

        {/* Preferences */}
        <View style={styles.settingsCard}>
          <BlurView intensity={30} tint="dark" style={styles.settingsBlur}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.settingsGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="palette" size={20} color={colors.textSecondary} />
            <Text style={[styles.sectionTitle, { color: colors.textSecondary }]}>Preferences</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={handleThemeSelection}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons
                  name={isDark ? "weather-night" : "weather-sunny"}
                  size={20}
                  color={colors.textSecondary}
                />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Theme</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>
                  Currently: {getThemeModeText(themeMode)}
                </Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
            </LinearGradient>
          </BlurView>
        </View>

        {/* Support */}
        <View style={styles.settingsCard}>
          <BlurView intensity={30} tint="dark" style={styles.settingsBlur}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.settingsGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="help-circle" size={20} color={colors.info} />
            <Text style={[styles.sectionTitle, { color: colors.info }]}>Support</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={() => handleNav("/Disputes")}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="gavel" size={20} color={colors.warning} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Disputes</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Report issues or disputes</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.primary} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={() => handleNav("/Support")}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="help-circle" size={20} color={colors.info} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>Support</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Get help and assistance</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.info} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={() => handleFAQPress()}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="frequently-asked-questions" size={20} color={colors.info} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.text }]}>FAQs</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Frequently asked questions</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.info} />
          </TouchableOpacity>
            </LinearGradient>
          </BlurView>
        </View>

        {/* Account Actions */}
        <View style={styles.settingsCard}>
          <BlurView intensity={30} tint="dark" style={styles.settingsBlur}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.settingsGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
          <View style={styles.sectionHeader}>
            <MaterialCommunityIcons name="account-cog" size={20} color={colors.primary} />
            <Text style={[styles.sectionTitle, { color: colors.primary }]}>Account Actions</Text>
          </View>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomColor: colors.border }]}
            onPress={handleLogout}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="logout" size={20} color={colors.info} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.info }]}>Log Out</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Sign out of your account</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.info} />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionItem, { borderBottomWidth: 0 }]}
            onPress={confirmDelete}
          >
            <View style={styles.actionLeft}>
              <View style={styles.actionIconContainer}>
                <MaterialCommunityIcons name="delete" size={20} color={colors.error} />
              </View>
              <View style={styles.actionTextContainer}>
                <Text style={[styles.actionTitle, { color: colors.error }]}>Delete Account</Text>
                <Text style={[styles.actionSubtitle, { color: colors.textMuted }]}>Permanently remove your account</Text>
              </View>
            </View>
            <MaterialIcons name="chevron-right" size={20} color={colors.error} />
          </TouchableOpacity>
            </LinearGradient>
          </BlurView>
        </View>
      </ScrollView>

      {/* Premium Features Modal */}
      <SubscriptionModal
        visible={showPremiumModal}
        onClose={() => setShowPremiumModal(false)}
        onSubscribe={async () => {
          // TODO: Implement actual subscription purchase logic
          setShowPremiumModal(false);
          // Show success message or navigate to success screen
        }}
        featureName="Premium Features"
      />
    </View>
  );
}



const createStyles = (colors: any, isDark: boolean, designSystem: any) => StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  scrollContent: {
    paddingBottom: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.md,
  },
  profileCard: {
    marginTop: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.md,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    marginBottom: designSystem.spacing.md,
    borderWidth: 1,
    borderColor: colors.glassBorder,
    overflow: 'hidden',
  },
  profileBlur: {
    borderRadius: designSystem.borderRadius.md,
    overflow: 'hidden',
  },
  profileGradient: {
    padding: designSystem.spacing.lg,
    borderRadius: designSystem.borderRadius.md,
  },

  profileHeaderContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  // Enhanced Avatar Styles
  avatarSection: {
    alignItems: 'center',
    marginRight: designSystem.spacing.xl,
    position: 'relative',
  },
  avatarWrapper: {
    position: 'relative',
  },
  avatarContainer: {
    width: 72,
    height: 72,
    borderRadius: 36,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  avatarText: {
    fontSize: 26,
    fontFamily: "MontserratBold",
    textAlign: "center",
    letterSpacing: 0.3,
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 4,
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 3,
    ...designSystem.shadows.md,
    shadowColor: colors.success,
    shadowOpacity: 0.3,
  },
  // Enhanced Profile Info Styles
  profileInfo: {
    flex: 1,
    justifyContent: 'space-between',
  },

  nameText: {
    fontSize: 22,
    fontFamily: "MontserratBold",
    marginBottom: designSystem.spacing.sm,
    letterSpacing: 0.3,
  },
  emailContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designSystem.spacing.md,
  },
  emailText: {
    fontSize: 14,
    fontFamily: "MontserratRegular",
    marginLeft: designSystem.spacing.sm,
    opacity: 0.8,
  },

  // Cool Premium Button Styles
  premiumButton: {
    marginTop: designSystem.spacing.sm,
    marginBottom: designSystem.spacing.lg,
    alignSelf: 'stretch',
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: '#FFD700', // Golden border like create commit
    ...designSystem.shadows.xl,
    shadowColor: '#FFD700',
  },
  premiumContainer: {
    paddingVertical: designSystem.spacing.xl,
    paddingHorizontal: designSystem.spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    backgroundColor: '#000000', // Black background like create commit
    minHeight: 60,
    width: '100%',
    overflow: 'hidden',
  },
  premiumBlur: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
  },
  premiumContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 30, // Above blur layer
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  premiumText: {
    color: "#FFD700", // Gold text like create commit
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    letterSpacing: designSystem.typography.letterSpacing.wide,
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    textAlign: 'center',
    marginHorizontal: designSystem.spacing.sm,
  },

  // Section Header Styles
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designSystem.spacing.md,
    paddingBottom: designSystem.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: colors.primary + '20',
  },
  sectionTitle: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm + 2,
    flex: 1,
  },
  settingsCard: {
    borderRadius: designSystem.borderRadius.md,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
    marginBottom: designSystem.spacing.md,
    borderWidth: 1,
    borderColor: colors.glassBorder,
    overflow: 'hidden',
  },
  settingsBlur: {
    borderRadius: designSystem.borderRadius.md,
    overflow: 'hidden',
  },
  settingsGradient: {
    padding: designSystem.spacing.lg,
    borderRadius: designSystem.borderRadius.md,
  },
  actionItem: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingVertical: designSystem.spacing.md,
    borderBottomWidth: 1,
    // borderBottomColor will be set dynamically based on theme
  },
  actionLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  actionIconContainer: {
    width: 40,
    height: 40,
    borderRadius: designSystem.borderRadius.xl,
    justifyContent: "center",
    alignItems: "center",
    marginRight: designSystem.spacing.lg,
  },
  actionTextContainer: {
    flex: 1,
  },
  actionTitle: {
    fontSize: designSystem.typography.fontSize.md + 1,
    fontFamily: "MontserratBold",
    marginBottom: designSystem.spacing.xs / 4,
    // color will be set dynamically based on theme
  },
  actionSubtitle: {
    fontSize: designSystem.typography.fontSize.xs + 1,
    fontFamily: "MontserratRegular",
    // color will be set dynamically based on theme
  },
  // Coming Soon / Disabled Styles
  disabledActionItem: {
    opacity: designSystem.opacity.disabled,
  },
  blurredIcon: {
    opacity: designSystem.opacity.subtle,
  },
  blurredChevron: {
    opacity: designSystem.opacity.overlay,
  },
  comingSoonTitle: {
    fontStyle: 'italic',
    opacity: 0.8,
  },
  comingSoonSubtitle: {
    fontStyle: 'italic',
    opacity: 0.7,
  },
  // Title with Coming Soon Tag
  titleWithTag: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 1,
  },
  comingSoonTag: {
    backgroundColor: colors.primary,
    paddingHorizontal: designSystem.spacing.sm / 2,
    paddingVertical: designSystem.spacing.xs / 2,
    borderRadius: designSystem.borderRadius.sm,
    marginLeft: designSystem.spacing.sm,
    ...designSystem.shadows.md,
    shadowColor: colors.primary,
  },
  comingSoonTagText: {
    fontSize: designSystem.typography.fontSize.xs - 2,
    fontFamily: "MontserratBold",
    color: colors.background,
    textTransform: 'uppercase',
    letterSpacing: designSystem.typography.letterSpacing.wide,
  },
});

export default Settings;
