import { Redirect } from "expo-router";
import React, { useState, memo, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Modal,
  StyleSheet,
  SafeAreaView,
  Animated,
} from "react-native";
import { getToken, updateId, updateToken } from "../../lib/utils/variables";
import { auth } from "../../lib/config/firebase";
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendPasswordResetEmail,
  sendEmailVerification,
} from "firebase/auth";
import fetchUserData from "@/ui/pages/SignUp/fetchUserData";
import { firestoreService } from "../../lib/services/database";
import { useAuth } from "@/shared/contexts/AuthContext";
import { SuccessModal, ErrorModal, ConfirmationModal, InputModal } from "@/shared/components/modals";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { LinearGradient } from 'expo-linear-gradient';
import { Background, Card, Button, Input } from '@/shared/components/ui';
import { Image } from 'react-native';



// Animated Welcome Text Component
const AnimatedWelcomeText = memo(() => {
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  const words = ['Welcome', 'Bienvenido', 'Bienvenue', 'Willkommen', 'Benvenuto', 'Bem-vindo', 'Добро пожаловать', '欢迎', 'いらっしゃいませ', '환영합니다', 'أهلاً وسهلاً', 'स्वागत है', 'Hoş geldiniz'];

  useEffect(() => {
    const animateText = () => {
      // Fade out
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        // Change word
        setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);

        // Fade in
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }).start();
      });
    };

    const interval = setInterval(animateText, 3000); // Change word every 3 seconds
    return () => clearInterval(interval);
  }, [fadeAnim, words.length]);

  return (
    <View style={{ alignItems: 'center', justifyContent: 'center' }}>
      <Animated.Text
        style={[
          styles.formTitle,
          {
            color: '#FFFFFF', // White color
            opacity: fadeAnim,
            fontFamily: 'MontserratBold',
          }
        ]}
      >
        {words[currentWordIndex]}
      </Animated.Text>
    </View>
  );
});

const Login = () => {
  const { isAuthenticated } = useAuth();
  const { colors } = useTheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [fname, setFname] = useState("");
  const [lname, setLname] = useState("");
  const [dateOfBirth, setDateOfBirth] = useState("");
  const [gender, setGender] = useState("");

  const [SignUpInPwd, setSignUpInPwd] = useState("login");
  const [disLoginBtn, setLoginBtn] = useState(false);
  const [disSignupBtn, setSignupBtn] = useState(false);

  const [modalVisible, setModalVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  // Modal states for replacing Alert dialogs
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '', onPress: () => {} });
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });
  const [confirmationModalVisible, setConfirmationModalVisible] = useState(false);
  const [confirmationModalData, setConfirmationModalData] = useState({ title: '', message: '', onConfirm: () => {} });
  const [inputModalVisible, setInputModalVisible] = useState(false);
  const [inputModalData, setInputModalData] = useState({ title: '', message: '', onConfirm: (value: string) => {} });

  // Date picker states
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [selectedYear, setSelectedYear] = useState(2012); // Set initial year to 2012
  const [selectedMonth, setSelectedMonth] = useState(0);
  const [selectedDay, setSelectedDay] = useState(1);

  // Constants for date picker
  const maxYear = 2012; // Maximum year (users must be at least ~12 years old)
  const years = Array.from(
    { length: 100 }, 
    (_, i) => maxYear - i
  );
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Helper functions
  const formatDate = (year: number, month: number, day: number) => {
    return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  };

  const getDaysInMonth = (year: number, month: number) => {
    return new Date(year, month + 1, 0).getDate();
  };

  const handleDateConfirm = () => {
    const formattedDate = formatDate(selectedYear, selectedMonth, selectedDay);
    setDateOfBirth(formattedDate);
    setShowDatePicker(false);
  };



  const handleSignup = async () => {
    if (!validateInputs()) return;

    try {
      setSignupBtn(true);

      const result = await createUserWithEmailAndPassword(auth, email, password);
      const user = result.user;

      await sendEmailVerification(user);

      if (user.email) {
        const result = await firestoreService.users.createUser({
          fname,
          lname,
          email: user.email,
          dateOfBirth,
          gender,
          streak: 0,
        });

        if (result.success) {
          // Create welcome notification
          await firestoreService.notifications.createWelcomeNotification(user.email, fname);
        } else {
          throw new Error(result.error || "Failed to create user document");
        }
      } else {
        throw new Error("User email is null");
      }

      setSuccessModalData({
        title: "User Sign Up Successful!",
        message: "Please verify your email before logging in.",
        onPress: () => {
          setSuccessModalVisible(false);
          showContent("login");
        }
      });
      setSuccessModalVisible(true);

      setSignupBtn(false);
    } catch (err) {
      console.error("Error signing up user:", err);
      setSignupBtn(false);
      setErrorModalData({
        title: "Sign Up Failed",
        message: "An error occurred during sign up. Please try again."
      });
      setErrorModalVisible(true);
    }
  };

  const handleLogin = async () => {
    try {
      setLoginBtn(true);
  
      const result = await signInWithEmailAndPassword(auth, email, password);
      const user = result.user;
  
      if (!user.emailVerified) {
        setConfirmationModalData({
          title: "Email not verified",
          message: "Please verify your email before logging in.",
          onConfirm: () => {
            setConfirmationModalVisible(false);
            handleResendVerificationEmail();
          }
        });
        setConfirmationModalVisible(true);
        setLoginBtn(false);
        return;
      }
  
      if (user.email) {
        const result = await fetchUserData(user.email);

        if (result.success) {
          // The AuthContext will handle the authentication state automatically
          updateToken(true);
          updateId(user.email);
        } else {
          setErrorModalData({
            title: "User Data Error",
            message: result.error || "User Sign-In failed. User data not fetched."
          });
          setErrorModalVisible(true);
          setLoginBtn(false);
          return;
        }
      } else {
        throw new Error("User email is null.");
      }
  
      setLoginBtn(false);
    }catch (error: any) {
      setLoginBtn(false);
      
      if (error.code?.includes("auth/invalid-credential")) {
        setErrorMessage("Invalid email or password. Please try again.");
        setModalVisible(true);
      } else if (error.code?.includes("auth/user-not-found")) {
        setErrorMessage("User not found. Please sign up first.");
        setModalVisible(true);
      } else if (error.code?.includes("auth/missing-password")) {
        setErrorMessage("Enter your password first");
        setModalVisible(true);
      } else if (error.code?.includes("auth/too-many-requests")) {
        setErrorMessage(
          "Too many failed attempts. Please try again later or reset your password."
        );
        setModalVisible(true);
      } else {
        setErrorMessage("An error occurred. Please try again.");
        setModalVisible(true);
      }
      
      // console.error("Error signing in:", error.message);
    }
  };

  const handleForgotPwd = async () => {
    try {
      if (!email) {
        setErrorModalData({
          title: "Email Required",
          message: "Please enter your email for password reset."
        });
        setErrorModalVisible(true);
        return;
      }

      await sendPasswordResetEmail(auth, email);
      setEmail("");
      setSuccessModalData({
        title: "Success",
        message: "Password reset email sent successfully!",
        onPress: () => setSuccessModalVisible(false)
      });
      setSuccessModalVisible(true);
    } catch (error: any) {
      console.error("Error sending password reset email:", error.message);
    }
  };

  const handleResendVerificationEmail = async () => {
    try {
      const user = auth.currentUser;
      if (user) {
        await sendEmailVerification(user);
        setSuccessModalData({
          title: "Success",
          message: "Verification email sent successfully!",
          onPress: () => setSuccessModalVisible(false)
        });
        setSuccessModalVisible(true);
      }
    } catch (error: any) {
      console.error("Error resending verification email:", error.message);
    }
  };

  const showContent = (val: any) => {
    setSignUpInPwd(val);
  };

  const validateInputs = () => {
    // Regular expressions
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/;
    const nameRegex = /^[A-Za-z]+$/;

    // Check email
    if (!email || !emailRegex.test(email)) {
      setErrorMessage("Please enter a valid email address.");
      setModalVisible(true);
      return false;
    }

    // Check first name
    if (!fname || !nameRegex.test(fname)) {
      setErrorMessage("First name should contain only letters.");
      setModalVisible(true);
      return false;
    }

    // Check last name
    if (!lname || !nameRegex.test(lname)) {
      setErrorMessage("Last name should contain only letters.");
      setModalVisible(true);
      return false;
    }

    // Check password
    if (!password || !passwordRegex.test(password)) {
      setErrorMessage(
        "Password must be at least 6 characters long, contain one letter, one number, and one special character."
      );
      setModalVisible(true);
      return false;
    }

    // Check date of birth
    if (!dateOfBirth) {
      setErrorMessage("Please select your date of birth.");
      setModalVisible(true);
      return false;
    }

    // Check gender
    if (!gender) {
      setErrorMessage("Please select your gender.");
      setModalVisible(true);
      return false;
    }

    return true;
  };
  

  return (
    <Background variant="shader" style={styles.container}>
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 0}
        >
          {isAuthenticated && <Redirect href="/(tabs)" />}

          {/* Modern Header */}
          <View style={styles.headerSection}>
            {/* Logo Container - No Card wrapper */}
            <View style={styles.logoContent}>
              <View style={styles.logoImageContainer}>
                <Image
                  source={require('../../assets/images/new_logo.png')}
                  style={styles.logoImage}
                  resizeMode="contain"
                />
              </View>
              <Text style={[styles.appTitle, { color: colors.text }]}>
                ACCUSTOM
              </Text>
              <LinearGradient
                colors={['#FFD700', '#FFEB3B']}
                style={styles.titleUnderline}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
              />
            </View>

            {/* Welcome Message */}
            <View style={styles.welcomeSection}>
              <Text style={[styles.welcomeText, { color: colors.text }]}>
                Where Self-Improvement Pays You
              </Text>

              {/* Subtitle */}
              <Text style={[styles.subtitle, { color: colors.textMuted }]}>
                Build habits that build wealth
              </Text>
            </View>
          </View>

        {/* Form Section */}
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <Card style={styles.formContainer} borderRadius={20} intensity={30} variant="secondary" padding={16}>
            {SignUpInPwd === "login" && (
              <View style={styles.formContent}>
                <AnimatedWelcomeText />

                <View style={styles.inputContainer}>
                  <Input
                    placeholder="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                  />
                  <Input
                    placeholder="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry
                  />
                </View>

                <Button
                  title="Enter the Royale"
                  onPress={handleLogin}
                  disabled={disLoginBtn}
                  loading={disLoginBtn}
                  size="large"
                  style={styles.primaryButton}
                />
              </View>
            )}

          {SignUpInPwd === "signup" && (
            <View style={styles.formContent}>
              <Text style={[styles.formTitle, { color: colors.text }]}>
                Begin Your Transformation
              </Text>

              <View style={styles.inputContainer}>
                <View style={styles.nameRow}>
                  <Input
                    placeholder="First Name"
                    value={fname}
                    onChangeText={setFname}
                    containerStyle={styles.nameInput}
                  />
                  <Input
                    placeholder="Last Name"
                    value={lname}
                    onChangeText={setLname}
                    containerStyle={styles.nameInput}
                  />
                </View>

                <Input
                  placeholder="Email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
                <Input
                  placeholder="Password"
                  value={password}
                  onChangeText={setPassword}
                  secureTextEntry
                />
                {/* Date of Birth Picker */}
                <TouchableOpacity
                  style={[styles.datePickerButton, {
                    backgroundColor: colors.surface,
                    borderColor: colors.border,
                  }]}
                  onPress={() => setShowDatePicker(true)}
                >
                  <Text style={[styles.datePickerButtonText, {
                    color: dateOfBirth ? colors.text : colors.textMuted
                  }]}>
                    {dateOfBirth || "Select Date of Birth"}
                  </Text>
                </TouchableOpacity>

                {/* Date Picker Modal */}
                <Modal
                  visible={showDatePicker}
                  transparent={true}
                  animationType="fade"
                  onRequestClose={() => setShowDatePicker(false)}
                >
                  <View style={styles.datePickerModal}>
                    <View style={styles.datePickerContainer}>
                      <View style={styles.pickerRow}>
                        {/* Year Picker */}
                        <View style={styles.pickerColumn}>
                          <Text style={styles.pickerLabel}>Year</Text>
                          <ScrollView style={styles.pickerScroll}>
                            {years.map(year => (
                              <TouchableOpacity
                                key={year}
                                style={[
                                  styles.pickerItem,
                                  selectedYear === year && styles.pickerItemSelected
                                ]}
                                onPress={() => setSelectedYear(year)}
                              >
                                <Text 
                                  style={[
                                    styles.pickerItemText,
                                    selectedYear === year && styles.pickerItemTextSelected
                                  ]}
                                >
                                  {year}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>

                        {/* Month Picker */}
                        <View style={styles.pickerColumn}>
                          <Text style={styles.pickerLabel}>Month</Text>
                          <ScrollView style={styles.pickerScroll}>
                            {months.map((month, index) => (
                              <TouchableOpacity
                                key={month}
                                style={[
                                  styles.pickerItem,
                                  selectedMonth === index && styles.pickerItemSelected
                                ]}
                                onPress={() => setSelectedMonth(index)}
                              >
                                <Text 
                                  style={[
                                    styles.pickerItemText,
                                    selectedMonth === index && styles.pickerItemTextSelected
                                  ]}
                                >
                                  {month}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>

                        {/* Day Picker */}
                        <View style={styles.pickerColumn}>
                          <Text style={styles.pickerLabel}>Day</Text>
                          <ScrollView style={styles.pickerScroll}>
                            {Array.from(
                              {length: getDaysInMonth(selectedYear, selectedMonth)},
                              (_, i) => i + 1
                            ).map(day => (
                              <TouchableOpacity
                                key={day}
                                style={[
                                  styles.pickerItem,
                                  selectedDay === day && styles.pickerItemSelected
                                ]}
                                onPress={() => setSelectedDay(day)}
                              >
                                <Text 
                                  style={[
                                    styles.pickerItemText,
                                    selectedDay === day && styles.pickerItemTextSelected
                                  ]}
                                >
                                  {day}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>
                      </View>

                      <View style={styles.modalButtons}>
                        <TouchableOpacity
                          style={[styles.modalButton, styles.cancelButton]}
                          onPress={() => setShowDatePicker(false)}
                        >
                          <Text style={[styles.modalButtonText, styles.cancelButtonText]}>
                            Cancel
                          </Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                          style={[styles.modalButton, styles.confirmButton]}
                          onPress={handleDateConfirm}
                        >
                          <Text style={[styles.modalButtonText, styles.confirmButtonText]}>
                            Confirm
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </Modal>
                <View style={[styles.genderContainer, { marginTop: 16 }]}>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      {
                        backgroundColor: gender === 'male' ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setGender('male')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      { color: gender === 'male' ? '#000' : colors.text }
                    ]}>Male</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      {
                        backgroundColor: gender === 'female' ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setGender('female')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      { color: gender === 'female' ? '#000' : colors.text }
                    ]}>Female</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[
                      styles.genderButton,
                      {
                        backgroundColor: gender === 'other' ? colors.primary : colors.surface,
                        borderColor: colors.border,
                      }
                    ]}
                    onPress={() => setGender('other')}
                  >
                    <Text style={[
                      styles.genderButtonText,
                      { color: gender === 'other' ? '#000' : colors.text }
                    ]}>Other</Text>
                  </TouchableOpacity>
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.primaryButton,
                  disSignupBtn && styles.primaryButtonDisabled
                ]}
                onPress={handleSignup}
                disabled={disSignupBtn}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#FFD700', '#FFEB3B', '#FFC107']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.buttonContent}>
                    {disSignupBtn ? (
                      <ActivityIndicator size="small" color="#000000" />
                    ) : (
                      <Text style={styles.primaryButtonText}>
                        Create Account
                      </Text>
                    )}
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}

          {SignUpInPwd === "forgotPwd" && (
            <View style={styles.formContent}>
              <Text style={[styles.formTitle, { color: colors.text }]}>
                Recover Access
              </Text>

              <View style={styles.inputContainer}>
                <Input
                  placeholder="Email"
                  value={email}
                  onChangeText={setEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>

              <TouchableOpacity
                style={[styles.primaryButton]}
                onPress={handleForgotPwd}
                activeOpacity={0.8}
              >
                <LinearGradient
                  colors={['#FFD700', '#FFEB3B', '#FFC107']}
                  style={styles.buttonGradient}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                >
                  <View style={styles.buttonContent}>
                    <Text style={styles.primaryButtonText}>
                      Send Reset Link
                    </Text>
                  </View>
                </LinearGradient>
              </TouchableOpacity>
            </View>
          )}

          {/* Navigation Links */}
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              onPress={() =>
                showContent(SignUpInPwd === "login" ? "signup" : "login")
              }
              style={[styles.linkButton, styles.leftLinkButton]}
            >
              <Text style={[styles.linkText, styles.leftLinkText, { color: colors.primary }]}>
                {SignUpInPwd === "login"
                  ? "New user? Sign Up"
                  : "Existing user? Sign In"}
              </Text>
            </TouchableOpacity>
            {!(SignUpInPwd === "forgotPwd" || SignUpInPwd === "signup") && (
              <TouchableOpacity
                onPress={() => showContent("forgotPwd")}
                style={[styles.linkButton, styles.rightLinkButton]}
              >
                <Text style={[styles.linkText, styles.rightLinkText, { color: colors.textMuted }]}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            )}
          </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Error Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.card }]}>
            <Text style={[styles.modalTitle, { color: colors.error }]}>
              Authentication Error
            </Text>
            <Text style={[styles.modalMessage, { color: colors.text }]}>
              {errorMessage}
            </Text>
            <TouchableOpacity
              style={[styles.modalButton, { backgroundColor: colors.primary }]}
              onPress={() => setModalVisible(false)}
            >
              <Text style={styles.modalButtonText}>OK</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
        onButtonPress={successModalData.onPress}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        visible={confirmationModalVisible}
        onClose={() => setConfirmationModalVisible(false)}
        title={confirmationModalData.title}
        message={confirmationModalData.message}
        onConfirm={confirmationModalData.onConfirm}
        confirmText="Resend Verification Email"
        cancelText="Cancel"
      />

      </SafeAreaView>
    </Background>
  );
};

const styles = StyleSheet.create({
  // Main container styles
  container: {
    flex: 1,
  },
  safeArea: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },


  // Modern glassmorphic header styles
  headerSection: {
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 120 : 100,
    paddingBottom: 60,
    paddingHorizontal: 24,
  },
  logoContainer: {
    marginBottom: 24,
    alignItems: 'center',
  },
  logoContent: {
    alignItems: 'center',
    gap: 8,
  },
  welcomeSection: {
    alignItems: 'center',
    marginTop: 20,
    gap: 8,
  },
  logoImageContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoImage: {
    width: 120,
    height: 80,
  },
  logoGlow: {
    position: 'absolute',
    width: 140,
    height: 100,
    borderRadius: 15,
    shadowOffset: {
      width: 0,
      height: 0,
    },
    shadowOpacity: 0.4,
    shadowRadius: 20,
    elevation: 10,
  },
  appTitle: {
    fontSize: 24,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    letterSpacing: 2,
    marginTop: 8,
  },
  titleUnderline: {
    width: 40,
    height: 2,
    borderRadius: 1,
    marginTop: 4,
  },
  welcomeText: {
    fontSize: 18,
    fontFamily: 'MontserratMedium',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    letterSpacing: 0.3,
    lineHeight: 20,
    opacity: 0.8,
  },

  // Spacer to balance content
  spacer: {
    flex: 0.6,
    minHeight: 30,
  },

  // Form styles
  scrollView: {
    flex: 1,
  },
  formContainer: {
    marginHorizontal: 24,
    marginBottom: 30,
  },
  formContent: {
    gap: 12,
  },
  formTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 6,
    letterSpacing: 0.5,
  },

  // Input styles
  inputContainer: {
    gap: 10,
  },
  nameRow: {
    flexDirection: 'row',
    gap: 12,
  },
  nameInput: {
    flex: 1,
  },

  // Navigation and link styles
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 12,
    paddingHorizontal: 0,
  },
  linkButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  linkText: {
    fontSize: 14,
    fontFamily: 'MontserratMedium',
  },
  leftLinkButton: {
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
  },
  rightLinkButton: {
    alignItems: 'flex-end',
    alignSelf: 'flex-end',
  },
  leftLinkText: {
    textAlign: 'left',
  },
  rightLinkText: {
    textAlign: 'right',
  },

  // Premium casino-themed button styles
  primaryButton: {
    marginTop: 20,
    shadowColor: '#FFD700',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  primaryButtonDisabled: {
    opacity: 0.6,
    shadowOpacity: 0.1,
    elevation: 2,
  },
  primaryButtonText: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    color: '#000000',
    letterSpacing: 1.5,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  buttonGradient: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 14,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 24,
  },

  // Gender selection styles
  genderContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  genderButton: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 10,
    alignItems: 'center',
  },
  genderButtonText: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
  },

  // Date picker styles
  datePickerButton: {
    borderWidth: 1,
    borderRadius: 12,
    paddingVertical: 14,
    paddingHorizontal: 14,
  },
  datePickerButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
  },
  datePickerModal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  datePickerContainer: {
    backgroundColor: '#2A2A2A',
    padding: 20,
    borderRadius: 16,
    width: '90%',
    maxHeight: '80%',
  },
  pickerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  pickerColumn: {
    flex: 1,
    marginHorizontal: 5,
  },
  pickerLabel: {
    color: '#fcfaec',
    marginBottom: 8,
    textAlign: 'center',
    fontFamily: 'MontserratBold',
    fontSize: 14,
  },
  pickerScroll: {
    maxHeight: 150,
    backgroundColor: '#1e1e1e',
    borderRadius: 8,
  },
  pickerItem: {
    padding: 12,
    alignItems: 'center',
  },
  pickerItemText: {
    color: '#fcfaec',
    fontFamily: 'MontserratRegular',
    fontSize: 16,
  },
  pickerItemSelected: {
    backgroundColor: '#FFEB3B',
  },
  pickerItemTextSelected: {
    color: '#000000',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 20,
  },
  confirmButton: {
    backgroundColor: '#FFEB3B',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  cancelButton: {
    backgroundColor: '#666',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  confirmButtonText: {
    color: '#000',
    fontFamily: 'MontserratBold',
    fontSize: 16,
  },
  cancelButtonText: {
    color: '#fcfaec',
    fontFamily: 'MontserratBold',
    fontSize: 16,
  },



  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    borderRadius: 16,
    padding: 24,
    maxWidth: 400,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 10,
    alignItems: 'center',
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'MontserratBold',
    textAlign: 'center',
    marginBottom: 12,
  },
  modalMessage: {
    fontSize: 16,
    fontFamily: 'MontserratRegular',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  modalButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 100,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontFamily: 'MontserratBold',
    color: '#000',
  },
});

export default memo(Login);