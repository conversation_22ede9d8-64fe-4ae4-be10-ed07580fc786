import { DarkTheme, DefaultTheme, ThemeProvider as NavigationThemeProvider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import { View, Platform, StyleSheet } from 'react-native';
import { setBackgroundColorAsync } from 'expo-system-ui';
import 'react-native-reanimated';

import { useColorScheme } from '@/shared/hooks/useColorScheme';
import { AuthProvider } from '@/shared/contexts/AuthContext';
import { ProtectedRoute } from '@/ui/common/ProtectedRoute';
import { ThemeProvider, useTheme } from '@/shared/contexts/ThemeContext';
import { GitHubAuthProvider } from '@/shared/contexts/GitHubAuthContext';
import { pushNotificationService } from '@/lib/services/notifications/PushNotificationService';
import { NotificationHandler } from '@/lib/services/notifications/NotificationHandler';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

// Create a wrapper component to use theme context inside the provider
function AppContent() {
  const { isDark, colors } = useTheme();

  // Set system navigation bar background color to match theme
  useEffect(() => {
    if (Platform.OS === 'android') {
      setBackgroundColorAsync(colors.background);
    }
  }, [colors.background]);

  // Create navigation theme based on our theme context
  const navigationTheme = {
    ...(isDark ? DarkTheme : DefaultTheme),
    colors: {
      ...(isDark ? DarkTheme.colors : DefaultTheme.colors),
      background: colors.background,
      card: colors.surface,
      text: colors.text,
      border: colors.border,
      primary: colors.primary,
    },
  };

  return (
    <NavigationThemeProvider value={navigationTheme}>
      {Platform.OS === 'web' ? (
        <View style={[styles.webContainer, { backgroundColor: colors.background }]}>
          <View style={[styles.webContent, { backgroundColor: colors.background }]}>
            <Stack
              screenOptions={{
                headerShown: false,
                animation: 'slide_from_right',
                animationDuration: 250,
                gestureEnabled: true,
                gestureDirection: 'horizontal',
              }}
            >
              <Stack.Screen name="SignIn/index" />
              <Stack.Screen name="(tabs)" />
              <Stack.Screen
                name="ProgramDetails"
                options={{
                  animation: 'slide_from_right',
                  animationDuration: 200,
                }}
              />
              <Stack.Screen
                name="PaymentConfirmation"
                options={{
                  animation: 'slide_from_right',
                  animationDuration: 200,
                }}
              />
              <Stack.Screen
                name="UserProfile"
                options={{
                  animation: 'slide_from_right',
                  animationDuration: 200,
                }}
              />
              <Stack.Screen
                name="MyPrograms"
                options={{
                  animation: 'slide_from_right',
                  animationDuration: 200,
                }}
              />
              <Stack.Screen name="ConfirmDelete" />
              <Stack.Screen name="PushNotifications" />
              <Stack.Screen name="EmailSettings" />
              <Stack.Screen name="Disputes" />
              <Stack.Screen name="Support" />
              <Stack.Screen name="FAQ" />
              <Stack.Screen name="Notifications" />
              <Stack.Screen name="TestNotifications" />
              <Stack.Screen
                name="CustomStake"
                options={{
                  animation: 'slide_from_right',
                  animationDuration: 200,
                }}
              />
              <Stack.Screen name="integrations" />
              <Stack.Screen name="+not-found" />
            </Stack>
          </View>
        </View>
      ) : (
        <Stack
          screenOptions={{
            headerShown: false,
            animation: 'slide_from_right',
            animationDuration: 250,
            gestureEnabled: true,
            gestureDirection: 'horizontal',
          }}
        >
          <Stack.Screen name="SignIn/index" />
          <Stack.Screen name="(tabs)" />
          <Stack.Screen
            name="ProgramDetails"
            options={{
              animation: 'slide_from_right',
              animationDuration: 200,
            }}
          />
          <Stack.Screen
            name="PaymentConfirmation"
            options={{
              animation: 'slide_from_right',
              animationDuration: 200,
            }}
          />
          <Stack.Screen
            name="UserProfile"
            options={{
              animation: 'slide_from_right',
              animationDuration: 200,
            }}
          />
          <Stack.Screen
            name="MyPrograms"
            options={{
              animation: 'slide_from_right',
              animationDuration: 200,
            }}
          />
          <Stack.Screen name="ConfirmDelete" />
          <Stack.Screen name="PushNotifications" />
          <Stack.Screen name="EmailSettings" />
          <Stack.Screen name="Disputes" />
          <Stack.Screen name="Support" />
          <Stack.Screen name="FAQ" />
          <Stack.Screen name="Notifications" />
          <Stack.Screen name="TestNotifications" />
          <Stack.Screen
            name="CustomStake"
            options={{
              animation: 'slide_from_right',
              animationDuration: 200,
            }}
          />
          <Stack.Screen name="integrations" />
          <Stack.Screen name="+not-found" />
        </Stack>
      )}
      <StatusBar style={isDark ? "light" : "dark"} />
    </NavigationThemeProvider>
  );
}

export default function RootLayout() {
  const [loaded] = useFonts({
    // Montserrat font family for modern UI design
    MontserratRegular: require("../assets/fonts/Montserrat-Regular.ttf"),
    MontserratMedium: require("../assets/fonts/Montserrat-MediumItalic.ttf"), // Using existing medium variant
    MontserratBold: require("../assets/fonts/Montserrat-Bold.ttf"),
    // Keep SpaceMono for code/monospace needs
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();

      // Initialize push notification service
      const initPushNotifications = async () => {
        try {
          const success = await pushNotificationService.initialize();
          if (success) {
            console.log('Push notification service initialized successfully');
          } else {
            console.warn('Push notification service initialization failed');
          }
        } catch (error) {
          console.error('Error initializing push notification service:', error);
        }
      };

      initPushNotifications();
    }
  }, [loaded]);



  if (!loaded) {
    return null;
  }

  return (
    <AuthProvider>
      <ThemeProvider>
        <GitHubAuthProvider>
          <NotificationHandler />
          <AppContent />
        </GitHubAuthProvider>
      </ThemeProvider>
    </AuthProvider>
  );
}

const styles = StyleSheet.create({
  webContainer: {
    flex: 1,
    // backgroundColor will be set dynamically based on theme
  },
  webContent: {
    maxWidth: 480, // Maximum width similar to mobile devices
    width: '100%',
    height: '100%',
    alignSelf: 'center',
    // backgroundColor will be set dynamically based on theme
    overflow: 'hidden',
    // Add subtle shadow to make it look like a device
    ...Platform.select({
      web: {
        boxShadow: '0px 0px 20px rgba(0, 0, 0, 0.15)',
      },
    }),
  },
});

