import React, { useCallback, useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  BackHandler,
  Image,
  Alert,
  Animated,
  Platform,
} from "react-native";
import { useLocalSearchPara<PERSON>, useRouter, useFocusEffect } from "expo-router";
import { firestoreService } from "../lib/services/database";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { LinearGradient } from 'expo-linear-gradient';
import { BlurView } from 'expo-blur';
import { getId, getFname } from "@/lib/utils/variables";
import FAQSection from "@/ui/pages/Explore/FAQSection";
import { ProtectedRoute } from "@/ui/common/ProtectedRoute";
import { LazyScreen } from "@/ui/common/LazyScreen";
import { getUserTimezone } from "@/lib/utils/timezoneUtils";

import { useTheme } from "@/shared/contexts/ThemeContext";

// Platform-specific blur configuration - REDUCED BLACK BLUR
const getBlurConfig = () => {
  if (Platform.OS === 'web') {
    return {
      intensity: 25, // Reduced blur intensity
      tint: 'dark' as const // Black blur instead of white
    };
  } else if (Platform.OS === 'android') {
    return {
      intensity: 25, // Reduced blur intensity
      tint: 'dark' as const, // Black blur instead of white
      experimentalBlurMethod: 'dimezisBlurView' as const
    };
  } else {
    // iOS
    return {
      intensity: 80, // Reduced blur intensity
      tint: 'systemMaterialDark' as const // Dark system material for iOS
    };
  }
};

interface Program {
  id: string;
  name: string;
  description: string;
  duration: number | string;
  startDate: string;
  endDate: string;
  betAmount: number;
  participantsCount: number;
  registrationsOpen: boolean;
  status: "upcoming" | "ongoing" | "ended";
  category: string;
  moderator: string;
  createdAt: string;
  createdBy: string;
  defaultLives: number;
  maxLivePurchase: number;
  headline: string;
  image?: {
    url: string;
    alt: string;
  };
}

// Floating Particle Component
const FloatingParticle: React.FC<{
  delay: number;
  size: number;
  duration: number;
  left: number;
  top: number;
}> = ({ delay, size, duration, left, top }) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0.3)).current;
  const scale = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    const startAnimation = () => {

      // DRAMATIC floating motion - much larger movements
      Animated.loop(
        Animated.sequence([
          Animated.timing(translateX, {
            toValue: Math.random() * 100 - 50, // Much larger range (-50 to +50)
            duration: duration,
            useNativeDriver: true,
          }),
          Animated.timing(translateX, {
            toValue: Math.random() * 100 - 50,
            duration: duration,
            useNativeDriver: true,
          }),
        ])
      ).start();

      Animated.loop(
        Animated.sequence([
          Animated.timing(translateY, {
            toValue: Math.random() * 80 - 40, // Much larger vertical movement
            duration: duration * 0.7,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: Math.random() * 80 - 40,
            duration: duration * 0.7,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // DRAMATIC pulsing opacity - more intense
      Animated.loop(
        Animated.sequence([
          Animated.timing(opacity, {
            toValue: 1.0, // Full opacity
            duration: 800, // Faster pulsing
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0.1, // Almost invisible
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // DRAMATIC scale animation - bigger changes
      Animated.loop(
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 2.0, // Double size!
            duration: 1200, // Faster scaling
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 0.3, // Very small
            duration: 1200,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    const timer = setTimeout(startAnimation, delay);
    return () => clearTimeout(timer);
  }, [delay, duration, translateX, translateY, opacity, scale]);

  return (
    <Animated.View
      style={{
        position: 'absolute',
        left: `${left}%`,
        top: `${top}%`,
        width: size,
        height: size,
        borderRadius: size / 2,
        backgroundColor: '#FFD700', // Golden color
        shadowColor: '#FFD700',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 1.0, // Maximum shadow intensity
        shadowRadius: size, // Larger glow radius
        elevation: 15, // Higher elevation for stronger shadow
        zIndex: 1, // Behind the text (text has zIndex: 20)
        transform: [
          { translateX },
          { translateY },
          { scale },
        ],
        opacity,
      }}
    />
  );
};

// Sparkle Particle Component - for extra drama
const SparkleParticle: React.FC<{
  delay: number;
  size: number;
  duration: number;
  left: number;
  top: number;
}> = ({ delay, size, duration, left, top }) => {
  const translateX = useRef(new Animated.Value(0)).current;
  const translateY = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(0)).current;
  const rotate = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const startAnimation = () => {
      // Rapid sparkle movement
      Animated.loop(
        Animated.sequence([
          Animated.timing(translateX, {
            toValue: Math.random() * 60 - 30,
            duration: duration,
            useNativeDriver: true,
          }),
          Animated.timing(translateX, {
            toValue: Math.random() * 60 - 30,
            duration: duration,
            useNativeDriver: true,
          }),
        ])
      ).start();

      Animated.loop(
        Animated.sequence([
          Animated.timing(translateY, {
            toValue: Math.random() * 60 - 30,
            duration: duration * 0.8,
            useNativeDriver: true,
          }),
          Animated.timing(translateY, {
            toValue: Math.random() * 60 - 30,
            duration: duration * 0.8,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Rapid flashing
      Animated.loop(
        Animated.sequence([
          Animated.timing(opacity, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(opacity, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Spinning rotation
      Animated.loop(
        Animated.timing(rotate, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        })
      ).start();
    };

    const timer = setTimeout(startAnimation, delay);
    return () => clearTimeout(timer);
  }, [delay, duration, translateX, translateY, opacity, rotate]);

  const spin = rotate.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={{
        position: 'absolute',
        left: `${left}%`,
        top: `${top}%`,
        width: size,
        height: size,
        backgroundColor: '#FFFFFF', // White sparkles for contrast
        shadowColor: '#FFFFFF',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 1,
        shadowRadius: size * 2,
        elevation: 20,
        zIndex: 1,
        transform: [
          { translateX },
          { translateY },
          { rotate: spin },
        ],
        opacity,
      }}
    />
  );
};

// Ticking Clock Animation Component
const TickingClock: React.FC = () => {
  const rotation = useRef(new Animated.Value(0)).current;
  const scale = useRef(new Animated.Value(1)).current;
  const pulseOpacity = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    // Moderate ticking rotation animation
    const tickAnimation = () => {
      Animated.sequence([
        // Moderate tick to the right
        Animated.timing(rotation, {
          toValue: 0.035, // Increased rotation (about 12 degrees)
          duration: 150,
          useNativeDriver: true,
        }),
        // Back to center
        Animated.timing(rotation, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // Repeat the ticking animation
        setTimeout(tickAnimation, 800); // Moderate pause between ticks
      });
    };

    // Moderate pulsing scale animation
    const pulseAnimation = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(scale, {
            toValue: 1.08, // Increased scale change
            duration: 1200, // Moderate speed
            useNativeDriver: true,
          }),
          Animated.timing(scale, {
            toValue: 1,
            duration: 1200,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    // Moderate opacity pulsing
    const opacityPulse = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseOpacity, {
            toValue: 0.85, // More noticeable opacity change
            duration: 1600, // Moderate speed
            useNativeDriver: true,
          }),
          Animated.timing(pulseOpacity, {
            toValue: 1,
            duration: 1600,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    // Start all animations
    tickAnimation();
    pulseAnimation();
    opacityPulse();
  }, [rotation, scale, pulseOpacity]);

  const rotateInterpolate = rotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Animated.View
      style={[
        {
          backgroundColor: '#000000',
          borderWidth: 2,
          borderColor: '#FFD700',
          borderRadius: 12,
          padding: 4,
          alignItems: 'center',
          justifyContent: 'center',
          marginBottom: 4,
          transform: [{ scale }],
          opacity: pulseOpacity,
        },
      ]}
    >
      <Animated.View
        style={{
          transform: [{ rotate: rotateInterpolate }],
        }}
      >
        <MaterialCommunityIcons name="clock-fast" size={18} color="#FFD700" />
      </Animated.View>
    </Animated.View>
  );
};

// Animated Button Component with Vanta.js-style animation
const AnimatedJoinButton: React.FC<{
  onPress: () => void;
  disabled: boolean;
  loading: boolean;
  designSystem: any;
  text?: string;
}> = ({ onPress, disabled, loading, designSystem, text = "Join Challenge Now" }) => {
  const [isPressed, setIsPressed] = React.useState(false);

  // Reset the pressed state when screen comes into focus (when coming back)
  useFocusEffect(
    React.useCallback(() => {
      setIsPressed(false);
    }, [])
  );

  const handlePress = () => {
    // Disable blur immediately when pressed
    setIsPressed(true);
    // Small delay to ensure blur is disabled before navigation
    setTimeout(() => {
      onPress();
    }, 50);
  };
  // Create multiple particles with different properties - BIGGER BLOBS!
  const particles = Array.from({ length: 15 }, (_, i) => ({
    id: i,
    delay: i * 150, // Slightly slower staggered start
    size: Math.random() * 20 + 15, // Much bigger blobs (15-35px)
    duration: 2000 + Math.random() * 1000, // Slightly slower for bigger blobs
    left: Math.random() * 100, // Full width coverage
    top: Math.random() * 100,  // Full height coverage
  }));

  // Create additional sparkle particles for extra drama
  const sparkles = Array.from({ length: 15 }, (_, i) => ({
    id: `sparkle-${i}`,
    delay: i * 150,
    size: Math.random() * 4 + 2, // Smaller sparkles (2-6px)
    duration: 800 + Math.random() * 400, // Very fast movement
    left: Math.random() * 100,
    top: Math.random() * 100,
  }));



  return (
    <TouchableOpacity
      style={[
        {
          borderRadius: designSystem.borderRadius.xl,
          overflow: 'hidden',
          position: 'relative',
          borderWidth: 2, // Gold border width
          borderColor: '#FFD700', // Gold border color
        }
      ]}
      onPress={handlePress}
      disabled={disabled || loading || isPressed}
      activeOpacity={0.8}
    >
      {/* Black Button Background */}
      <View
        style={{
          paddingVertical: designSystem.spacing.xxl * 1.5, // Much taller button
          paddingHorizontal: designSystem.spacing.xxl,
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          backgroundColor: '#000000', // Black background
          minHeight: 80, // Ensure minimum height
        }}
      >
        {/* Floating Particles Animation - Inside the gradient */}
        {particles.map((particle) => (
          <FloatingParticle
            key={particle.id}
            delay={particle.delay}
            size={particle.size}
            duration={particle.duration}
            left={particle.left}
            top={particle.top}
          />
        ))}

        {/* Sparkle Particles for extra drama */}
        {sparkles.map((sparkle) => (
          <SparkleParticle
            key={sparkle.id}
            delay={sparkle.delay}
            size={sparkle.size}
            duration={sparkle.duration}
            left={sparkle.left}
            top={sparkle.top}
          />
        ))}

        {/* Button Content - Above blur */}
        {loading ? (
          <ActivityIndicator size="small" color="#FFD700" style={{ zIndex: 30 }} />
        ) : (
          <View style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 30, // Above blur layer
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
          }}>
            <Text style={{
              color: "#FFD700", // Gold text color
              fontSize: designSystem.typography.fontSize.xxl, // Even larger font size (20px)
              fontFamily: "MontserratBold", // Use the proper bold font
              letterSpacing: designSystem.typography.letterSpacing.wide,
              textShadowColor: 'rgba(0, 0, 0, 0.8)',
              textShadowOffset: { width: 0, height: 1 },
              textShadowRadius: 2,
            }}>{text}</Text>
          </View>
        )}

        {/* Properly contained BlurView overlay - only when not pressed */}
        {!isPressed && (
          <BlurView
            intensity={getBlurConfig().intensity}
            tint={getBlurConfig().tint}
            {...(Platform.OS !== 'web' && { experimentalBlurMethod: getBlurConfig().experimentalBlurMethod })}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 2,
              // Ensure blur is contained within button bounds
              borderRadius: designSystem.borderRadius.xl,
              overflow: 'hidden',
            }}
          />
        )}
      </View>
    </TouchableOpacity>
  );
};

const ProgramDetailsComponent: React.FC = () => {
  const { colors, isDark, designSystem } = useTheme();
  const styles = createStyles(colors, isDark, designSystem);

  const { programId, paymentCompleted, programPreferences } = useLocalSearchParams<{
    programId?: string;
    paymentCompleted?: string;
    programPreferences?: string;
  }>();
  const router = useRouter();
  const [program, setProgram] = useState<Program | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [signUpLoading, setSignUpLoading] = useState<boolean>(false);
  const [userId, setUserId] = useState<string>("");
  const [isUserSignedUp, setIsUserSignedUp] = useState<boolean>(false);
  const [timeUntilStart, setTimeUntilStart] = useState<string>("");



  // Combined fetching of userId and program data - delayed to allow smooth transitions
  useEffect(() => {
    const fetchData = async () => {
      try {
        const id = String(await getId());
        setUserId(id);
        if (!programId) return;

        // Add a small delay to allow navigation transition to complete
        await new Promise(resolve => setTimeout(resolve, 150));
        setLoading(true);

        const result = await firestoreService.programs.getProgramById(programId);
        if (result.success && result.data) {
          setProgram(result.data as Program);
        } else {
          Alert.alert("Error", "Program not found");
          router.back();
        }
      } catch (error) {
        console.error("Error fetching program:", error);
        Alert.alert("Error", "Something went wrong");
        router.back();
      } finally {
        setLoading(false);
      }
    };

    // Use requestAnimationFrame to ensure this runs after the transition starts
    const frame = requestAnimationFrame(() => {
      fetchData();
    });

    return () => cancelAnimationFrame(frame);
  }, [programId, router]);

  // Check participant status once userId & programId are available
  useEffect(() => {
    const checkParticipant = async () => {
      if (!userId || !programId) return;
      const result = await firestoreService.participants.isParticipantEnrolled(programId, userId);
      if (result.success) {
        setIsUserSignedUp(result.data || false);
      }
    };
    checkParticipant();
  }, [userId, programId]);

  // Countdown timer effect - shows time until program actually starts
  useEffect(() => {
    if (!program?.startDate) return;

    const updateCountdown = async () => {
      try {
        // Get user's timezone
        const userTimezone = getUserTimezone();

        // Get current time
        const now = new Date();

        // For now, we'll calculate when the program starts at midnight in the program's "base" timezone
        // Since we don't have participant data here, we'll assume the program starts at midnight UTC
        // and show the countdown in the user's local timezone

        // Parse the program start date (YYYY-MM-DD format)
        const [year, month, day] = program.startDate.split('-').map(Number);

        // Create the program start time at midnight UTC
        const programStartTimeUTC = new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));

        // Calculate time difference
        const timeDiff = programStartTimeUTC.getTime() - now.getTime();

        console.log('Program Start Countdown Debug:', {
          userTimezone,
          now: now.toISOString(),
          nowInUserTz: now.toLocaleString('en-US', { timeZone: userTimezone }),
          programStartDate: program.startDate,
          programStartTimeUTC: programStartTimeUTC.toISOString(),
          programStartInUserTz: programStartTimeUTC.toLocaleString('en-US', { timeZone: userTimezone }),
          timeDiff,
          timeDiffHours: timeDiff / (1000 * 60 * 60)
        });

        if (timeDiff > 0) {
          const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

          if (days > 0) {
            setTimeUntilStart(`${days}d ${hours}h`);
          } else if (hours > 0) {
            setTimeUntilStart(`${hours}h ${minutes}m`);
          } else {
            setTimeUntilStart(`${minutes}m`);
          }
        } else {
          setTimeUntilStart("LIVE");
        }
      } catch (error) {
        console.error('Error calculating countdown:', error);
        setTimeUntilStart("ERROR");
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [program?.startDate]);

  // Prevent hardware back press during sign-up
  useEffect(() => {
    const onBackPress = () => signUpLoading;
    const backHandler = BackHandler.addEventListener(
      "hardwareBackPress",
      onBackPress
    );
    return () => backHandler.remove();
  }, [signUpLoading]);

  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    });
  }, []);

  const handleSignUp = useCallback(async () => {
    if (!program || !userId) return;
    setSignUpLoading(true);

    try {
      // Parse program preferences from URL parameter
      let parsedPreferences = undefined;
      if (programPreferences) {
        try {
          parsedPreferences = JSON.parse(decodeURIComponent(programPreferences));
        } catch (error) {
          console.warn('Failed to parse program preferences:', error);
        }
      }

      const result = await firestoreService.enrollUserInProgram(userId, program.id, {
        fname: (await getFname()) || 'User',
        paymentDone: paymentCompleted === 'true',
        timezone: getUserTimezone(),
        programPreferences: parsedPreferences
      });

      if (result.success) {
        Alert.alert(
          "Success!",
          "You have successfully signed up for the program.",
          [
            {
              text: "Go to Progress",
              onPress: () =>
                router.replace(
                  `/progress?selectedProgramId=${program.id}&t=${Date.now()}`
                ),
            },
          ]
        );
      } else {
        Alert.alert("Error", result.error || "Failed to sign up for program");
      }
    } catch (error) {
      console.error("Error signing up for program:", error);
      Alert.alert("Error", "Failed to sign up. Please try again.");
    } finally {
      setSignUpLoading(false);
    }
  }, [program, userId, paymentCompleted, router]);

  const handleBeforeSignUp = () => {
    // Check if payment was already completed (coming back from payment screen)
    if (paymentCompleted === 'true') {
      handleSignUp();
    } else {
      // Direct redirect to payment confirmation page
      router.push(`/PaymentConfirmation?programId=${program?.id}`);
    }
  };

  if (loading) {
    return (
      <View style={[styles.loadingContainer, { backgroundColor: colors.background }]}>
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  if (!program) return null;

  const weeks = Math.ceil(Number(program.duration) / 7);

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollContainer}>
        {/* PROGRAM IMAGE BANNER WITH OVERLAY */}
      <View style={styles.bannerContainer}>
        {program.image?.url && (
          <Image
            source={{ uri: program.image.url }}
            style={styles.programBanner}
            resizeMode="cover"
            loadingIndicatorSource={require('@/assets/images/icon.png')}
            fadeDuration={200}
          />
        )}
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.4)']}
          style={styles.bannerOverlay}
        />
      </View>



      {/* HERO SECTION WITH GLASSMORPHISM */}
      <View style={styles.heroSection}>
        <BlurView intensity={60} tint="dark" style={styles.heroBlur}>
          <LinearGradient
            colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
            style={styles.heroGradient}
          >
            <View style={styles.heroContent}>
              {/* Program Title */}
              <Text style={[styles.programTitle, { color: colors.text }]}>{program.name}</Text>

              {/* Program Headline */}
              <Text style={[styles.programHeadline, { color: colors.textSecondary }]}>{program.headline}</Text>

              {/* Creator & Moderator Info */}
              <View style={styles.programInfoContainer}>
                <Text style={[styles.programInfoText, { color: colors.textSecondary }]}>Created by </Text>
                <TouchableOpacity style={[styles.creatorButton, { backgroundColor: colors.glassBackground }]}>
                  <Text style={[styles.creatorButtonText, { color: colors.primary }]}>{program.createdBy}</Text>
                </TouchableOpacity>
                <Text style={[styles.programInfoText, { color: colors.textSecondary }]}> • Moderated by </Text>
                <TouchableOpacity style={[styles.moderatorButton, { backgroundColor: colors.glassBackground }]}>
                  <Text style={[styles.moderatorButtonText, { color: colors.primary }]}>
                    {program.moderator}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </LinearGradient>
        </BlurView>
      </View>

      {/* STATS CARDS */}
      <View style={styles.statsContainer}>
        <View style={styles.statsRow}>
          <BlurView intensity={40} tint="dark" style={[styles.statCard, styles.statCardLeft]}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.statCardGradient}
            >
              <MaterialCommunityIcons name="currency-usd" size={24} color={colors.primary} />
              <Text style={[styles.statLabel, { color: colors.textMuted }]}>Pledge</Text>
              <Text style={[styles.statValue, { color: colors.text }]}>${program.betAmount}</Text>
            </LinearGradient>
          </BlurView>

          <BlurView intensity={40} tint="dark" style={[styles.statCard, styles.statCardCenter]}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.statCardGradient}
            >
              <MaterialCommunityIcons name="account-group" size={24} color={colors.primary} />
              <Text style={[styles.statLabel, { color: colors.textMuted }]}>Players</Text>
              <Text style={[styles.statValue, { color: colors.text }]}>{program.participantsCount}</Text>
            </LinearGradient>
          </BlurView>

          <BlurView intensity={40} tint="dark" style={[styles.statCard, styles.statCardRight]}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.statCardGradient}
            >
              <MaterialCommunityIcons name="trophy" size={24} color={colors.primary} />
              <Text style={[styles.statLabel, { color: colors.textMuted }]}>Pool</Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                ${program.participantsCount * program.betAmount}
              </Text>
            </LinearGradient>
          </BlurView>
        </View>
      </View>
      {/* CHALLENGE DETAILS CARD */}
      <View style={styles.detailsSection}>
        <BlurView intensity={40} tint="dark" style={styles.detailsCard}>
          <LinearGradient
            colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
            style={styles.detailsGradient}
          >
            <View style={styles.sectionHeader}>
              <MaterialCommunityIcons name="information" size={24} color={colors.primary} />
              <Text style={[styles.sectionTitle, { color: colors.text }]}>Challenge Details</Text>
            </View>

            <Text style={[styles.descriptionText, { color: colors.textSecondary }]}>{program.description}</Text>

            {/* Duration & Dates Row */}
            <View style={styles.infoGrid}>
              <View style={styles.infoItem}>
                <MaterialCommunityIcons name="calendar-clock" size={20} color={colors.primary} />
                <Text style={[styles.infoLabel, { color: colors.textMuted }]}>Duration</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{weeks} Weeks</Text>
              </View>

              <View style={styles.infoItem}>
                <MaterialCommunityIcons name="calendar-start" size={20} color={colors.primary} />
                <Text style={[styles.infoLabel, { color: colors.textMuted }]}>Start Date</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>
                  {formatDate(program.startDate)}
                </Text>
              </View>

              <View style={styles.infoItem}>
                <MaterialCommunityIcons name="calendar-end" size={20} color={colors.primary} />
                <Text style={[styles.infoLabel, { color: colors.textMuted }]}>End Date</Text>
                <Text style={[styles.infoValue, { color: colors.text }]}>{formatDate(program.endDate)}</Text>
              </View>
            </View>

            <View style={[styles.divider, { backgroundColor: colors.glassBorder }]} />

            {/* Lives Information */}
            <View style={styles.livesSection}>
              <View style={styles.livesHeader}>
                <MaterialCommunityIcons name="heart" size={20} color={colors.error} />
                <Text style={[styles.livesTitle, { color: colors.text }]}>Grace Days</Text>
              </View>

              <View style={styles.livesGrid}>
                <View style={styles.livesItem}>
                  <Text style={[styles.livesLabel, { color: colors.textMuted }]}>Default Lives</Text>
                  <Text style={[styles.livesValue, { color: colors.text }]}>{program.defaultLives}</Text>
                </View>

                <View style={styles.livesItem}>
                  <Text style={[styles.livesLabel, { color: colors.textMuted }]}>Max Purchase</Text>
                  <Text style={[styles.livesValue, { color: colors.text }]}>{program.maxLivePurchase}</Text>
                </View>
              </View>
            </View>
          </LinearGradient>
        </BlurView>
      </View>

      {/* JOIN CHALLENGE BUTTON - BEFORE FAQ */}
      {program.registrationsOpen && !isUserSignedUp && (
        <View style={styles.preActionSection}>
          <AnimatedJoinButton
            onPress={handleBeforeSignUp}
            disabled={signUpLoading}
            loading={signUpLoading}
            designSystem={designSystem}
          />
        </View>
      )}

      <FAQSection
        category={program.category}
        betAmount={program.betAmount}
        startDate={program.startDate}
        endDate={program.endDate}
      />

      {/* ACTION BUTTONS */}
      <View style={styles.actionSection}>
        {program.registrationsOpen ? (
          !isUserSignedUp ? (
            <AnimatedJoinButton
              onPress={handleBeforeSignUp}
              disabled={signUpLoading}
              loading={signUpLoading}
              designSystem={designSystem}
              text="Join the Challenge"
            />
          ) : (
            <BlurView intensity={40} tint="dark" style={styles.joinedContainer}>
              <LinearGradient
                colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
                style={styles.joinedGradient}
              >
                <MaterialCommunityIcons name="check-circle" size={24} color={colors.success} />
                <Text style={[styles.joinedText, { color: colors.success }]}>Already Joined</Text>
              </LinearGradient>
            </BlurView>
          )
        ) : (
          <BlurView intensity={40} tint="dark" style={styles.closedContainer}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.closedGradient}
            >
              <MaterialCommunityIcons name="lock" size={24} color={colors.error} />
              <Text style={[styles.closedText, { color: colors.error }]}>Registration Closed</Text>
            </LinearGradient>
          </BlurView>
        )}

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            if (!signUpLoading) {
              router.back();
            }
          }}
          disabled={signUpLoading}
          activeOpacity={0.8}
        >
          <BlurView intensity={30} tint="dark" style={styles.backButtonBlur}>
            <LinearGradient
              colors={[colors.glassBackground, colors.glassBackgroundSecondary]}
              style={styles.backButtonGradient}
            >
              <MaterialCommunityIcons name="arrow-left" size={20} color={colors.text} style={styles.backButtonIcon} />
              <Text style={[styles.backButtonText, { color: colors.text }]}>Go Back</Text>
            </LinearGradient>
          </BlurView>
        </TouchableOpacity>
      </View>
      </ScrollView>

      {/* STICKY COUNTDOWN TAG - Always show for testing */}
      {program.registrationsOpen && (
        <View style={styles.stickyCountdownContainer}>
          <BlurView intensity={80} tint="dark" style={styles.countdownBlur}>
            <View style={styles.countdownGradient}>
              <View style={styles.countdownContent}>
                <TickingClock />
                <Text style={styles.countdownLabel}>STARTS</Text>
                <Text style={styles.countdownLabel}>IN</Text>
                <Text style={styles.countdownTime}>{timeUntilStart || "2d 5h"}</Text>
              </View>
            </View>
          </BlurView>
        </View>
      )}
    </View>
  );
};

const createStyles = (colors: any, isDark: boolean, designSystem: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background
  },
  scrollContainer: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: colors.background
  },

  // Banner Section - MADE MORE PROMINENT
  bannerContainer: {
    position: 'relative',
    width: '100%',
    height: 300, // Increased from 200 to 300 for more prominence
  },
  programBanner: {
    width: "100%",
    height: '100%',
    position: 'absolute',
  },
  bannerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },

  // Luxury Casino Badge
  luxuryBadgeContainer: {
    position: 'absolute',
    top: 160, // Positioned within safe area, overlapping bottom of banner
    right: designSystem.spacing.lg,
    width: 120,
    height: 120,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 100, // Ensure it's above other elements
  },

  // Outer Glow Ring
  glowRing: {
    position: 'absolute',
    width: 120,
    height: 120,
    borderRadius: 60,
    opacity: 0.8,
    ...designSystem.shadows.xl,
    shadowColor: '#FFD700',
    shadowOpacity: 0.8,
    shadowRadius: 20,
    elevation: 15,
  },
  glowGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 60,
    opacity: 0.3,
  },

  // Main Luxury Badge
  luxuryBadge: {
    width: 100,
    height: 100,
    borderRadius: 50,
    overflow: 'hidden',
    borderWidth: 3,
    borderColor: '#FFD700',
    ...designSystem.shadows.xl,
    shadowColor: '#FFD700',
    shadowOpacity: 0.6,
    shadowRadius: 15,
    elevation: 12,
  },
  luxuryGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },

  // Inner Ring Effect
  innerRing: {
    width: '100%',
    height: '100%',
    borderRadius: 46,
    overflow: 'hidden',
    borderWidth: 2,
    borderColor: 'rgba(0,0,0,0.2)',
  },
  innerGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Badge Content
  badgeContent: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  luxuryText: {
    fontSize: designSystem.typography.fontSize.sm,
    fontFamily: 'MontserratBold',
    letterSpacing: 2,
    color: '#000',
    textShadowColor: 'rgba(255,255,255,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
    marginTop: 2,
  },

  // Sparkle Effects
  sparkleContainer: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  sparkle1: {
    position: 'absolute',
    top: 8,
    right: 12,
    opacity: 0.8,
  },
  sparkle2: {
    position: 'absolute',
    bottom: 15,
    left: 8,
    opacity: 0.6,
  },
  sparkle3: {
    position: 'absolute',
    top: 25,
    left: 5,
    opacity: 0.7,
  },

  // Hero Section - ADJUSTED FOR TALLER BANNER
  heroSection: {
    marginTop: -80, // Increased overlap from -60 to -80 to maintain proportional overlap
    marginHorizontal: designSystem.spacing.sm, // Reduced margin for broader box
    marginBottom: designSystem.spacing.md,
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  heroBlur: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
  },
  heroGradient: {
    padding: designSystem.spacing.xxl,
    borderRadius: designSystem.borderRadius.xl,
  },
  heroContent: {
    alignItems: 'center',
  },



  // Program Title & Info
  programTitle: {
    fontSize: designSystem.typography.fontSize.xxxl,
    fontFamily: "MontserratBold",
    color: colors.text,
    textAlign: "center",
    marginBottom: designSystem.spacing.sm,
    letterSpacing: designSystem.typography.letterSpacing.wide,
  },
  programHeadline: {
    fontSize: designSystem.typography.fontSize.lg,
    color: colors.textSecondary,
    textAlign: "center",
    fontFamily: "MontserratRegular",
    marginBottom: designSystem.spacing.sm, // Reduced from lg to sm
    lineHeight: designSystem.typography.lineHeight.relaxed * designSystem.typography.fontSize.lg,
  },
  programInfoContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    flexWrap: 'wrap',
  },
  programInfoText: {
    fontSize: designSystem.typography.fontSize.sm,
    color: colors.textSecondary,
    fontFamily: "MontserratRegular",
  },
  creatorButton: {
    paddingVertical: designSystem.spacing.xs,
    paddingHorizontal: designSystem.spacing.sm,
    borderRadius: designSystem.borderRadius.sm,
    marginHorizontal: designSystem.spacing.xs,
    borderWidth: 1,
    borderColor: colors.glassBorder,
  },
  creatorButtonText: {
    fontSize: designSystem.typography.fontSize.sm,
    color: colors.primary,
    fontFamily: "MontserratBold"
  },
  moderatorButton: {
    paddingVertical: designSystem.spacing.xs,
    paddingHorizontal: designSystem.spacing.sm,
    borderRadius: designSystem.borderRadius.sm,
    marginHorizontal: designSystem.spacing.xs,
    borderWidth: 1,
    borderColor: colors.glassBorder,
  },
  moderatorButtonText: {
    fontSize: designSystem.typography.fontSize.sm,
    color: colors.primary,
    fontFamily: "MontserratBold"
  },

  // Stats Section
  statsContainer: {
    marginHorizontal: designSystem.spacing.sm, // Reduced margin for broader boxes
    marginBottom: designSystem.spacing.md,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: designSystem.spacing.sm,
  },
  statCard: {
    flex: 1,
    borderRadius: designSystem.borderRadius.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  statCardLeft: {
    marginRight: designSystem.spacing.xs / 2,
  },
  statCardCenter: {
    marginHorizontal: designSystem.spacing.xs / 2,
  },
  statCardRight: {
    marginLeft: designSystem.spacing.xs / 2,
  },
  statCardGradient: {
    padding: designSystem.spacing.lg,
    alignItems: 'center',
    minHeight: 80,
    justifyContent: 'center',
  },
  statLabel: {
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: "MontserratRegular",
    marginTop: designSystem.spacing.xs,
    textAlign: 'center',
  },
  statValue: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginTop: designSystem.spacing.xs / 2,
    textAlign: 'center',
  },

  // Details Section
  detailsSection: {
    marginHorizontal: designSystem.spacing.sm, // Reduced margin for broader box
    marginBottom: designSystem.spacing.md,
  },
  detailsCard: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.lg,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  detailsGradient: {
    padding: designSystem.spacing.xxl,
    borderRadius: designSystem.borderRadius.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designSystem.spacing.md, // Reduced from lg to md
  },
  sectionTitle: {
    fontSize: designSystem.typography.fontSize.xl,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm,
  },
  descriptionText: {
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: "MontserratRegular",
    lineHeight: designSystem.typography.lineHeight.relaxed * designSystem.typography.fontSize.md,
    marginBottom: designSystem.spacing.md, // Reduced from lg to md
  },

  // Info Grid
  infoGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: designSystem.spacing.md, // Reduced from lg to md
  },
  infoItem: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: designSystem.spacing.xs,
  },
  infoLabel: {
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: "MontserratRegular",
    marginTop: designSystem.spacing.xs,
    textAlign: 'center',
  },
  infoValue: {
    fontSize: designSystem.typography.fontSize.sm,
    fontFamily: "MontserratBold",
    marginTop: designSystem.spacing.xs / 2,
    textAlign: 'center',
  },

  // Lives Section
  livesSection: {
    marginTop: designSystem.spacing.md, // Reduced from lg to md
  },
  livesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: designSystem.spacing.md,
  },
  livesTitle: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm,
  },
  livesGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  livesItem: {
    alignItems: 'center',
    flex: 1,
  },
  livesLabel: {
    fontSize: designSystem.typography.fontSize.xs,
    fontFamily: "MontserratRegular",
    textAlign: 'center',
  },
  livesValue: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginTop: designSystem.spacing.xs / 2,
    textAlign: 'center',
  },

  // Divider
  divider: {
    height: 1,
    backgroundColor: colors.glassBorder,
    marginVertical: designSystem.spacing.md, // Reduced from lg to md
    opacity: 0.5,
  },

  // Pre-FAQ Action Section
  preActionSection: {
    marginHorizontal: designSystem.spacing.sm,
    marginBottom: designSystem.spacing.md,
  },

  // Pre-FAQ CTA Button
  preCtaButton: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    ...designSystem.shadows.xl,
    shadowColor: colors.primary,
  },
  preCtaButtonGradient: {
    paddingVertical: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.xxl,
    alignItems: 'center',
    justifyContent: 'center',
  },
  preCtaButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  preCtaButtonIcon: {
    marginRight: designSystem.spacing.sm,
  },
  preCtaButtonText: {
    color: "#000000",
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    letterSpacing: designSystem.typography.letterSpacing.wide,
  },

  // Action Section
  actionSection: {
    marginHorizontal: designSystem.spacing.sm, // Reduced margin for broader buttons
    marginBottom: designSystem.spacing.lg,
    gap: designSystem.spacing.md,
  },

  // CTA Button
  ctaButton: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    ...designSystem.shadows.xl,
    shadowColor: colors.primary,
  },
  ctaButtonGradient: {
    paddingVertical: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.xxl,
    alignItems: 'center',
    justifyContent: 'center',
  },
  ctaButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  ctaButtonIcon: {
    marginRight: designSystem.spacing.sm,
  },
  ctaButtonText: {
    color: "#000000",
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    letterSpacing: designSystem.typography.letterSpacing.wide,
  },

  // Joined State
  joinedContainer: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.md,
    shadowColor: colors.success,
  },
  joinedGradient: {
    paddingVertical: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.xxl,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  joinedText: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm,
  },

  // Closed State
  closedContainer: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.glassBorder,
    ...designSystem.shadows.md,
    shadowColor: colors.error,
  },
  closedGradient: {
    paddingVertical: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.xxl,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  closedText: {
    fontSize: designSystem.typography.fontSize.lg,
    fontFamily: "MontserratBold",
    marginLeft: designSystem.spacing.sm,
  },

  // Back Button
  backButton: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
    ...designSystem.shadows.md,
    shadowColor: isDark ? colors.neumorphicShadowLight : colors.neumorphicShadowDark,
  },
  backButtonBlur: {
    borderRadius: designSystem.borderRadius.xl,
    overflow: 'hidden',
  },
  backButtonGradient: {
    paddingVertical: designSystem.spacing.lg,
    paddingHorizontal: designSystem.spacing.xxl,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: colors.glassBorder,
  },
  backButtonIcon: {
    marginRight: designSystem.spacing.sm,
  },
  backButtonText: {
    fontSize: designSystem.typography.fontSize.md,
    fontFamily: "MontserratRegular",
  },

  // Legacy styles (keeping for compatibility)
  cancelButton: {
    marginTop: 10,
    alignItems: "center",
  },

  // Sticky Countdown Tag
  stickyCountdownContainer: {
    position: 'absolute',
    right: 0,
    top: '50%',
    transform: [{ translateY: -60 }], // Center vertically
    width: 60, // Very compact width
    height: 120,
    zIndex: 1000,
    ...designSystem.shadows.xl,
    shadowColor: colors.error,
    shadowOpacity: 0.6,
    elevation: 20,
  },
  countdownBlur: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: designSystem.borderRadius.xl,
    borderBottomLeftRadius: designSystem.borderRadius.xl,
    borderWidth: 2,
    borderColor: '#FFD700', // Golden border
    borderRightWidth: 0, // No border on the right side since it's against the screen edge
    overflow: 'hidden',
  },
  countdownGradient: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: designSystem.spacing.sm,
    paddingHorizontal: designSystem.spacing.xs,
  },
  countdownContent: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
  },
  countdownLabel: {
    fontSize: 9,
    fontFamily: 'MontserratBold',
    color: '#FFD700', // Gold font color
    letterSpacing: 0.5,
    textAlign: 'center',
    marginTop: 2,
    lineHeight: 10,
  },
  countdownTime: {
    fontSize: 14,
    fontFamily: 'MontserratBold',
    color: '#FFD700', // Gold font color
    textAlign: 'center',
    marginTop: designSystem.spacing.xs,
    textShadowColor: 'rgba(0,0,0,0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    lineHeight: 16,
  },
  countdownUnit: {
    fontSize: 8,
    fontFamily: 'MontserratRegular',
    color: '#FFF',
    textAlign: 'center',
    marginTop: 2,
    opacity: 0.8,
  },
  countdownTimezone: {
    fontSize: 7,
    fontFamily: 'MontserratRegular',
    color: '#FFF',
    textAlign: 'center',
    marginTop: 1,
    opacity: 0.6,
  },

  // Star Container and Icon Styles
  starContainer: {
    backgroundColor: '#000000', // Black background
    borderWidth: 2,
    borderColor: '#FFD700', // Golden border
    borderRadius: 12,
    padding: 4,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  starIcon: {
    // Gold color is already set in the component
  },
});

const ProgramDetails: React.FC = () => {
  return (
    <ProtectedRoute>
      <LazyScreen delay={100}>
        <ProgramDetailsComponent />
      </LazyScreen>
    </ProtectedRoute>
  );
};

export default ProgramDetails;
