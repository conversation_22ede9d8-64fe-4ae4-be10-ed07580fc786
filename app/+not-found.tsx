import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Stack, useRouter } from 'expo-router';

export default function NotFoundScreen() {
  const router = useRouter();

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ title: 'Oops!' }} />
      <Text style={styles.title}>Page Not Found</Text>
      <Text style={styles.description}>
        The page you are looking for doesn't exist. Please check the URL or go back to the home page.
      </Text>
      <TouchableOpacity style={styles.link} onPress={() => router.push('/')}>
        <Text style={styles.linkText}>Go Back Home</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFEB3B',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
  },
  link: {
    marginTop: 15,
    paddingVertical: 15,
    paddingHorizontal: 20,
    backgroundColor: '#FFEB3B',
    borderRadius: 8,
  },
  linkText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000',
  },
});
