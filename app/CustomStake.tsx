import React, { useCallback, useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Platform,
  KeyboardAvoidingView,
  StatusBar,
  SafeAreaView,
  Modal,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from '@/shared/contexts/ThemeContext';
import { useCustomStakeForm } from '@/shared/hooks/useCustomStakeForm';
import { useCustomStakeValidation, useStakeCalculation } from '@/shared/hooks/useCustomStakeValidation';
import { ErrorModal } from '@/shared/components/modals';
import { CustomStakeSubmissionData } from '@/shared/types/customStake';
import { SentenceForm } from '@/ui/forms/SentenceForm';
import { LazySkeletonScreen } from '@/ui/common/LazySkeletonScreen';
import { SimpleFormSkeleton } from '@/ui/common/SimpleFormSkeleton';
import { PreferencesFeaturesForm } from '@/ui/forms/PreferencesFeaturesForm';
import { SummaryConfirmationForm } from '@/ui/forms/SummaryConfirmationForm';
import { ProgressBar } from '@/ui/forms/ProgressBar';
import { firestoreService } from '@/lib/services/database';
import { getId } from '@/lib/utils/variables';

// All interfaces and constants are now imported from types/customStake.ts

// Constants moved to types/customStake.ts

const CustomStakeComponent: React.FC = () => {
  const { colors, isDark } = useTheme();
  const router = useRouter();
  const searchParams = useLocalSearchParams();

  // Inline styles
  const styles = {
    safeArea: {
      flex: 1,
      backgroundColor: colors.background,
    },
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    header: {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      paddingHorizontal: 20,
      paddingTop: 12,
      paddingBottom: 12,
      backgroundColor: colors.background,
    },
    backButton: {
      padding: 10,
      borderRadius: 12,
      backgroundColor: colors.surface,
      minWidth: 44,
      minHeight: 44,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
    },
    headerTitle: {
      flex: 1,
      fontSize: 20,
      fontFamily: 'MontserratBold',
      color: colors.text,
      textAlign: 'center' as const,
      paddingHorizontal: 10,
    },
    headerSpacer: {
      width: 44,
    },
    scrollContent: {
      flex: 1,
    },
    scrollContentContainer: {
      flexGrow: 1,
    },
    submitContainer: {
      paddingHorizontal: 24,
      paddingVertical: 24,
      paddingBottom: Platform.OS === 'ios' ? 40 : 24,
      backgroundColor: colors.surface,
      borderTopWidth: 2,
      borderTopColor: colors.border,
    },
    submitButton: {
      backgroundColor: colors.primary,
      borderRadius: 12,
      paddingVertical: 18,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      marginHorizontal: 4,
    },
    submitButtonDisabled: {
      opacity: 0.5,
      backgroundColor: colors.border,
    },
    submitButtonText: {
      fontSize: 18,
      fontFamily: 'MontserratBold',
      color: '#000000',
      letterSpacing: 0.5,
    },
    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center' as const,
      alignItems: 'center' as const,
      padding: 20,
    },
    modalContainer: {
      borderRadius: 16,
      padding: 0,
      maxWidth: 400,
      width: '100%' as const,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.25,
      shadowRadius: 8,
      elevation: 10,
    },
    modalHeader: {
      padding: 20,
      paddingBottom: 10,
      alignItems: 'center' as const,
    },
    modalTitle: {
      fontSize: 20,
      fontFamily: 'MontserratBold',
      textAlign: 'center' as const,
    },
    modalContent: {
      paddingHorizontal: 20,
      paddingBottom: 20,
    },
    modalMessage: {
      fontSize: 16,
      fontFamily: 'MontserratRegular',
      textAlign: 'center' as const,
      lineHeight: 24,
    },
    modalButtons: {
      padding: 20,
      paddingTop: 10,
    },
    modalButton: {
      paddingVertical: 12,
      paddingHorizontal: 24,
      borderRadius: 12,
      alignItems: 'center' as const,
    },
    modalButtonText: {
      fontSize: 16,
      fontFamily: 'MontserratBold',
      color: '#000',
    },
  };

  // State for managing steps
  const [currentStep, setCurrentStep] = useState(1);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false); // Prevent duplicate submissions

  // Modal state for replacing Alert dialogs
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });

  // Use custom hooks for form management
  const {
    formData,
    updateCommitment,
    updateEvidenceType,
    updateReportingFrequency,
    updateRecipient,
    updateTimingType,
    updateAmount,
    updateStartDate,
    updateEndDate,
    updateWeekLength,
    updateTimesPerWeek,
    updateTimesPerMonth,
    updateLocationData,
    updateAppName,
    updateStravaActivity,
    updateTiming,
    updateMotivationalNote,
    updateReminderChannels,
    updateStrictnessLevel,
    updateNotificationTiming,
    updateProgramPreferences,
    updateFormData,
  } = useCustomStakeForm();

  // Handle template pre-filling - use a ref to prevent infinite loops
  const templateAppliedRef = useRef(false);

  // ScrollView ref for scrolling to top
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    if (searchParams.template === 'true' && !templateAppliedRef.current) {
      // Delay template processing to avoid blocking the initial render
      const processTemplate = async () => {
        // Wait for the screen to render first
        await new Promise(resolve => setTimeout(resolve, 100));

        const templateData: any = {};

        if (searchParams.commitment) templateData.commitment = searchParams.commitment as string;
        if (searchParams.evidenceType) templateData.evidenceType = searchParams.evidenceType as string;
        if (searchParams.reportingFrequency) templateData.reportingFrequency = searchParams.reportingFrequency as string;
        if (searchParams.amountPerReport) templateData.amountPerReport = parseInt(searchParams.amountPerReport as string);
        if (searchParams.timesPerWeek) templateData.timesPerWeek = parseInt(searchParams.timesPerWeek as string);
        if (searchParams.timesPerMonth) templateData.timesPerMonth = parseInt(searchParams.timesPerMonth as string);
        if (searchParams.weekLength) templateData.weekLength = parseInt(searchParams.weekLength as string);

        // Apply template data to form
        updateFormData(templateData);
        templateAppliedRef.current = true;
      };

      // Use requestAnimationFrame to ensure this runs after the initial render
      const frame = requestAnimationFrame(() => {
        processTemplate();
      });

      return () => cancelAnimationFrame(frame);
    }
  }, [searchParams.template, searchParams.commitment, searchParams.evidenceType, searchParams.reportingFrequency, searchParams.amountPerReport, searchParams.timesPerWeek, searchParams.timesPerMonth, searchParams.weekLength, updateFormData]);

  // Use validation hooks - always call them to follow Rules of Hooks
  const validation = useCustomStakeValidation(formData);
  const totalStake = useStakeCalculation(formData);

  const handleNextClick = useCallback(() => {
    // Use validation from hook
    if (!validation.isValid) {
      const firstError = validation.errors[0];
      setErrorModalData({
        title: 'Error',
        message: firstError.message
      });
      setErrorModalVisible(true);
      return;
    }

    // Move to step 2
    setCurrentStep(2);
  }, [validation]);

  const handleStep2Next = useCallback(() => {
    // Move to step 3 (summary and confirmation)
    setCurrentStep(3);

    // Scroll to top when moving to step 3
    setTimeout(() => {
      scrollViewRef.current?.scrollTo({ y: 0, animated: true });
    }, 100);
  }, []);

  const handleFinalSubmit = useCallback(async () => {
    // Prevent multiple submissions
    if (isSubmitting) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Get current user ID
      const userId = await getId();
      if (!userId) {
        setErrorModalData({
          title: 'Error',
          message: 'User ID not found. Please sign in and try again.'
        });
        setErrorModalVisible(true);
        setIsSubmitting(false);
        return;
      }

      // Create comprehensive data object
      const stakeData: CustomStakeSubmissionData = {
        ...formData,
        totalStake,
        createdAt: new Date().toISOString(),
        // Calculate actual end date for weekly/monthly commitments
        calculatedEndDate: formData.startDate && formData.weekLength ?
          (() => {
            const start = new Date(formData.startDate);
            const multiplier = formData.reportingFrequency === 'weekly' ? 7 : 30;
            const endDate = new Date(start);
            endDate.setDate(start.getDate() + (formData.weekLength * multiplier));
            return endDate.toISOString();
          })() : formData.endDate,
        // Add reporting schedule details
        reportingSchedule: {
          frequency: formData.reportingFrequency,
          ...(formData.reportingFrequency === 'weekly' && {
            dayOfWeek: formData.startDate ? new Date(formData.startDate).getDay() : undefined,
            timesPerWeek: formData.timesPerWeek
          }),
          ...(formData.reportingFrequency === 'monthly' && {
            dayOfMonth: formData.startDate ? new Date(formData.startDate).getDate() : undefined,
            timesPerMonth: formData.timesPerMonth
          })
        }
      };

      // Prepare commit data for Firestore
      // Filter out undefined values to prevent Firebase errors
      const baseCommitData = {
        commitment: stakeData.commitment,
        evidenceType: stakeData.evidenceType,
        reportingFrequency: stakeData.reportingFrequency,
        startDate: stakeData.startDate?.toISOString ? stakeData.startDate.toISOString() : stakeData.startDate,
        endDate: stakeData.endDate?.toISOString ? stakeData.endDate.toISOString() : stakeData.endDate,
        recipient: stakeData.recipient,
        amountPerReport: stakeData.amountPerReport,
        totalStake: stakeData.totalStake,
        referee: stakeData.referee,
        reportingSchedule: stakeData.reportingSchedule
      };

      // Add optional fields only if they have valid values (not undefined or null)
      const commitData: any = { ...baseCommitData };

      // Add calculatedEndDate if it exists
      if (stakeData.calculatedEndDate) commitData.calculatedEndDate = stakeData.calculatedEndDate;

      // Add weekLength only for frequencies that need it and when it has a value
      if (stakeData.reportingFrequency !== 'once' && stakeData.weekLength !== undefined && stakeData.weekLength !== null) {
        commitData.weekLength = stakeData.weekLength;
      }

      // Add timing fields only if they have values
      if (stakeData.timingType) commitData.timingType = stakeData.timingType;
      if (stakeData.beforeTime) commitData.beforeTime = stakeData.beforeTime;
      if (stakeData.afterTime) commitData.afterTime = stakeData.afterTime;
      if (stakeData.startTime) commitData.startTime = stakeData.startTime;
      if (stakeData.endTime) commitData.endTime = stakeData.endTime;

      // Add evidence-specific fields only if they have values
      if (stakeData.location) commitData.location = stakeData.location;
      if (stakeData.locationData) commitData.locationData = stakeData.locationData;
      if (stakeData.appName) commitData.appName = stakeData.appName;
      if (stakeData.stravaActivity) commitData.stravaActivity = stakeData.stravaActivity;

      // Add program preferences only if they exist
      if (stakeData.programPreferences) commitData.programPreferences = stakeData.programPreferences;

      // Add frequency-specific fields only if they have values
      if (stakeData.timesPerWeek !== undefined && stakeData.timesPerWeek !== null) {
        commitData.timesPerWeek = stakeData.timesPerWeek;
      }
      if (stakeData.timesPerMonth !== undefined && stakeData.timesPerMonth !== null) {
        commitData.timesPerMonth = stakeData.timesPerMonth;
      }

      // Save commit to Firestore
      const result = await firestoreService.commits.createFullCommit(userId, commitData);

      if (result.success) {

        const message = `Your custom stake has been created successfully!\n\nTotal Stake: $${stakeData.totalStake.toFixed(2)}`;

        setSuccessMessage(message);
        setShowSuccessModal(true);
      } else {
        console.error('Failed to save commit:', result.error);
        setSuccessMessage('Failed to save your commitment. Please try again.');
        setShowSuccessModal(true);
      }
    } catch (error) {
      console.error('Error in handleFinalSubmit:', error);
      setSuccessMessage('An unexpected error occurred. Please try again.');
      setShowSuccessModal(true);
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, totalStake, router, isSubmitting]);

  const handleBackToStep1 = useCallback(() => {
    setCurrentStep(1);
  }, []);

  const handleBackToStep2 = useCallback(() => {
    setCurrentStep(2);
  }, []);



  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={colors.background}
        translucent={false}
      />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => {
              console.log('Back button pressed!');
              if (currentStep > 1) {
                setCurrentStep(currentStep - 1);
              } else {
                router.back();
              }
            }}
            accessibilityLabel="Go back"
            accessibilityRole="button"
            activeOpacity={0.8}
          >
            <MaterialIcons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Custom Stake</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Progress Bar */}
        <ProgressBar
          currentStep={currentStep}
          totalSteps={3}
        />

        {/* Scrollable Content */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {currentStep === 1 ? (
            <SentenceForm
              formData={formData}
              onUpdateCommitment={updateCommitment}
              onUpdateEvidenceType={updateEvidenceType}
              onUpdateReportingFrequency={updateReportingFrequency}
              onUpdateTimingType={updateTimingType}
              onUpdateRecipient={updateRecipient}
              onUpdateAmount={updateAmount}
              onUpdateStartDate={updateStartDate}
              onUpdateEndDate={updateEndDate}
              onUpdateWeekLength={updateWeekLength}
              onUpdateTimesPerWeek={updateTimesPerWeek}
              onUpdateTimesPerMonth={updateTimesPerMonth}
              onUpdateLocationData={updateLocationData}
              onUpdateAppName={updateAppName}
              onUpdateStravaActivity={updateStravaActivity}
              onUpdateTiming={updateTiming}
            />
          ) : currentStep === 2 ? (
            <PreferencesFeaturesForm
              programPreferences={formData.programPreferences!}
              reportingFrequency={formData.reportingFrequency}
              onUpdateMotivationalNote={updateMotivationalNote}
              onUpdateReminderChannels={updateReminderChannels}
              onUpdateStrictnessLevel={updateStrictnessLevel}
              onUpdateNotificationTiming={updateNotificationTiming}
              onUpdateProgramPreferences={updateProgramPreferences}
              onSubmit={handleStep2Next}
              onCancel={handleBackToStep1}
            />
          ) : (
            <SummaryConfirmationForm
              formData={formData}
              totalStake={totalStake}
              onSubmit={handleFinalSubmit}
              onCancel={handleBackToStep2}
            />
          )}
        </ScrollView>

        {/* Submit Button - Only show on Step 1 */}
        {currentStep === 1 && (
          <View style={styles.submitContainer}>
            <TouchableOpacity
              style={[
                styles.submitButton,
                !validation.isValid && styles.submitButtonDisabled
              ]}
              onPress={handleNextClick}
              disabled={!validation.isValid}
            >
              <Text style={styles.submitButtonText}>Next</Text>
            </TouchableOpacity>
          </View>
        )}
      </KeyboardAvoidingView>

      {/* Success Modal */}
      <Modal
        visible={showSuccessModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowSuccessModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContainer, { backgroundColor: colors.surface }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {successMessage.includes('Failed') || successMessage.includes('error') ? 'Error' : 'Success!'}
              </Text>
            </View>

            <View style={styles.modalContent}>
              <Text style={[styles.modalMessage, { color: colors.text }]}>
                {successMessage}
              </Text>
            </View>

            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, { backgroundColor: colors.primary }]}
                onPress={() => {
                  setShowSuccessModal(false);
                  if (!successMessage.includes('Failed') && !successMessage.includes('error')) {
                    // Navigate to progress page to maintain flow after successful commit creation
                    router.replace('/(tabs)/progress');
                  }
                }}
                activeOpacity={0.7}
              >
                <Text style={styles.modalButtonText}>OK</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />
    </SafeAreaView>
  );
};



const CustomStake: React.FC = () => {
  return (
    <LazySkeletonScreen
      delay={1500}
      skeleton={<SimpleFormSkeleton />}
    >
      <CustomStakeComponent />
    </LazySkeletonScreen>
  );
};

export default CustomStake;
