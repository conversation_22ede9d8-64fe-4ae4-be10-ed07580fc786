import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  ScrollView,
  TextInput,
  FlatList,
  StyleSheet,
  ActivityIndicator,
} from "react-native";
import { getId } from "@/lib/utils/variables";
import Header from "@/ui/common/Header";
import { firestoreService } from "../lib/services/database";
import { Dispute } from "../lib/services/database/types";
import { useTheme } from "@/shared/contexts/ThemeContext";
import { ErrorModal, SuccessModal } from "@/shared/components/modals";


const SUBJECTS = [
  "Validation Issues",
  "Technical Issue",
  "Cash In/Out Issue",
  "Against player(s) in the pool",
  "Against Moderator",
  "Others",
];

const DROPDOWN_LEFT_OFFSET = 10;

const Disputes = () => {
  const { colors } = useTheme();
  const [disputes, setDisputes] = useState<Dispute[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("");
  const [explanation, setExplanation] = useState("");

  // Modal states for replacing Alert dialogs
  const [successModalVisible, setSuccessModalVisible] = useState(false);
  const [successModalData, setSuccessModalData] = useState({ title: '', message: '' });
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [errorModalData, setErrorModalData] = useState({ title: '', message: '' });

  // For program selection (we use a string array for program names)
  const [programs, setPrograms] = useState<string[]>([]);
  const [showProgramDropdown, setShowProgramDropdown] = useState(false);
  const [programDropdownPosition, setProgramDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    height: 0,
  });

  // For subject selection
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [subjectDropdownPosition, setSubjectDropdownPosition] = useState({
    top: 0,
    left: 0,
    width: 0,
    height: 0,
  });

  // Fetch user email and programs from Firestore
  const fetchUserData = useCallback(async () => {
    try {
      const userEmail = String(await getId());
      setEmail(userEmail);

      // Fetch user's programs using centralized service
      const result = await firestoreService.getUserDashboardData(userEmail);
      if (result.success && result.data) {
        const programNames = result.data.programs.map(p => p.name || "Unnamed Program");
        setPrograms([...programNames, "Other"]);
      }
    } catch (error) {
      console.error("Error fetching user data:", error);
    }
  }, []);

  // Fetch disputes for the current user
  const fetchDisputes = useCallback(async () => {
    try {
      setIsLoading(true);
      const result = await firestoreService.getDisputesByUser(email);
      if (result.success) {
        setDisputes(result.data || []);
      } else {
        console.error("Error fetching disputes:", result.error);
      }
    } catch (error) {
      console.error("Error fetching disputes:", error);
    } finally {
      setIsLoading(false);
    }
  }, [email]);

  useEffect(() => {
    fetchUserData();
  }, [fetchUserData]);

  useEffect(() => {
    if (email) {
      fetchDisputes();
    }
  }, [email, fetchDisputes]);

  // Dismiss any open dropdown when tapping outside
  const dismissDropdowns = () => {
    if (showProgramDropdown) setShowProgramDropdown(false);
    if (showSubjectDropdown) setShowSubjectDropdown(false);
  };

  // Handle dispute submission
  const handleSubmitDispute = async () => {
    if (!selectedProgram || !selectedSubject || !explanation.trim()) {
      setErrorModalData({
        title: "Error",
        message: "Please fill in all fields."
      });
      setErrorModalVisible(true);
      return;
    }
    try {
      setIsSubmitting(true); // Start loading

      const result = await firestoreService.createDispute({
        email,
        program: selectedProgram,
        programName: selectedProgram,
        subject: selectedSubject,
        explanation,
        status: "Pending",
      });

      if (result.success) {
        // Create notification for dispute submission
        await firestoreService.notifications.createDisputeSubmittedNotification(
          email,
          selectedSubject,
          selectedProgram
        );

        setSuccessModalData({
          title: "Dispute Submitted",
          message: "Your dispute has been recorded."
        });
        setSuccessModalVisible(true);
        setSelectedProgram("");
        setSelectedSubject("");
        setExplanation("");
        setShowForm(false);
        fetchDisputes();
      } else {
        setErrorModalData({
          title: "Error",
          message: result.error || "Failed to submit dispute"
        });
        setErrorModalVisible(true);
      }
    } catch (error) {
      console.error("Error submitting dispute:", error);
      setErrorModalData({
        title: "Error",
        message: "Failed to submit dispute. Please try again."
      });
      setErrorModalVisible(true);
    } finally {
      setIsSubmitting(false); // Stop loading
    }
  };

  const renderDisputeCard = ({ item }: { item: any }) => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <Text style={styles.cardTitle} numberOfLines={1}>
          {item.programName}
        </Text>
        <View style={styles.statusContainer}>
          <View style={[styles.statusDot, { backgroundColor: getStatusColor(item.status) }]} />
          <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
            {item.status}
          </Text>
        </View>
      </View>
      
      <Text style={styles.subjectText} numberOfLines={2}>
        {item.subject}
      </Text>
      
      <View style={styles.cardFooter}>
        <Text style={styles.dateText}>
          {item.createdAt?.toDate().toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })}
        </Text>
      </View>
    </View>
  );

  // Add this helper function for status colors
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'pending':
        return colors.warning;
      case 'resolved':
        return colors.success;
      case 'rejected':
        return colors.error;
      default:
        return colors.textMuted;
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <Header />
      <TouchableWithoutFeedback onPress={dismissDropdowns}>
        <View style={styles.content}>
          {/* If no form is shown, display dispute list */}
          {!showForm ? (
            <>
              <Text style={styles.pageTitle}>Your Disputes</Text>
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#FFEB3B" />
                </View>
              ) : disputes.length === 0 ? (
                <Text style={styles.emptyText}>No disputes found.</Text>
              ) : (
                <FlatList
                  data={disputes}
                  renderItem={renderDisputeCard}
                  keyExtractor={(_, index) => index.toString()}
                  contentContainerStyle={styles.listContainer}
                />
              )}
              <TouchableOpacity
                style={styles.floatingButton}
                onPress={() => setShowForm(true)}
              >
                <Text style={styles.floatingButtonText}>Start a Dispute</Text>
              </TouchableOpacity>
            </>
          ) : (
            <ScrollView contentContainerStyle={styles.formContainer}>
              <Text style={styles.pageTitle}>Dispute Creation</Text>

              <Text style={styles.label}>From:</Text>
              <Text style={styles.value}>{email}</Text>

              {/* Program Selection */}
              <Text style={styles.label}>Program:</Text>
              <TouchableOpacity
                style={styles.dropdown}
                activeOpacity={1}
                onLayout={(event) => {
                  const { x, y, width, height } = event.nativeEvent.layout;
                  setProgramDropdownPosition({ top: y, left: x, width, height });
                }}
                onPress={() => {
                  if (showSubjectDropdown) setShowSubjectDropdown(false);
                  setShowProgramDropdown(!showProgramDropdown);
                }}
              >
                <View style={styles.dropdownInner}>
                  <Text style={styles.dropdownText}>
                    {selectedProgram || "Select Program"}
                  </Text>
                  <Text style={styles.arrow}>▼</Text>
                </View>
              </TouchableOpacity>

              {/* Subject Selection */}
              <Text style={styles.label}>Subject:</Text>
              <TouchableOpacity
                style={styles.dropdown}
                activeOpacity={1}
                onLayout={(event) => {
                  const { x, y, width, height } = event.nativeEvent.layout;
                  setSubjectDropdownPosition({ top: y, left: x, width, height });
                }}
                onPress={() => {
                  if (showProgramDropdown) setShowProgramDropdown(false);
                  setShowSubjectDropdown(!showSubjectDropdown);
                }}
              >
                <View style={styles.dropdownInner}>
                  <Text style={styles.dropdownText}>
                    {selectedSubject || "Select Subject"}
                  </Text>
                  <Text style={styles.arrow}>▼</Text>
                </View>
              </TouchableOpacity>

              <Text style={styles.label}>Explain in detail:</Text>
              <TextInput
                style={styles.textArea}
                value={explanation}
                onChangeText={setExplanation}
                placeholder="Type here..."
                placeholderTextColor="#888"
                multiline
                numberOfLines={6}
                textAlignVertical="top"
              />

              <TouchableOpacity
                style={[styles.submitButton, isSubmitting && styles.submitButtonDisabled]}
                onPress={handleSubmitDispute}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="black" size="small" />
                ) : (
                  <Text style={styles.submitButtonText}>Submit</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.backButton,
                  isSubmitting && styles.backButtonDisabled
                ]}
                onPress={() => setShowForm(false)}
                disabled={isSubmitting}
              >
                <Text style={[
                  styles.backButtonText,
                  isSubmitting && styles.backButtonTextDisabled
                ]}>Back</Text>
              </TouchableOpacity>
            </ScrollView>
          )}

          {/* Program Dropdown Overlay */}
          {showProgramDropdown && (
            <View
              style={[
                styles.overlay,
                {
                  top: programDropdownPosition.top + programDropdownPosition.height,
                  left: programDropdownPosition.left + DROPDOWN_LEFT_OFFSET,
                  width: programDropdownPosition.width,
                },
              ]}
            >
              {programs.map((program, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownOption}
                  onPress={() => {
                    setSelectedProgram(program);
                    setShowProgramDropdown(false);
                  }}
                >
                  <Text style={styles.dropdownText}>{program}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Subject Dropdown Overlay */}
          {showSubjectDropdown && (
            <View
              style={[
                styles.overlay,
                {
                  top: subjectDropdownPosition.top + subjectDropdownPosition.height,
                  left: subjectDropdownPosition.left + DROPDOWN_LEFT_OFFSET,
                  width: subjectDropdownPosition.width,
                },
              ]}
            >
              {SUBJECTS.map((subject, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.dropdownOption}
                  onPress={() => {
                    setSelectedSubject(subject);
                    setShowSubjectDropdown(false);
                  }}
                >
                  <Text style={styles.dropdownText}>{subject}</Text>
                </TouchableOpacity>
              ))}
            </View>
          )}
        </View>
      </TouchableWithoutFeedback>

      {/* Success Modal */}
      <SuccessModal
        visible={successModalVisible}
        onClose={() => setSuccessModalVisible(false)}
        title={successModalData.title}
        message={successModalData.message}
      />

      {/* Error Modal */}
      <ErrorModal
        visible={errorModalVisible}
        onClose={() => setErrorModalVisible(false)}
        title={errorModalData.title}
        message={errorModalData.message}
      />
    </View>
  );
};


const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  pageTitle: {
    fontSize: 24,
    color: colors.primary,
    fontWeight: "700",
    marginBottom: 20,
    marginTop: 8,
  },
  listContainer: {
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyText: {
    color: colors.textMuted,
    fontSize: 16,
    textAlign: "center",
    marginTop: 40,
  },
  card: {
    backgroundColor: colors.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.25)',
    elevation: 5,
    borderWidth: 1,
    borderColor: colors.border,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    flex: 1,
    marginRight: 12,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 13,
    fontWeight: '600',
  },
  subjectText: {
    fontSize: 15,
    color: colors.textSecondary,
    marginBottom: 12,
    lineHeight: 20,
  },
  cardFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  dateText: {
    fontSize: 12,
    color: colors.textMuted,
    fontWeight: '500',
  },
  floatingButton: {
    position: "absolute",
    bottom: 20,
    right: 16,
    left: 16,
    backgroundColor: colors.primary,
    borderRadius: 25,
    padding: 15,
    alignItems: "center",
    boxShadow: '0 2px 3.84px rgba(0, 0, 0, 0.25)',
    elevation: 5,
  },
  floatingButtonText: {
    color: colors.background,
    fontSize: 16,
    fontWeight: "bold",
  },
  formContainer: {
    paddingBottom: 40,
  },
  label: {
    color: colors.textSecondary,
    marginTop: 18,
    marginBottom: 4,
    fontSize: 15,
    fontWeight: "600",
  },
  value: { color: colors.text, fontSize: 15, marginBottom: 10 },
  dropdown: {
    backgroundColor: colors.surface,
    borderRadius: 8,
    padding: 14,
    marginTop: 4,
  },
  dropdownInner: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  dropdownText: { color: colors.text, fontSize: 14 },
  arrow: { color: colors.text, fontSize: 16 },
  overlay: {
    position: "absolute",
    backgroundColor: colors.surface,
    paddingVertical: 6,
    paddingHorizontal: 8,
    borderRadius: 10,
    zIndex: 999,
    marginTop: 4,
  },
  dropdownOption: {
    backgroundColor: colors.card,
    paddingVertical: 10,
    paddingHorizontal: 12,
    borderRadius: 6,
    marginVertical: 2,
  },
  textArea: {
    backgroundColor: colors.card,
    borderRadius: 10,
    padding: 14,
    color: colors.text,
    height: 160,
    fontSize: 14,
    marginTop: 6,
  },
  submitButton: {
    marginTop: 30,
    backgroundColor: colors.primary,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
    minHeight: 50,
    justifyContent: 'center',
  },
  submitButtonText: { color: colors.background, fontSize: 16, fontWeight: "bold" },
  backButton: {
    marginTop: 15,
    backgroundColor: colors.surface,
    paddingVertical: 12,
    borderRadius: 10,
    alignItems: "center",
  },

  backButtonText: {
    color: colors.text,
    fontSize: 15,
    fontWeight: "500"
  },
  submitButtonDisabled: {
    opacity: 0.7,
  },
  backButtonDisabled: {
    opacity: 0.7,
  },
  backButtonTextDisabled: {
    color: colors.textMuted,
  },
});

export default Disputes;
