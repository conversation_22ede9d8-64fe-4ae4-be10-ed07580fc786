{"name": "accustom-functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@google-cloud/scheduler": "^5.3.0", "@types/node-fetch": "^2.6.12", "firebase-admin": "^12.0.0", "firebase-functions": "^6.4.0", "node-fetch": "^3.3.2"}, "devDependencies": {"@types/node": "^18.19.117", "typescript": "^4.9.5"}, "private": true}