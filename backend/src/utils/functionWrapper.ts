/**
 * Function wrapper utilities for standardizing Cloud Function patterns
 * Provides consistent error handling, validation, and response formatting
 */

import { CallableRequest } from 'firebase-functions/v2/https';
import { validateRequired, withErrorHandling, createSuccessResponse, createErrorResponse, AppError, ErrorType } from './errorHandler';

export interface CloudFunctionConfig {
  functionName: string;
  requiredParams?: string[];
  validateAuth?: boolean;
  timeoutMs?: number;
}

export interface StandardResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
  details?: any;
}

/**
 * Wrapper for Cloud Functions with standardized error handling and validation
 */
export function wrapCloudFunction<T = any, R = any>(
  handler: (data: T, context?: any) => Promise<R>,
  config: CloudFunctionConfig
) {
  return withErrorHandling(async (request: CallableRequest<T>): Promise<StandardResponse<R>> => {
    const startTime = Date.now();

    try {
      // Extract data and context
      const data = request.data;
      const context = request.auth;

      // Validate authentication if required
      if (config.validateAuth && !context) {
        throw new AppError('Authentication required', ErrorType.PERMISSION_DENIED);
      }

      // Validate required parameters
      if (config.requiredParams && config.requiredParams.length > 0) {
        validateRequired(data as Record<string, any>, config.requiredParams);
      }

      // Set up timeout if specified
      let timeoutHandle: NodeJS.Timeout | undefined;
      if (config.timeoutMs) {
        timeoutHandle = setTimeout(() => {
          throw new AppError(`Function ${config.functionName} timed out after ${config.timeoutMs}ms`, ErrorType.TIMEOUT_ERROR);
        }, config.timeoutMs);
      }

      try {
        // Execute the handler
        const result = await handler(data, context);

        // Clear timeout
        if (timeoutHandle) {
          clearTimeout(timeoutHandle);
        }

        // Log execution time
        const executionTime = Date.now() - startTime;
        console.log(`Function ${config.functionName} completed in ${executionTime}ms`);

        // Return standardized success response
        return createSuccessResponse(result);

      } catch (handlerError) {
        // Clear timeout
        if (timeoutHandle) {
          clearTimeout(timeoutHandle);
        }
        throw handlerError;
      }

    } catch (error) {
      // Log execution time for errors too
      const executionTime = Date.now() - startTime;
      console.error(`Function ${config.functionName} failed after ${executionTime}ms:`, error);

      // Return standardized error response
      return createErrorResponse(error as Error) as StandardResponse<R>;
    }
  }, config.functionName);
}

/**
 * Wrapper specifically for program-related functions
 */
export function wrapProgramFunction<T extends { programId: string }, R = any>(
  handler: (data: T, context?: any) => Promise<R>,
  functionName: string,
  additionalRequiredParams: string[] = []
) {
  return wrapCloudFunction(handler, {
    functionName,
    requiredParams: ['programId', ...additionalRequiredParams],
    timeoutMs: 60000 // 1 minute timeout for program operations
  });
}

/**
 * Wrapper specifically for participant-related functions
 */
export function wrapParticipantFunction<T extends { programId: string; userId: string }, R = any>(
  handler: (data: T, context?: any) => Promise<R>,
  functionName: string,
  additionalRequiredParams: string[] = []
) {
  // Use longer timeout for scheduling operations that involve multiple scheduler jobs
  const timeoutMs = functionName.includes('Scheduling') ? 120000 : 30000; // 2 minutes for scheduling, 30 seconds for others

  return wrapCloudFunction(handler, {
    functionName,
    requiredParams: ['programId', 'userId', ...additionalRequiredParams],
    timeoutMs
  });
}

/**
 * Wrapper for migration and cleanup functions
 */
export function wrapMaintenanceFunction<T = any, R = any>(
  handler: (data: T, context?: any) => Promise<R>,
  functionName: string,
  requiredParams: string[] = []
) {
  return wrapCloudFunction(handler, {
    functionName,
    requiredParams,
    timeoutMs: 300000 // 5 minute timeout for maintenance operations
  });
}



/**
 * Wrapper for HTTP-triggered functions (like OAuth callbacks)
 */
export function wrapHttpFunction<R = any>(
  handler: (request: any, response: any) => Promise<R>,
  functionName: string
) {
  return withErrorHandling(async (request: any, response: any): Promise<void> => {
    const startTime = Date.now();

    try {
      console.log(`HTTP function ${functionName} called`);

      await handler(request, response);

      const executionTime = Date.now() - startTime;
      console.log(`HTTP function ${functionName} completed in ${executionTime}ms`);

    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`HTTP function ${functionName} failed after ${executionTime}ms:`, error);

      // Send error response if response hasn't been sent yet
      if (!response.headersSent) {
        response.status(500).json(createErrorResponse(error as Error));
      }
    }
  }, functionName);
}

/**
 * Wrapper specifically for Cloud Scheduler HTTPS functions
 * Includes CORS, method validation, and scheduler authentication
 */
export function wrapSchedulerHttpFunction<TData = any, TResult = any>(
  handler: (data: TData) => Promise<TResult>,
  functionName: string,
  requiredParams: string[] = []
) {
  return withErrorHandling(async (request: any, response: any): Promise<void> => {
    const startTime = Date.now();

    try {
      console.log(`Scheduler HTTP function ${functionName} called`);

      // Set CORS headers
      response.set('Access-Control-Allow-Origin', '*');
      response.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
      response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');

      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        response.status(204).send('');
        return;
      }

      // Only allow POST requests
      if (request.method !== 'POST') {
        response.status(405).json(createErrorResponse('Method not allowed'));
        return;
      }

      // Basic security check - ensure request comes from Google Cloud Scheduler
      const userAgent = request.get('User-Agent') || '';
      if (!userAgent.includes('Google-Cloud-Scheduler')) {
        console.warn(`Unauthorized access attempt to ${functionName}:`, {
          userAgent,
          ip: request.ip
        });
        response.status(403).json(createErrorResponse('Forbidden'));
        return;
      }

      // Extract and validate data from request body
      const { data } = request.body;
      if (!data) {
        response.status(400).json(createErrorResponse('Missing data in request body'));
        return;
      }

      // Validate required parameters
      const missing = requiredParams.filter(param => !data[param]);
      if (missing.length > 0) {
        response.status(400).json(createErrorResponse(
          `Missing required parameters: ${missing.join(', ')}`
        ));
        return;
      }

      // Execute the handler
      const result = await handler(data);

      // Send success response
      response.status(200).json(createSuccessResponse(result));

      const executionTime = Date.now() - startTime;
      console.log(`Scheduler HTTP function ${functionName} completed in ${executionTime}ms`);

    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`Scheduler HTTP function ${functionName} failed after ${executionTime}ms:`, error);

      // Send error response if response hasn't been sent yet
      if (!response.headersSent) {
        response.status(500).json(createErrorResponse(
          error instanceof Error ? error.message : 'Unknown error'
        ));
      }
    }
  }, functionName);
}

/**
 * Create a standardized logging function
 */
export function createLogger(functionName: string) {
  return {
    info: (message: string, data?: any) => {
      console.log(`[${functionName}] ${message}`, data || '');
    },
    warn: (message: string, data?: any) => {
      console.warn(`[${functionName}] ${message}`, data || '');
    },
    error: (message: string, error?: any) => {
      console.error(`[${functionName}] ${message}`, error || '');
    },
    debug: (message: string, data?: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug(`[${functionName}] ${message}`, data || '');
      }
    }
  };
}


