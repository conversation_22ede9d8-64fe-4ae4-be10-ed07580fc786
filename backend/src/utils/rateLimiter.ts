/**
 * Rate Limiter for Cloud Functions
 * Uses Firestore for distributed rate limiting across function instances
 */

import * as admin from 'firebase-admin';
import { AppError, ErrorType } from './errorHandler';

export interface RateLimitConfig {
  windowMs: number;  // Time window in milliseconds
  maxRequests: number;  // Maximum requests per window
  keyGenerator?: (request: any) => string;  // Custom key generator
}

export interface RateLimitData {
  count: number;
  windowStart: admin.firestore.Timestamp;
  lastRequest: admin.firestore.Timestamp;
}

export class RateLimiter {
  private db: admin.firestore.Firestore;
  private collectionName = 'rate_limits';

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Check if request is within rate limit
   */
  async checkRateLimit(
    key: string,
    config: RateLimitConfig
  ): Promise<{ allowed: boolean; remaining: number; resetTime: Date }> {
    const now = new Date();
    const windowStart = new Date(now.getTime() - config.windowMs);
    
    const rateLimitRef = this.db.collection(this.collectionName).doc(key);

    try {
      const result = await this.db.runTransaction(async (transaction) => {
        const doc = await transaction.get(rateLimitRef);
        
        let currentData: RateLimitData;
        
        if (!doc.exists) {
          // First request
          currentData = {
            count: 1,
            windowStart: admin.firestore.Timestamp.fromDate(now),
            lastRequest: admin.firestore.Timestamp.fromDate(now)
          };
          
          transaction.set(rateLimitRef, currentData);
          
          return {
            allowed: true,
            remaining: config.maxRequests - 1,
            resetTime: new Date(now.getTime() + config.windowMs)
          };
        }
        
        const existingData = doc.data() as RateLimitData;
        const existingWindowStart = existingData.windowStart.toDate();
        
        // Check if we're in a new window
        if (existingWindowStart < windowStart) {
          // New window, reset counter
          currentData = {
            count: 1,
            windowStart: admin.firestore.Timestamp.fromDate(now),
            lastRequest: admin.firestore.Timestamp.fromDate(now)
          };
          
          transaction.set(rateLimitRef, currentData);
          
          return {
            allowed: true,
            remaining: config.maxRequests - 1,
            resetTime: new Date(now.getTime() + config.windowMs)
          };
        }
        
        // Same window, check if limit exceeded
        if (existingData.count >= config.maxRequests) {
          return {
            allowed: false,
            remaining: 0,
            resetTime: new Date(existingWindowStart.getTime() + config.windowMs)
          };
        }
        
        // Increment counter
        currentData = {
          count: existingData.count + 1,
          windowStart: existingData.windowStart,
          lastRequest: admin.firestore.Timestamp.fromDate(now)
        };
        
        transaction.set(rateLimitRef, currentData);
        
        return {
          allowed: true,
          remaining: config.maxRequests - currentData.count,
          resetTime: new Date(existingWindowStart.getTime() + config.windowMs)
        };
      });

      return result;

    } catch (error) {
      console.error('Rate limit check failed:', error);
      // On error, allow the request (fail open)
      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: new Date(now.getTime() + config.windowMs)
      };
    }
  }

  /**
   * Middleware for rate limiting Cloud Functions
   */
  async enforceRateLimit(
    key: string,
    config: RateLimitConfig
  ): Promise<void> {
    const result = await this.checkRateLimit(key, config);
    
    if (!result.allowed) {
      throw new AppError(
        `Rate limit exceeded. Try again after ${result.resetTime.toISOString()}`,
        ErrorType.RATE_LIMITED,
        {
          resetTime: result.resetTime.toISOString(),
          remaining: result.remaining
        }
      );
    }
  }

  /**
   * Generate rate limit key for user-based limiting
   */
  static generateUserKey(userId: string, operation: string): string {
    return `user:${userId}:${operation}`;
  }

  /**
   * Generate rate limit key for IP-based limiting
   */
  static generateIPKey(ip: string, operation: string): string {
    return `ip:${ip}:${operation}`;
  }

  /**
   * Generate rate limit key for function-based limiting
   */
  static generateFunctionKey(functionName: string): string {
    return `function:${functionName}`;
  }

  /**
   * Clean up old rate limit records (maintenance operation)
   */
  async cleanupOldRecords(olderThanMs: number = 24 * 60 * 60 * 1000): Promise<number> {
    const cutoffTime = new Date(Date.now() - olderThanMs);
    
    try {
      const oldRecordsSnapshot = await this.db
        .collection(this.collectionName)
        .where('lastRequest', '<', admin.firestore.Timestamp.fromDate(cutoffTime))
        .get();

      if (oldRecordsSnapshot.empty) {
        return 0;
      }

      const batch = this.db.batch();
      
      oldRecordsSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();
      
      console.log(`Cleaned up ${oldRecordsSnapshot.size} old rate limit records`);
      return oldRecordsSnapshot.size;

    } catch (error) {
      console.error('Failed to cleanup old rate limit records:', error);
      return 0;
    }
  }
}

// Common rate limit configurations
export const RATE_LIMITS = {
  // OAuth operations - 10 requests per minute per user
  OAUTH: {
    windowMs: 60 * 1000,
    maxRequests: 10
  },
  
  // Program operations - 100 requests per hour per user
  PROGRAM_OPERATIONS: {
    windowMs: 60 * 60 * 1000,
    maxRequests: 100
  },
  
  // Daily checkups - 1000 requests per hour globally
  DAILY_CHECKUPS: {
    windowMs: 60 * 60 * 1000,
    maxRequests: 1000
  },
  
  // General API - 1000 requests per hour per user
  GENERAL_API: {
    windowMs: 60 * 60 * 1000,
    maxRequests: 1000
  }
};
