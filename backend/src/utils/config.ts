/**
 * Configuration service for Firebase Cloud Functions
 * Centralizes environment variable access and configuration management
 */

export interface CloudConfig {
  projectId: string;
  location: string;
  functionRegion: string;
}

export interface DatabaseConfig {
  collections: {
    programs: string;
    participants: string;
    submissions: string;
    githubOAuthTokens: string;
    schedulerLocks: string;
    users: string;
    notifications: string;
  };
}

export interface SchedulerConfig {
  defaultLocation: string;
  defaultTimeZone: string;
  retryAttempts: number;
  timeoutMs: number;
}

/**
 * Get cloud configuration from environment variables
 */
export function getCloudConfig(): CloudConfig {
  const projectId = process.env.GCLOUD_PROJECT || process.env.GCP_PROJECT;
  if (!projectId) {
    throw new Error('Project ID not found in environment variables');
  }

  return {
    projectId,
    location: process.env.FUNCTIONS_REGION || 'us-central1',
    functionRegion: 'us-central1'
  };
}

/**
 * Get database configuration
 */
export function getDatabaseConfig(): DatabaseConfig {
  return {
    collections: {
      programs: 'programs',
      participants: 'participants',
      submissions: 'submissions',
      githubOAuthTokens: 'github_oauth_tokens',
      schedulerLocks: 'scheduler_locks',
      users: 'users',
      notifications: 'notifications'
    }
  };
}

/**
 * Get scheduler configuration
 */
export function getSchedulerConfig(): SchedulerConfig {
  return {
    defaultLocation: 'us-central1',
    defaultTimeZone: 'UTC',
    retryAttempts: 3,
    timeoutMs: 30000
  };
}



/**
 * Get function URL for a given function name
 */
export function getFunctionUrl(functionName: string, config?: CloudConfig): string {
  const cloudConfig = config || getCloudConfig();
  return `https://${cloudConfig.location}-${cloudConfig.projectId}.cloudfunctions.net/${functionName}`;
}

/**
 * Validate required environment variables
 */
export function validateEnvironment(): void {
  const required = ['GCLOUD_PROJECT', 'GCP_PROJECT'];
  const missing = required.filter(env => !process.env[env]);
  
  if (missing.length > 0 && !process.env.GCLOUD_PROJECT && !process.env.GCP_PROJECT) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
