/**
 * Date and time utility functions
 * Centralizes date calculations and timezone operations
 */

/**
 * Calculate personal start date based on program start date and user timezone
 */
export function calculatePersonalStartDate(programStartDate: string, userTimezone: string): string {
  try {
    const startDate = new Date(programStartDate + 'T00:00:00');
    const userStartDate = new Date(startDate.toLocaleString('en-US', { timeZone: userTimezone }));
    return userStartDate.toISOString();
  } catch (error) {
    console.error('Error calculating personal start date:', error);
    return new Date(programStartDate + 'T00:00:00').toISOString();
  }
}

/**
 * Calculate personal end date based on start date and duration
 */
export function calculatePersonalEndDate(personalStartDate: string, durationDays: number): string {
  try {
    const startDate = new Date(personalStartDate);
    const endDate = new Date(startDate.getTime() + (durationDays * 24 * 60 * 60 * 1000));
    return endDate.toISOString();
  } catch (error) {
    console.error('Error calculating personal end date:', error);
    const fallbackStart = new Date(personalStartDate);
    fallbackStart.setDate(fallbackStart.getDate() + durationDays);
    return fallbackStart.toISOString();
  }
}

/**
 * Calculate current program status and day for a participant
 */
export function calculateCurrentStatus(
  personalStartDate: string,
  personalEndDate: string,
  userTimezone: string,
  currentTime: Date = new Date()
): {
  personalProgramStatus: 'upcoming' | 'ongoing' | 'ended';
  personalCurrentDay: number;
} {
  try {
    const startDate = new Date(personalStartDate);
    const endDate = new Date(personalEndDate);
    const userCurrentTime = new Date(currentTime.toLocaleString('en-US', { timeZone: userTimezone }));

    const daysSinceStart = Math.floor(
      (userCurrentTime.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    const personalCurrentDay = Math.max(1, daysSinceStart + 1);

    let personalProgramStatus: 'upcoming' | 'ongoing' | 'ended';
    if (userCurrentTime < startDate) {
      personalProgramStatus = 'upcoming';
    } else if (userCurrentTime >= endDate) {
      personalProgramStatus = 'ended';
    } else {
      personalProgramStatus = 'ongoing';
    }

    return { personalProgramStatus, personalCurrentDay };
  } catch (error) {
    console.error('Error calculating current status:', error);
    return { personalProgramStatus: 'upcoming', personalCurrentDay: 1 };
  }
}


