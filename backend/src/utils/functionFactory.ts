/**
 * Function Factory for creating standardized Cloud Functions
 * Reduces boilerplate and ensures consistent patterns
 */

import { https } from 'firebase-functions/v2';
import { ServiceContainer } from '../services/serviceContainer';
import { wrapProgramFunction, wrapParticipantFunction, wrapMaintenanceFunction } from './functionWrapper';
import { createSuccessResponse, createErrorResponse } from './errorHandler';

// Shared resource configurations
export const FUNCTION_RESOURCES = {
  STANDARD: {
    cpu: 0.5,
    memory: '256MiB' as const,
    maxInstances: 10,
  },
  MAINTENANCE: {
    cpu: 0.5,
    memory: '256MiB' as const,
    maxInstances: 5,
  },
  SCHEDULER_HTTP: {
    cpu: 0.5,
    memory: '256MiB' as const,
    maxInstances: 10,
    invoker: 'public' as const, // Allow unauthenticated access for Cloud Scheduler
  }
};

// Standard CORS headers for HTTPS functions
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

/**
 * Create a program-related function
 */
export function createProgramFunction<TData extends { programId: string } = { programId: string }, TResult = any>(
  name: string,
  handler: (data: TData, services: ServiceContainer) => Promise<TResult>,
  additionalRequiredParams?: string[]
) {
  const services = ServiceContainer.getInstance();

  const wrappedHandler = async (data: TData) => {
    return await handler(data, services);
  };

  return https.onCall(
    FUNCTION_RESOURCES.STANDARD,
    wrapProgramFunction(wrappedHandler, name, additionalRequiredParams)
  );
}

/**
 * Create a participant-related function
 */
export function createParticipantFunction<TData extends { programId: string; userId: string } = { programId: string; userId: string }, TResult = any>(
  name: string,
  handler: (data: TData, services: ServiceContainer) => Promise<TResult>,
  additionalRequiredParams?: string[]
) {
  const services = ServiceContainer.getInstance();

  const wrappedHandler = async (data: TData) => {
    return await handler(data, services);
  };

  return https.onCall(
    FUNCTION_RESOURCES.STANDARD,
    wrapParticipantFunction(wrappedHandler, name, additionalRequiredParams)
  );
}

/**
 * Create a maintenance-related function
 */
export function createMaintenanceFunction<TData = any, TResult = any>(
  name: string,
  handler: (data: TData, services: ServiceContainer) => Promise<TResult>,
  requiredParams?: string[]
) {
  const services = ServiceContainer.getInstance();

  const wrappedHandler = async (data: TData) => {
    return await handler(data, services);
  };

  return https.onCall(
    FUNCTION_RESOURCES.MAINTENANCE,
    wrapMaintenanceFunction(wrappedHandler, name, requiredParams)
  );
}

/**
 * Create an HTTPS endpoint for Cloud Scheduler
 * Handles CORS, method validation, authentication, and error handling
 */
export function createSchedulerHttpsFunction<TData = any, TResult = any>(
  name: string,
  handler: (data: TData, services: ServiceContainer) => Promise<TResult>,
  requiredParams: string[] = []
) {
  const services = ServiceContainer.getInstance();

  return https.onRequest(
    FUNCTION_RESOURCES.SCHEDULER_HTTP,
    async (req, res) => {
      try {
        // Set CORS headers
        Object.entries(CORS_HEADERS).forEach(([key, value]) => {
          res.set(key, value);
        });

        // Handle preflight requests
        if (req.method === 'OPTIONS') {
          res.status(204).send('');
          return;
        }

        // Only allow POST requests
        if (req.method !== 'POST') {
          res.status(405).json(createErrorResponse('Method not allowed'));
          return;
        }

        // Basic security check - ensure request comes from Google Cloud Scheduler
        const userAgent = req.get('User-Agent') || '';

        if (!userAgent.includes('Google-Cloud-Scheduler')) {
          res.status(403).json(createErrorResponse('Forbidden'));
          return;
        }

        // Extract and validate data from request body
        const { data } = req.body;
        if (!data) {
          res.status(400).json(createErrorResponse('Missing data in request body'));
          return;
        }

        // Validate required parameters
        const missing = requiredParams.filter(param => !data[param]);
        if (missing.length > 0) {
          res.status(400).json(createErrorResponse(
            `Missing required parameters: ${missing.join(', ')}`
          ));
          return;
        }

        // Execute the handler
        const result = await handler(data, services);

        // Send success response
        res.status(200).json(createSuccessResponse(result));

      } catch (error) {
        console.error(`${name} error:`, error);
        res.status(500).json(createErrorResponse(
          error instanceof Error ? error.message : 'Unknown error'
        ));
      }
    }
  );
}








