/**
 * Backend Timezone Utilities for Program Lifecycle Management
 * These functions help manage timezone-aware program start and end scheduling
 */

/**
 * Get timezone offset in minutes for a specific date
 * This accounts for daylight saving time changes
 */
export const getTimezoneOffsetForDate = (timezone: string, date: Date): number => {
  try {
    // Create a date formatter for the timezone
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    
    // Return offset in minutes (positive means ahead of UTC)
    return (tzDate.getTime() - utcDate.getTime()) / (1000 * 60);
  } catch (error) {
    console.error(`Error calculating timezone offset for ${timezone}:`, error);
    return 0;
  }
};

/**
 * Find the earliest timezone among a list of timezones for a specific date
 * Returns the timezone that experiences midnight first (most negative offset)
 */
export const findEarliestTimezone = (timezones: string[], referenceDate: Date): string => {
  if (timezones.length === 0) return 'UTC';
  if (timezones.length === 1) return timezones[0];

  let earliestTimezone = timezones[0];
  let earliestOffset = getTimezoneOffsetForDate(earliestTimezone, referenceDate);

  for (let i = 1; i < timezones.length; i++) {
    const currentOffset = getTimezoneOffsetForDate(timezones[i], referenceDate);
    // Earlier timezone has more negative offset (experiences midnight first)
    if (currentOffset < earliestOffset) {
      earliestOffset = currentOffset;
      earliestTimezone = timezones[i];
    }
  }

  return earliestTimezone;
};

/**
 * Find the latest timezone among a list of timezones for a specific date
 * Returns the timezone that experiences midnight last (most positive offset)
 */
export const findLatestTimezone = (timezones: string[], referenceDate: Date): string => {
  if (timezones.length === 0) return 'UTC';
  if (timezones.length === 1) return timezones[0];

  let latestTimezone = timezones[0];
  let latestOffset = getTimezoneOffsetForDate(latestTimezone, referenceDate);

  for (let i = 1; i < timezones.length; i++) {
    const currentOffset = getTimezoneOffsetForDate(timezones[i], referenceDate);
    // Later timezone has more positive offset (experiences midnight last)
    if (currentOffset > latestOffset) {
      latestOffset = currentOffset;
      latestTimezone = timezones[i];
    }
  }

  return latestTimezone;
};

/**
 * Calculate when midnight occurs in a specific timezone for a given date
 * Returns a UTC Date object representing midnight in the target timezone
 */
export const getMidnightInTimezone = (dateString: string, timezone: string): Date => {
  try {
    // Parse the date string (YYYY-MM-DD format)
    const [year, month, day] = dateString.split('-').map(Number);
    
    // Create a date object representing midnight in the target timezone
    // We use a temporary date to get the timezone offset
    const tempDate = new Date(year, month - 1, day, 0, 0, 0, 0);
    
    // Get the timezone offset for this date
    const offsetMinutes = getTimezoneOffsetForDate(timezone, tempDate);
    
    // Create UTC date representing midnight in the target timezone
    const utcMidnight = new Date(year, month - 1, day, 0, 0, 0, 0);
    utcMidnight.setMinutes(utcMidnight.getMinutes() - offsetMinutes);
    
    return utcMidnight;
  } catch (error) {
    console.error(`Error calculating midnight for ${timezone} on ${dateString}:`, error);
    // Fallback to UTC midnight
    const [year, month, day] = dateString.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  }
};

/**
 * Calculate program start time - Uses earliest participant timezone
 * Program status changes to 'ongoing' when first participant reaches midnight
 */
export const calculateProgramStartTime = (startDateString: string, participantTimezones: string[]): Date => {
  if (participantTimezones.length === 0) {
    // No participants, default to UTC
    const [year, month, day] = startDateString.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  }

  const referenceDate = new Date(startDateString + 'T00:00:00Z');
  const earliestTimezone = findEarliestTimezone(participantTimezones, referenceDate);

  return getMidnightInTimezone(startDateString, earliestTimezone);
};

/**
 * Calculate program end time for initial scheduling - ALWAYS in system timezone (UTC)
 * This is the first-stage end time, after which we check for ongoing participants
 */
export const calculateProgramInitialEndTime = (startDateString: string, duration: number): Date => {
  // Calculate end date
  const startDate = new Date(startDateString);
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + duration);
  const endDateString = endDate.toISOString().split('T')[0]; // YYYY-MM-DD format

  // Always use UTC for initial program end scheduling
  const [year, month, day] = endDateString.split('-').map(Number);
  return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
};

/**
 * Calculate program end time based on latest participant timezone
 * Used for rescheduling when participants are still ongoing after initial end time
 */
export const calculateProgramEndTime = (startDateString: string, duration: number, participantTimezones: string[]): Date => {
  // Calculate end date
  const startDate = new Date(startDateString);
  const endDate = new Date(startDate);
  endDate.setDate(endDate.getDate() + duration);
  const endDateString = endDate.toISOString().split('T')[0]; // YYYY-MM-DD format

  if (participantTimezones.length === 0) {
    // No participants, default to UTC
    const [year, month, day] = endDateString.split('-').map(Number);
    return new Date(Date.UTC(year, month - 1, day, 0, 0, 0, 0));
  }

  const referenceDate = new Date(endDateString + 'T00:00:00Z');
  const latestTimezone = findLatestTimezone(participantTimezones, referenceDate);

  return getMidnightInTimezone(endDateString, latestTimezone);
};

/**
 * Validate timezone string and check for DST transitions
 */
export const isValidTimezone = (timezone: string): boolean => {
  try {
    // Test if timezone is valid by trying to format a date with it
    new Date().toLocaleString('en-US', { timeZone: timezone });
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Check if a date falls within a DST transition period
 */
export const isDSTTransitionPeriod = (date: Date, timezone: string): boolean => {
  try {
    // Check the day before and after for offset changes
    const dayBefore = new Date(date.getTime() - 24 * 60 * 60 * 1000);
    const dayAfter = new Date(date.getTime() + 24 * 60 * 60 * 1000);

    const offsetBefore = getTimezoneOffsetForDate(timezone, dayBefore);
    const offsetCurrent = getTimezoneOffsetForDate(timezone, date);
    const offsetAfter = getTimezoneOffsetForDate(timezone, dayAfter);

    // If offsets are different, we're in a transition period
    return offsetBefore !== offsetCurrent || offsetCurrent !== offsetAfter;
  } catch (error) {
    console.error('Error checking DST transition:', error);
    return false;
  }
};

/**
 * Get safe midnight time accounting for DST transitions
 */
export const getSafeMidnightInTimezone = (dateString: string, timezone: string): Date => {
  const baseDate = getMidnightInTimezone(dateString, timezone);

  // Check if this is a DST transition period
  if (isDSTTransitionPeriod(baseDate, timezone)) {
    console.warn(`DST transition detected for ${dateString} in ${timezone}, using UTC+1 hour as safety buffer`);
    // Add 1 hour buffer to avoid DST issues
    return new Date(baseDate.getTime() + 60 * 60 * 1000);
  }

  return baseDate;
};

/**
 * Extract participant timezones from Firestore participant documents or processed participant objects
 * Now includes validation to filter out invalid timezones
 */
export const extractParticipantTimezones = (participantDocs: any[]): string[] => {
  return participantDocs
    .map(doc => {
      // Handle both Firestore document objects and processed participant objects
      if (typeof doc.data === 'function') {
        // Firestore document object
        return doc.data()?.timezone;
      } else {
        // Already processed participant object
        return doc.timezone;
      }
    })
    .filter((timezone): timezone is string =>
      typeof timezone === 'string' &&
      timezone.length > 0 &&
      isValidTimezone(timezone)
    );
};

/**
 * Create a cron expression for a specific UTC date and time
 * Returns a cron expression in the format: "minute hour day month *"
 */
export const createCronForUTCDateTime = (utcDate: Date): string => {
  const minute = utcDate.getUTCMinutes();
  const hour = utcDate.getUTCHours();
  const day = utcDate.getUTCDate();
  const month = utcDate.getUTCMonth() + 1; // JavaScript months are 0-based
  
  return `${minute} ${hour} ${day} ${month} *`;
};


