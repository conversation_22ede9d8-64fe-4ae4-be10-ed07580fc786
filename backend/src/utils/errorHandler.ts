/**
 * Common error handling utilities for Firebase Cloud Functions
 */

export interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
}

export interface SuccessResponse<T = any> {
  success: true;
  data?: T;
  message?: string;
}

export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

/**
 * Standard error types for the application
 */
export enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  RATE_LIMITED = 'RATE_LIMITED'
}

/**
 * Custom application error class
 */
export class AppError extends Error {
  constructor(
    message: string,
    public type: ErrorType = ErrorType.INTERNAL_ERROR,
    public details?: any
  ) {
    super(message);
    this.name = 'AppError';
  }
}

/**
 * Validation error for missing or invalid parameters
 */
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, ErrorType.VALIDATION_ERROR, details);
    this.name = 'ValidationError';
  }
}

/**
 * Not found error for missing resources
 */
export class NotFoundError extends AppError {
  constructor(resource: string, id?: string) {
    const message = id ? `${resource} with ID ${id} not found` : `${resource} not found`;
    super(message, ErrorType.NOT_FOUND);
    this.name = 'NotFoundError';
  }
}

/**
 * Wrapper for Cloud Function error handling
 */
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  functionName: string
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      console.error(`Error in ${functionName}:`, error);
      
      if (error instanceof AppError) {
        throw error;
      }
      
      // Convert unknown errors to AppError
      const message = error instanceof Error ? error.message : 'Unknown error occurred';
      throw new AppError(message, ErrorType.INTERNAL_ERROR, error);
    }
  };
}

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T>(data?: T, message?: string): SuccessResponse<T> {
  return {
    success: true,
    ...(data !== undefined && { data }),
    ...(message && { message })
  };
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  error: string | Error | AppError,
  code?: string,
  details?: any
): ErrorResponse {
  if (error instanceof AppError) {
    return {
      success: false,
      error: error.message,
      code: error.type,
      details: error.details
    };
  }
  
  if (error instanceof Error) {
    return {
      success: false,
      error: error.message,
      code: code || ErrorType.INTERNAL_ERROR,
      details
    };
  }
  
  return {
    success: false,
    error: typeof error === 'string' ? error : 'Unknown error',
    code: code || ErrorType.INTERNAL_ERROR,
    details
  };
}

/**
 * Validate required parameters
 */
export function validateRequired(params: Record<string, any>, required: string[]): void {
  const missing = required.filter(key => !params[key]);
  if (missing.length > 0) {
    throw new ValidationError(`Missing required parameters: ${missing.join(', ')}`);
  }
}

/**
 * Safe async operation with timeout
 */
export async function withTimeout<T>(
  operation: Promise<T>,
  timeoutMs: number,
  errorMessage = 'Operation timed out'
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new AppError(errorMessage, ErrorType.TIMEOUT_ERROR)), timeoutMs);
  });
  
  return Promise.race([operation, timeoutPromise]);
}
