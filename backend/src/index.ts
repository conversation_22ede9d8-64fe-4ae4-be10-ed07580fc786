import * as admin from 'firebase-admin';

// Initialize Firebase Admin with proper configuration
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      // Use default credentials in Cloud Functions environment
      // The service account will be configured via IAM roles
      projectId: process.env.GCLOUD_PROJECT || process.env.GCP_PROJECT || 'betonself',
    });

    // Configure Firestore settings to ignore undefined properties
    const firestore = admin.firestore();
    firestore.settings({
      ignoreUndefinedProperties: true
    });

    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Failed to initialize Firebase Admin:', error);
    throw error;
  }
}

// Import utilities
import { validateEnvironment } from './utils/config';
import { createProgramFunction, createParticipantFunction, createSchedulerHttpsFunction } from './utils/functionFactory';

// Validate environment on startup
validateEnvironment();

// Import and export GitHub OAuth functions
export { githubOAuthCallback, getGitHubToken } from './githubOAuth';

// Import and export FCM notification functions
export { registerFcmToken, unregisterFcmToken, sendPushNotification } from './fcm';

// Program lifecycle management functions
// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const programStarter = createSchedulerHttpsFunction(
  'programStarter',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().startProgram(data.programId);
  },
  ['programId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const programEnder = createSchedulerHttpsFunction(
  'programEnder',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().endProgramWithTimezoneCheck(data.programId);
  },
  ['programId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const individualDailyCheckup = createSchedulerHttpsFunction(
  'individualDailyCheckup',
  async (data: { programId: string; userId: string; [key: string]: any }, services) => {
    return await services.getProgramService().performIndividualCheckup(data.programId, data.userId, data);
  },
  ['programId', 'userId']
);

// HTTP version for Cloud Scheduler (using the name the scheduler expects)
export const dailyNotificationScheduler = createSchedulerHttpsFunction(
  'dailyNotificationScheduler',
  async (data: { programId: string; userId: string; [key: string]: any }, services) => {
    console.log(`🚀 dailyNotificationScheduler function called with data:`, JSON.stringify(data, null, 2));
    const result = await services.getNotificationService().sendDailyNotifications(data.programId, data.userId, data);
    console.log(`🏁 dailyNotificationScheduler function completed with result:`, JSON.stringify(result, null, 2));
    return result;
  },
  ['programId', 'userId']
);



// Program Scheduler Management Functions
// These functions handle two-stage program scheduling system

/**
 * Set up program start and initial end schedulers
 * Start: UTC timezone, End: Initial UTC with timezone check system
 */
export const setupProgramStartScheduler = createProgramFunction(
  'setupProgramStartScheduler',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().setupProgramStartScheduler(data.programId);
  }
);

/**
 * Set up initial program end scheduler (UTC-based, two-stage system)
 * Will check for ongoing participants and reschedule if needed
 */
export const setupProgramEndScheduler = createProgramFunction(
  'setupProgramEndScheduler',
  async (data: { programId: string }, services) => {
    return await services.getProgramService().setupProgramEndScheduler(data.programId);
  }
);

/**
 * Setup individual scheduling for a new participant enrollment
 */
export const setupIndividualSchedulingOnEnrollment = createParticipantFunction(
  'setupIndividualSchedulingOnEnrollment',
  async (data: { programId: string; userId: string }, services) => {
    return await services.getProgramService().setupIndividualScheduling(data.programId, data.userId);
  }
);


