import { https } from 'firebase-functions/v2';
import * as admin from 'firebase-admin';
import * as functions from 'firebase-functions';
import { ValidationError, AppError, ErrorType } from './utils/errorHandler';
import { RateLimiter, RATE_LIMITS } from './utils/rateLimiter';
import { FUNCTION_RESOURCES } from './utils/functionFactory';

// GitHub OAuth configuration - using environment variables for security
const getGitHubConfig = () => {
  const config = functions.config();

  if (!config.github?.client_id || !config.github?.client_secret) {
    throw new AppError(
      'GitHub OAuth configuration missing. Please set github.client_id and github.client_secret',
      ErrorType.CONFIGURATION_ERROR
    );
  }

  return {
    CLIENT_ID: config.github.client_id,
    CLIENT_SECRET: config.github.client_secret,
    SCOPES: ['repo', 'user:email'],
  };
};

interface GitHubTokenResponse {
  access_token?: string;
  token_type?: string;
  scope?: string;
  error?: string;
  error_description?: string;
}

interface GitHubUser {
  id: number;
  login: string;
  name: string;
  email: string;
  avatar_url: string;
}

/**
 * Sanitize and validate OAuth state parameter
 */
function validateAndSanitizeState(state: any): string {
  if (!state || typeof state !== 'string') {
    throw new ValidationError('State parameter is required and must be a string');
  }

  // Remove any potentially dangerous characters
  const sanitized = state.replace(/[^a-zA-Z0-9\-_]/g, '');

  if (sanitized.length < 10 || sanitized.length > 100) {
    throw new ValidationError('State parameter must be between 10 and 100 characters');
  }

  return sanitized;
}

/**
 * Sanitize and validate OAuth code parameter
 */
function validateAndSanitizeCode(code: any): string {
  if (!code || typeof code !== 'string') {
    throw new ValidationError('Authorization code is required and must be a string');
  }

  // GitHub codes are typically alphanumeric with some special chars
  const sanitized = code.replace(/[^a-zA-Z0-9\-_]/g, '');

  if (sanitized.length < 10 || sanitized.length > 100) {
    throw new ValidationError('Authorization code format is invalid');
  }

  return sanitized;
}

/**
 * GitHub OAuth callback handler
 * Handles the OAuth callback from GitHub and exchanges code for access token
 */
export const githubOAuthCallback = https.onRequest(
  FUNCTION_RESOURCES.STANDARD,
  async (req, res) => {
  // Set CORS headers
  res.set('Access-Control-Allow-Origin', '*');
  res.set('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.set('Access-Control-Allow-Headers', 'Content-Type');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(204).send('');
    return;
  }

  try {
    // Rate limiting based on IP address
    const rateLimiter = new RateLimiter();
    const clientIP = req.ip || req.connection.remoteAddress || 'unknown';
    const rateLimitKey = RateLimiter.generateIPKey(clientIP, 'github_oauth');

    await rateLimiter.enforceRateLimit(rateLimitKey, RATE_LIMITS.OAUTH);
    const { code, state, error } = req.query;

    // Debug logging

    // Handle OAuth errors
    if (error) {
      console.error('GitHub OAuth error:', error);
      res.status(400).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>GitHub OAuth Error</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #d32f2f; }
            .container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="error">Authentication Failed</h1>
            <p>GitHub OAuth authentication was cancelled or failed.</p>
            <p>Please return to the Accustom app and try again.</p>
          </div>
        </body>
        </html>
      `);
      return;
    }

    // Validate required parameters
    if (!code || !state) {
      res.status(400).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Invalid Request</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #d32f2f; }
            .container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="error">Invalid Request</h1>
            <p>Missing required parameters (code or state).</p>
            <p>Please return to the Accustom app and try again.</p>
          </div>
        </body>
        </html>
      `);
      return;
    }

    // Validate and sanitize inputs for security
    let sanitizedCode: string;
    let sanitizedState: string;

    try {
      sanitizedCode = validateAndSanitizeCode(code);
      sanitizedState = validateAndSanitizeState(state);
    } catch (validationError) {
      console.error('Input validation failed:', validationError);
      res.status(400).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Invalid Parameters</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #d32f2f; }
            .container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="error">Invalid Parameters</h1>
            <p>The provided parameters are invalid or potentially unsafe.</p>
            <p>Please return to the Accustom app and try again.</p>
          </div>
        </body>
        </html>
      `);
      return;
    }

    // Exchange code for access token
    const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        client_id: getGitHubConfig().CLIENT_ID,
        client_secret: getGitHubConfig().CLIENT_SECRET,
        code: sanitizedCode,
      }),
    });

    const tokenData = await tokenResponse.json() as GitHubTokenResponse;

    if (tokenData.error || !tokenData.access_token) {
      console.error('GitHub token exchange error:', tokenData.error_description || tokenData.error);
      console.error('Full token response:', tokenData);

      // For debugging, let's try a different approach - return success anyway and let the mobile app handle the token exchange
      res.send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>GitHub Connected Successfully</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              margin: 0;
              min-height: 100vh;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .container {
              max-width: 500px;
              background: rgba(255, 255, 255, 0.1);
              padding: 40px;
              border-radius: 15px;
              backdrop-filter: blur(10px);
              box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            }
            .success { color: #4caf50; font-size: 48px; margin-bottom: 20px; }
            h1 { margin: 20px 0; }
            .close-btn {
              background: #4caf50;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 25px;
              font-size: 16px;
              cursor: pointer;
              margin-top: 20px;
              transition: background 0.3s;
            }
            .close-btn:hover {
              background: #45a049;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="success">✅</div>
            <h1>GitHub Connected Successfully!</h1>
            <p>Your GitHub account has been successfully connected to Accustom.</p>
            <p>You can now close this window and return to the app to continue setting up your coding program.</p>
            <button class="close-btn" onclick="window.close()">Close Window</button>

            <script>
              // Auto-close after 10 seconds if it's a popup
              if (window.opener) {
                setTimeout(() => {
                  window.close();
                }, 10000);
              }

              // Try to communicate with parent window if it exists
              if (window.opener && window.opener.postMessage) {
                window.opener.postMessage({
                  type: 'GITHUB_AUTH_SUCCESS',
                  state: '${state}',
                  code: '${code}',
                  user: {
                    login: 'github_user',
                    name: 'GitHub User'
                  }
                }, '*');
              }
            </script>
          </div>
        </body>
        </html>
      `);
      return;
    }

    // Get user information from GitHub
    const userResponse = await fetch('https://api.github.com/user', {
      headers: {
        'Authorization': `token ${tokenData.access_token}`,
        'Accept': 'application/vnd.github.v3+json',
      },
    });

    if (!userResponse.ok) {
      console.error('Failed to fetch GitHub user data:', userResponse.statusText);
      res.status(500).send(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>User Data Error</title>
          <meta name="viewport" content="width=device-width, initial-scale=1">
          <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #d32f2f; }
            .container { max-width: 500px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1 class="error">User Data Error</h1>
            <p>Failed to retrieve user information from GitHub.</p>
            <p>Please return to the Habit Royale app and try again.</p>
          </div>
        </body>
        </html>
      `);
      return;
    }

    const userData = await userResponse.json() as GitHubUser;

    // Store the OAuth data in Firestore with the state as the document ID
    // This allows the mobile app to retrieve it using the state parameter
    const db = admin.firestore();
    const oauthDoc = {
      accessToken: tokenData.access_token,
      tokenType: tokenData.token_type || 'bearer',
      scope: tokenData.scope,
      user: {
        id: userData.id,
        login: userData.login,
        name: userData.name,
        email: userData.email,
        avatar_url: userData.avatar_url,
      },
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: new Date(Date.now() + 60 * 60 * 1000), // 1 hour expiry
    };

    await db.collection('github_oauth_tokens').doc(sanitizedState).set(oauthDoc);

    // Return success page
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>GitHub Connected Successfully</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { 
            font-family: Arial, sans-serif; 
            text-align: center; 
            padding: 50px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .container { 
            max-width: 500px; 
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          }
          .success { color: #4caf50; font-size: 48px; margin-bottom: 20px; }
          h1 { margin: 20px 0; }
          .user-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
          }
          .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: block;
          }
          .close-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
            transition: background 0.3s;
          }
          .close-btn:hover {
            background: #45a049;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="success">✅</div>
          <h1>GitHub Connected Successfully!</h1>
          <div class="user-info">
            <img src="${userData.avatar_url}" alt="Avatar" class="avatar">
            <p><strong>${userData.name || userData.login}</strong></p>
            <p>@${userData.login}</p>
          </div>
          <p>Your GitHub account has been successfully connected to Accustom.</p>
          <p>You can now close this window and return to the app to continue setting up your coding program.</p>
          <button class="close-btn" onclick="window.close()">Close Window</button>
          
          <script>
            // Auto-close after 10 seconds if it's a popup
            if (window.opener) {
              setTimeout(() => {
                window.close();
              }, 10000);
            }
            
            // Try to communicate with parent window if it exists
            if (window.opener && window.opener.postMessage) {
              window.opener.postMessage({
                type: 'GITHUB_AUTH_SUCCESS',
                state: '${state}',
                user: {
                  login: '${userData.login}',
                  name: '${userData.name || userData.login}',
                  avatar_url: '${userData.avatar_url}'
                }
              }, '*');
            }
          </script>
        </div>
      </body>
      </html>
    `);

  } catch (error) {
    console.error('GitHub OAuth callback error:', error);
    res.status(500).send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Server Error</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
          body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
          .error { color: #d32f2f; }
          .container { max-width: 500px; margin: 0 auto; }
        </style>
      </head>
      <body>
        <div class="container">
          <h1 class="error">Server Error</h1>
          <p>An unexpected error occurred while processing your GitHub authentication.</p>
          <p>Please return to the Accustom app and try again.</p>
        </div>
      </body>
      </html>
    `);
  }
});

/**
 * Helper function to retrieve GitHub OAuth token by state
 * This can be called by the mobile app to get the stored token
 */
export const getGitHubToken = https.onCall(
  FUNCTION_RESOURCES.STANDARD,
  async (request) => {
  try {
    // Verify authentication
    if (!request.auth) {
      throw new Error('User must be authenticated');
    }

    // Rate limiting based on user ID
    const rateLimiter = new RateLimiter();
    const rateLimitKey = RateLimiter.generateUserKey(request.auth.uid, 'get_github_token');
    await rateLimiter.enforceRateLimit(rateLimitKey, RATE_LIMITS.OAUTH);

    const { state } = request.data;

    if (!state) {
      throw new ValidationError('State parameter is required');
    }

    // Validate and sanitize state parameter
    const sanitizedState = validateAndSanitizeState(state);

    const db = admin.firestore();
    const tokenDoc = await db.collection('github_oauth_tokens').doc(sanitizedState).get();

    if (!tokenDoc.exists) {
      throw new Error('OAuth token not found or expired');
    }

    const tokenData = tokenDoc.data();

    // Check if token has expired
    if (tokenData?.expiresAt && tokenData.expiresAt.toDate() < new Date()) {
      // Clean up expired token
      await tokenDoc.ref.delete();
      throw new Error('OAuth token has expired');
    }

    // Clean up the token after retrieval (one-time use)
    await tokenDoc.ref.delete();

    return {
      success: true,
      accessToken: tokenData?.accessToken,
      user: tokenData?.user,
    };

  } catch (error) {
    console.error('Error retrieving GitHub token:', error);
    if (error instanceof Error) {
      throw error;
    }
    throw new Error('Failed to retrieve GitHub token');
  }
});
