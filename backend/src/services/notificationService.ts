/**
 * Backend Notification Service for Cloud Functions
 * Handles daily notification delivery and scheduling
 */

import { DatabaseService } from './databaseService';
import * as admin from 'firebase-admin';

export interface DailyNotificationResult {
  success: boolean;
  message: string;
  programId: string;
  userId: string;
  participantName?: string;
  action: 'notification_sent' | 'program_not_started' | 'program_ended' | 'no_notification_needed' | 'error';
  notificationsSent?: number;
  error?: string;
}

export class NotificationService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = new DatabaseService();
  }

  /**
   * Send daily notifications for a participant
   * Called by Cloud Scheduler at specified times
   */
  async sendDailyNotifications(
    programId: string,
    userId: string,
    schedulerData?: any
  ): Promise<DailyNotificationResult> {
    console.log(`🔔 NOTIFICATION SCHEDULER STARTED`);
    console.log(`📋 Program ID: ${programId}`);
    console.log(`👤 User ID: ${userId}`);
    console.log(`📊 Scheduler Data:`, JSON.stringify(schedulerData, null, 2));

    try {
      // Get participant details
      console.log(`🔍 Fetching participant data for ${userId} in program ${programId}`);
      const participant = await this.dbService.getParticipant(programId, userId);
      console.log(`✅ Participant found:`, {
        id: participant.id,
        fname: participant.fname,
        personalProgramStatus: participant.personalProgramStatus,
        personalStartDate: participant.personalStartDate,
        personalEndDate: participant.personalEndDate,
        timezone: participant.timezone
      });

      // Check if participant has individual program status fields
      if (!participant.personalProgramStatus) {
        console.log(`❌ Participant missing individual program status fields`);
        return {
          success: false,
          message: 'Participant missing individual program status fields',
          programId,
          userId,
          action: 'error',
          error: 'Missing individual status'
        };
      }

      // Handle recurring notification logic
      if (schedulerData?.isRecurring) {
        console.log(`🔄 Processing recurring notification logic`);
        const result = await this.handleRecurringNotification(participant, programId, schedulerData);
        if (result) {
          console.log(`✅ Recurring notification result:`, result);
          return result;
        }
      }

      // Default notification logic for non-recurring
      console.log(`📤 Processing default notification logic`);
      const result = await this.sendParticipantNotifications(participant, programId);
      console.log(`✅ Default notification result:`, result);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error in daily notification delivery for user ${userId}:`, errorMessage);
      console.error(`📋 Error stack:`, error);

      return {
        success: false,
        message: `Daily notification delivery failed: ${errorMessage}`,
        programId,
        userId,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Handle recurring notification logic - determines if notifications should be sent today
   */
  private async handleRecurringNotification(
    participant: any,
    programId: string,
    schedulerData: any
  ): Promise<DailyNotificationResult | null> {
    const now = new Date();
    const personalStartDate = new Date(schedulerData.personalStartDate);
    const personalEndDate = new Date(schedulerData.personalEndDate);

    console.log(`⏰ Time comparison:`, {
      now: now.toISOString(),
      personalStartDate: personalStartDate.toISOString(),
      personalEndDate: personalEndDate.toISOString(),
      timezone: participant.timezone,
      nowInUserTz: now.toLocaleString("en-US", { timeZone: participant.timezone }),
      startDateInUserTz: personalStartDate.toLocaleString("en-US", { timeZone: participant.timezone })
    });

    // Check if we're within the program duration for this user
    // Use user's timezone for date comparison
    const userTimezone = participant.timezone || 'UTC';

    // Get current date in user's timezone
    const nowInUserTz = new Date(now.toLocaleString("en-US", { timeZone: userTimezone }));
    const nowDateInUserTz = new Date(nowInUserTz.getFullYear(), nowInUserTz.getMonth(), nowInUserTz.getDate());

    // Get program start/end dates in user's timezone
    const startDateInUserTz = new Date(personalStartDate.toLocaleString("en-US", { timeZone: userTimezone }));
    const startDateOnlyInUserTz = new Date(startDateInUserTz.getFullYear(), startDateInUserTz.getMonth(), startDateInUserTz.getDate());

    const endDateInUserTz = new Date(personalEndDate.toLocaleString("en-US", { timeZone: userTimezone }));
    const endDateOnlyInUserTz = new Date(endDateInUserTz.getFullYear(), endDateInUserTz.getMonth(), endDateInUserTz.getDate());

    console.log(`📅 Date comparison in user timezone (${userTimezone}):`, {
      nowDateInUserTz: nowDateInUserTz.toISOString(),
      startDateOnlyInUserTz: startDateOnlyInUserTz.toISOString(),
      endDateOnlyInUserTz: endDateOnlyInUserTz.toISOString(),
      isBeforeStart: nowDateInUserTz < startDateOnlyInUserTz,
      isAfterEnd: nowDateInUserTz > endDateOnlyInUserTz,
      userTimezone: userTimezone
    });

    if (nowDateInUserTz < startDateOnlyInUserTz) {
      console.log(`❌ Program not started yet (date comparison in user timezone)`);
      return {
        success: true,
        message: 'Program not started yet for user (date check in user timezone)',
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'program_not_started'
      };
    }

    if (nowDateInUserTz > endDateOnlyInUserTz) {
      console.log(`❌ Program ended (date comparison in user timezone)`);
      return {
        success: true,
        message: 'Program ended for user',
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'program_ended'
      };
    }

    console.log(`✅ Date check passed - program should be active today`);

    // Check if user's program is ongoing
    console.log(`🔍 Checking participant status: ${participant.personalProgramStatus}`);
    if (participant.personalProgramStatus !== 'ongoing') {
      console.log(`❌ User program status is not ongoing: ${participant.personalProgramStatus}`);
      console.log(`⚠️  NOTE: You need to change the participant's personalProgramStatus to 'ongoing' in Firestore`);
      return {
        success: true,
        message: `User program status is ${participant.personalProgramStatus}, no notifications needed. Change participant status to 'ongoing' to receive notifications.`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'no_notification_needed'
      };
    }

    // Send notifications for ongoing program
    console.log(`✅ Program is ongoing, proceeding to send notifications`);
    return await this.sendParticipantNotifications(participant, programId);
  }

  /**
   * Send appropriate notifications to a participant
   */
  private async sendParticipantNotifications(
    participant: any,
    programId: string
  ): Promise<DailyNotificationResult> {
    let notificationsSent = 0;

    try {
      // Get program details
      const program = await this.dbService.getProgram(programId);
      
      // Calculate current day for the participant
      const personalStartDate = new Date(participant.personalStartDate);
      const now = new Date();
      const daysSinceStart = Math.floor((now.getTime() - personalStartDate.getTime()) / (1000 * 60 * 60 * 24));
      const currentDay = daysSinceStart + 1;

      // Check if participant has submitted today
      const hasSubmittedToday = await this.checkTodaySubmission(programId, participant.id, currentDay);

      if (!hasSubmittedToday) {
        // Send reminder notification
        await this.sendReminderNotification(participant.id, program.name, programId, currentDay);
        notificationsSent++;

        // If it's close to deadline, send urgent notification
        const hoursUntilDeadline = this.calculateHoursUntilDeadline(participant.timezone);
        if (hoursUntilDeadline <= 2 && hoursUntilDeadline > 0) {
          await this.sendUrgentReminderNotification(participant.id, program.name, programId, hoursUntilDeadline);
          notificationsSent++;
        }
      }

      return {
        success: true,
        message: `Daily notifications processed for ${participant.fname}`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'notification_sent',
        notificationsSent
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`Error sending notifications to participant ${participant.id}:`, errorMessage);
      
      return {
        success: false,
        message: `Failed to send notifications: ${errorMessage}`,
        programId,
        userId: participant.id,
        participantName: participant.fname,
        action: 'error',
        error: errorMessage
      };
    }
  }

  /**
   * Check if participant has submitted for today
   */
  private async checkTodaySubmission(programId: string, userId: string, dayNumber: number): Promise<boolean> {
    try {
      const submission = await this.dbService.getParticipantSubmission(programId, userId, dayNumber);
      return submission !== null && submission.status === 'submitted';
    } catch (error) {
      console.error(`Error checking today's submission for user ${userId}:`, error);
      return false; // Assume not submitted if we can't check
    }
  }

  /**
   * Send reminder notification using FCM
   */
  private async sendReminderNotification(
    userId: string,
    programName: string,
    programId: string,
    dayNumber: number
  ): Promise<void> {
    const notificationData = {
      title: "Daily Progress Reminder",
      message: `Don't forget to submit your progress for "${programName}" today! Day ${dayNumber}`,
      type: "reminder",
      priority: "medium",
      data: {
        action: "submit_progress",
        programId: programId,
        programName: programName,
        dayNumber: dayNumber.toString()
      }
    };

    // Use the existing FCM system to send push notifications
    await this.sendFCMNotification(userId, notificationData);
    console.log(`Reminder notification sent to user ${userId} for program ${programId}`);
  }

  /**
   * Send urgent reminder notification using FCM
   */
  private async sendUrgentReminderNotification(
    userId: string,
    programName: string,
    programId: string,
    hoursLeft: number
  ): Promise<void> {
    const notificationData = {
      title: "🚨 Urgent: Deadline Approaching!",
      message: `Only ${hoursLeft} hours left to submit your progress for "${programName}". Don't lose your streak!`,
      type: "reminder",
      priority: "high",
      data: {
        action: "submit_progress",
        programId: programId,
        programName: programName,
        urgency: "high",
        hoursLeft: hoursLeft.toString()
      }
    };

    // Use the existing FCM system to send push notifications
    await this.sendFCMNotification(userId, notificationData);
    console.log(`Urgent reminder notification sent to user ${userId} for program ${programId}`);
  }

  /**
   * Send FCM notification using the existing FCM system
   */
  private async sendFCMNotification(userId: string, notificationData: any): Promise<void> {
    try {
      // Get FCM tokens for the user
      const tokensSnapshot = await admin.firestore()
        .collection('users')
        .doc(userId)
        .collection('fcmTokens')
        .get();

      if (tokensSnapshot.empty) {
        console.log(`No FCM tokens found for user ${userId}`);
        return;
      }

      // Extract tokens
      const tokens = tokensSnapshot.docs.map(doc => doc.data().token);

      // Separate Expo tokens from FCM tokens
      const expoTokens = tokens.filter(token => token.startsWith('ExponentPushToken['));
      const fcmTokens = tokens.filter(token => !token.startsWith('ExponentPushToken['));

      // Send to Expo tokens using Expo's push service
      if (expoTokens.length > 0) {
        await this.sendExpoNotifications(expoTokens, notificationData);
      }

      // Send to FCM tokens using Firebase messaging
      if (fcmTokens.length > 0) {
        await this.sendFCMMessages(fcmTokens, notificationData);
      }

    } catch (error) {
      console.error('Error sending FCM notification:', error);
      throw error;
    }
  }

  /**
   * Send notifications to Expo tokens
   */
  private async sendExpoNotifications(tokens: string[], notification: any): Promise<void> {
    try {
      const messages = tokens.map(token => ({
        to: token,
        title: notification.title,
        body: notification.message,
        data: notification.data,
        sound: 'default',
        badge: 1,
      }));

      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messages),
      });

      if (!response.ok) {
        throw new Error(`Expo push service error: ${response.status}`);
      }

      console.log(`Successfully sent Expo notifications to ${tokens.length} devices`);
    } catch (error) {
      console.error('Error sending Expo notifications:', error);
      throw error;
    }
  }

  /**
   * Send notifications to FCM tokens
   */
  private async sendFCMMessages(tokens: string[], notification: any): Promise<void> {
    try {
      const fcmMessage: admin.messaging.MulticastMessage = {
        tokens,
        notification: {
          title: notification.title,
          body: notification.message,
        },
        data: notification.data,
        android: {
          notification: {
            channelId: 'accustom-reminder',
            priority: 'high' as const,
          },
        },
        apns: {
          payload: {
            aps: {
              alert: {
                title: notification.title,
                body: notification.message,
              },
              badge: 1,
              sound: 'default',
            },
          },
        },
      };

      const response = await admin.messaging().sendMulticast(fcmMessage);
      console.log(`Successfully sent FCM notifications to ${response.successCount} devices`);

      if (response.failureCount > 0) {
        console.error(`Failed to send FCM notifications to ${response.failureCount} devices`);
      }
    } catch (error) {
      console.error('Error sending FCM messages:', error);
      throw error;
    }
  }

  /**
   * Calculate hours until midnight deadline in user's timezone
   */
  private calculateHoursUntilDeadline(timezone: string): number {
    const now = new Date();
    const userNow = new Date(now.toLocaleString("en-US", { timeZone: timezone }));

    // Calculate midnight in user's timezone
    const midnight = new Date(userNow);
    midnight.setHours(24, 0, 0, 0); // Next midnight

    const hoursUntilDeadline = (midnight.getTime() - userNow.getTime()) / (1000 * 60 * 60);
    return Math.max(0, hoursUntilDeadline);
  }
}
