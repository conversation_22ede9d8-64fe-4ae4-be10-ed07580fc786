/**
 * Validation service for common validation patterns
 * Centralizes parameter validation and business logic validation
 */

import { ValidationError } from '../utils/errorHandler';
import { ProgramData, ParticipantData } from './databaseService';

export class ValidationService {
  /**
   * Validate program status for specific operations
   */
  static validateProgramStatus(
    program: ProgramData,
    expectedStatus: string | string[],
    operation: string
  ): void {
    const expected = Array.isArray(expectedStatus) ? expectedStatus : [expectedStatus];
    
    if (!expected.includes(program.status)) {
      throw new ValidationError(
        `Cannot ${operation}: Program status is '${program.status}', expected ${expected.join(' or ')}`
      );
    }
  }

  /**
   * Validate program can be started
   */
  static validateProgramCanStart(program: ProgramData): void {
    this.validateProgramStatus(program, 'upcoming', 'start program');
  }

  /**
   * Validate program can be ended
   */
  static validateProgramCanEnd(program: ProgramData): void {
    // Allow ending from ongoing status, but warn for other statuses
    if (program.status !== 'ongoing') {
      console.warn(`Program status is '${program.status}', expected 'ongoing'. Proceeding anyway.`);
    }
  }

  /**
   * Validate participant has required individual status fields
   */
  static validateParticipantIndividualStatus(participant: ParticipantData): void {
    const requiredFields = ['personalStartDate', 'personalEndDate', 'timezone'];
    const missing = requiredFields.filter(field => !participant[field]);
    
    if (missing.length > 0) {
      throw new ValidationError(
        `Participant missing required individual status fields: ${missing.join(', ')}`
      );
    }
  }

  /**
   * Validate participant is not already disqualified
   */
  static validateParticipantNotDisqualified(participant: ParticipantData): void {
    if (participant.disqualified === true) {
      throw new ValidationError(`Participant ${participant.id} is already disqualified`);
    }
  }

  /**
   * Validate timezone string
   */
  static validateTimezone(timezone: string): void {
    try {
      new Date().toLocaleString('en-US', { timeZone: timezone });
    } catch (error) {
      throw new ValidationError(`Invalid timezone: ${timezone}`);
    }
  }



  /**
   * Validate batch size for operations
   */
  static validateBatchSize(batchSize: number, maxSize = 100): void {
    if (!Number.isInteger(batchSize) || batchSize < 1 || batchSize > maxSize) {
      throw new ValidationError(`Batch size must be between 1 and ${maxSize}`);
    }
  }

  /**
   * Validate array of program IDs
   */
  static validateProgramIds(programIds: any): string[] {
    if (!Array.isArray(programIds)) {
      throw new ValidationError('Program IDs must be an array');
    }
    
    if (programIds.length === 0) {
      throw new ValidationError('Program IDs array cannot be empty');
    }
    
    const invalidIds = programIds.filter(id => typeof id !== 'string' || !id.trim());
    if (invalidIds.length > 0) {
      throw new ValidationError('All program IDs must be non-empty strings');
    }
    
    return programIds;
  }


}
