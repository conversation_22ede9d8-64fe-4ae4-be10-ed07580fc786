/**
 * Migration service for handling data migrations and cleanup operations
 */

import { DatabaseService, ProgramData, ParticipantData } from './databaseService';
import { ValidationService } from './validationService';
import { calculatePersonalStartDate, calculatePersonalEndDate, calculateCurrentStatus } from '../utils/dateUtils';

export interface MigrationResult {
  success: boolean;
  message: string;
  programId?: string;
  participantsMigrated?: number;
  totalParticipants?: number;
  errors?: string[];
}

export interface CleanupResult {
  success: boolean;
  message: string;
  locksRemoved?: number;
  affectedPrograms?: string[];
  deleted?: string[];
  notFound?: string[];
  errors?: string[];
}

export class MigrationService {
  private dbService: DatabaseService;

  constructor() {
    this.dbService = new DatabaseService();
  }

  /**
   * Migrate participants to individual status tracking
   */
  async migrateToIndividualStatus(programId: string, batchSize: number = 10): Promise<MigrationResult> {
    console.log(`Starting migration to individual status for program ${programId}`);

    // Validate inputs
    ValidationService.validateBatchSize(batchSize);

    // Get program details
    const program = await this.dbService.getProgram(programId);

    // Get all participants
    const participants = await this.dbService.getProgramParticipants(programId);

    if (participants.length === 0) {
      return {
        success: true,
        message: 'No participants to migrate',
        programId,
        participantsMigrated: 0,
        errors: []
      };
    }

    let participantsMigrated = 0;
    const errors: string[] = [];

    // Process participants in batches
    for (let i = 0; i < participants.length; i += batchSize) {
      const batch = participants.slice(i, i + batchSize);

      for (const participant of batch) {
        try {
          // Check if participant already has individual status fields
          if (participant.personalProgramStatus) {
            console.log(`Participant ${participant.id} already migrated, skipping`);
            continue;
          }

          // Calculate personal dates and status
          const migrationData = this.calculateMigrationData(participant, program);

          // Update participant with individual status fields
          await this.dbService.updateParticipant(participant.id, participant.id, migrationData);

          participantsMigrated++;
          console.log(`Migrated participant ${participant.id} to individual status`);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push(`Error migrating participant ${participant.id}: ${errorMessage}`);
          console.error(`Error migrating participant ${participant.id}:`, error);
        }
      }

      // Small delay between batches
      if (i + batchSize < participants.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return {
      success: errors.length === 0,
      message: `Migrated ${participantsMigrated} participants${errors.length > 0 ? ` with ${errors.length} errors` : ''}`,
      programId,
      participantsMigrated,
      totalParticipants: participants.length,
      errors
    };
  }



  /**
   * Validate program data integrity
   */
  async validateProgramIntegrity(programId: string): Promise<{
    success: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    try {
      // Check program exists and has valid data
      const program = await this.dbService.getProgram(programId);
      
      // Validate program fields
      if (!program.startDate) {
        issues.push('Program missing start date');
      }
      
      if (!program.duration || program.duration <= 0) {
        issues.push('Program has invalid duration');
      }

      // Check participants
      const participants = await this.dbService.getProgramParticipants(programId);
      
      if (participants.length === 0) {
        issues.push('Program has no participants');
      }

      // Check participant data integrity
      let participantsWithoutIndividualStatus = 0;
      let participantsWithInvalidTimezone = 0;

      for (const participant of participants) {
        if (!participant.personalProgramStatus) {
          participantsWithoutIndividualStatus++;
        }

        if (participant.timezone) {
          try {
            ValidationService.validateTimezone(participant.timezone);
          } catch {
            participantsWithInvalidTimezone++;
          }
        }
      }

      if (participantsWithoutIndividualStatus > 0) {
        issues.push(`${participantsWithoutIndividualStatus} participants missing individual status fields`);
        recommendations.push('Run migration to individual status');
      }

      if (participantsWithInvalidTimezone > 0) {
        issues.push(`${participantsWithInvalidTimezone} participants have invalid timezones`);
        recommendations.push('Update participant timezones');
      }

    } catch (error) {
      issues.push(`Error validating program: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    return {
      success: issues.length === 0,
      issues,
      recommendations
    };
  }

  /**
   * Private helper methods
   */

  private calculateMigrationData(participant: ParticipantData, program: ProgramData): Partial<ParticipantData> {
    // Calculate personal dates based on participant's timezone
    const userTimezone = participant.timezone || 'UTC';
    const personalStartDate = calculatePersonalStartDate(program.startDate, userTimezone);
    const personalEndDate = calculatePersonalEndDate(personalStartDate, program.duration);

    // Calculate current status and day
    const currentTime = new Date();
    const { personalProgramStatus, personalCurrentDay } = calculateCurrentStatus(
      personalStartDate,
      personalEndDate,
      userTimezone,
      currentTime
    );

    return {
      personalProgramStatus,
      personalStartDate,
      personalEndDate,
      personalCurrentDay,
      lastDayCheckup: currentTime.toISOString(),
    };
  }

  /**
   * Get migration statistics for a program
   */
  async getMigrationStats(programId: string): Promise<{
    totalParticipants: number;
    migratedParticipants: number;
    pendingMigration: number;
    migrationProgress: number;
  }> {
    const participants = await this.dbService.getProgramParticipants(programId);
    const totalParticipants = participants.length;
    const migratedParticipants = participants.filter(p => p.personalProgramStatus).length;
    const pendingMigration = totalParticipants - migratedParticipants;
    const migrationProgress = totalParticipants > 0 ? (migratedParticipants / totalParticipants) * 100 : 0;

    return {
      totalParticipants,
      migratedParticipants,
      pendingMigration,
      migrationProgress
    };
  }

  /**
   * Rollback migration for a program (if needed)
   */
  async rollbackMigration(programId: string): Promise<MigrationResult> {
    console.log(`Rolling back migration for program ${programId}`);

    const participants = await this.dbService.getProgramParticipants(programId);
    let participantsRolledBack = 0;
    const errors: string[] = [];

    for (const participant of participants) {
      try {
        if (participant.personalProgramStatus) {
          // Remove individual status fields
          const updateData = {
            personalProgramStatus: undefined,
            personalStartDate: undefined,
            personalEndDate: undefined,
            personalCurrentDay: undefined,
            lastDayCheckup: undefined
          };

          await this.dbService.updateParticipant(programId, participant.id, updateData);
          participantsRolledBack++;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        errors.push(`Error rolling back participant ${participant.id}: ${errorMessage}`);
      }
    }

    return {
      success: errors.length === 0,
      message: `Rolled back ${participantsRolledBack} participants${errors.length > 0 ? ` with ${errors.length} errors` : ''}`,
      programId,
      participantsMigrated: participantsRolledBack,
      totalParticipants: participants.length,
      errors
    };
  }
}
