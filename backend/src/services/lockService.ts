/**
 * Distributed Lock Service for preventing race conditions
 * Uses Firestore for distributed locking across cloud function instances
 */

import * as admin from 'firebase-admin';
import { getDatabaseConfig } from '../utils/config';
import { AppError, ErrorType } from '../utils/errorHandler';

export interface LockData {
  lockId: string;
  ownerId: string;
  operation: string;
  timestamp: admin.firestore.Timestamp;
  expiresAt: admin.firestore.Timestamp;
  metadata?: Record<string, any>;
}

export class LockService {
  private db: admin.firestore.Firestore;
  private config = getDatabaseConfig();
  private instanceId: string;

  constructor() {
    this.db = admin.firestore();
    // Generate unique instance ID for this function execution
    this.instanceId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Acquire a distributed lock with automatic expiry
   */
  async acquireLock(
    lockId: string,
    operation: string,
    timeoutMs: number = 300000, // 5 minutes default
    metadata?: Record<string, any>
  ): Promise<{ acquired: boolean; lockData?: LockData }> {
    const lockRef = this.db.collection(this.config.collections.schedulerLocks).doc(lockId);
    const expiresAt = new Date(Date.now() + timeoutMs);

    // Build lock data, only including metadata if it's provided and not undefined
    const lockData: LockData = {
      lockId,
      ownerId: this.instanceId,
      operation,
      timestamp: admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
      expiresAt: admin.firestore.Timestamp.fromDate(expiresAt),
      ...(metadata && Object.keys(metadata).length > 0 && { metadata })
    };

    try {
      // Use transaction to ensure atomic lock acquisition
      const result = await this.db.runTransaction(async (transaction) => {
        const lockDoc = await transaction.get(lockRef);
        
        if (lockDoc.exists) {
          const existingLock = lockDoc.data() as LockData;
          
          // Check if lock has expired
          if (existingLock.expiresAt.toDate() > new Date()) {
            // Lock is still valid and held by another instance
            return { acquired: false, existingLock };
          }
          
          // Lock has expired, we can take it
          console.log(`Taking over expired lock ${lockId} from ${existingLock.ownerId}`);
        }
        
        // Acquire the lock
        transaction.set(lockRef, lockData);
        return { acquired: true };
      });

      if (result.acquired) {
        console.log(`Lock acquired: ${lockId} by ${this.instanceId} for operation: ${operation}`);
        return { acquired: true, lockData };
      } else {
        console.log(`Lock ${lockId} is held by another instance: ${result.existingLock?.ownerId}`);
        return { acquired: false };
      }

    } catch (error) {
      console.error(`Failed to acquire lock ${lockId}:`, error);
      throw new AppError(`Failed to acquire lock: ${error}`, ErrorType.INTERNAL_ERROR);
    }
  }

  /**
   * Release a lock if owned by this instance
   */
  async releaseLock(lockId: string): Promise<{ released: boolean; reason?: string }> {
    const lockRef = this.db.collection(this.config.collections.schedulerLocks).doc(lockId);

    try {
      const result = await this.db.runTransaction(async (transaction) => {
        const lockDoc = await transaction.get(lockRef);
        
        if (!lockDoc.exists) {
          return { released: false, reason: 'lock_not_found' };
        }
        
        const lockData = lockDoc.data() as LockData;
        
        if (lockData.ownerId !== this.instanceId) {
          return { released: false, reason: 'not_owner' };
        }
        
        // Delete the lock
        transaction.delete(lockRef);
        return { released: true };
      });

      if (result.released) {
        console.log(`Lock released: ${lockId} by ${this.instanceId}`);
      } else {
        console.log(`Failed to release lock ${lockId}: ${result.reason}`);
      }

      return result;

    } catch (error) {
      console.error(`Failed to release lock ${lockId}:`, error);
      return { released: false, reason: 'error' };
    }
  }

  /**
   * Check if a lock exists and is valid
   */
  async isLocked(lockId: string): Promise<{ locked: boolean; lockData?: LockData }> {
    try {
      const lockDoc = await this.db.collection(this.config.collections.schedulerLocks).doc(lockId).get();
      
      if (!lockDoc.exists) {
        return { locked: false };
      }
      
      const lockData = lockDoc.data() as LockData;
      
      // Check if lock has expired
      if (lockData.expiresAt.toDate() <= new Date()) {
        // Clean up expired lock
        await lockDoc.ref.delete();
        return { locked: false };
      }
      
      return { locked: true, lockData };

    } catch (error) {
      console.error(`Failed to check lock ${lockId}:`, error);
      return { locked: false };
    }
  }

  /**
   * Execute operation with automatic lock management
   */
  async withLock<T>(
    lockId: string,
    operation: string,
    fn: () => Promise<T>,
    timeoutMs: number = 300000
  ): Promise<T> {
    const lockResult = await this.acquireLock(lockId, operation, timeoutMs);
    
    if (!lockResult.acquired) {
      throw new AppError(
        `Could not acquire lock for operation: ${operation}`,
        ErrorType.RESOURCE_LOCKED
      );
    }

    try {
      const result = await fn();
      return result;
    } finally {
      // Always try to release the lock
      await this.releaseLock(lockId);
    }
  }

  /**
   * Clean up expired locks (maintenance operation)
   */
  async cleanupExpiredLocks(): Promise<{ cleaned: number; errors: string[] }> {
    const now = new Date();
    const errors: string[] = [];
    let cleaned = 0;

    try {
      const expiredLocksSnapshot = await this.db
        .collection(this.config.collections.schedulerLocks)
        .where('expiresAt', '<=', admin.firestore.Timestamp.fromDate(now))
        .get();

      if (expiredLocksSnapshot.empty) {
        return { cleaned: 0, errors: [] };
      }

      // Use batch to delete expired locks
      const batch = this.db.batch();
      
      expiredLocksSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
        cleaned++;
      });

      await batch.commit();
      console.log(`Cleaned up ${cleaned} expired locks`);

    } catch (error) {
      const errorMsg = `Failed to cleanup expired locks: ${error}`;
      console.error(errorMsg);
      errors.push(errorMsg);
    }

    return { cleaned, errors };
  }

  /**
   * Get current instance ID
   */
  getInstanceId(): string {
    return this.instanceId;
  }
}
