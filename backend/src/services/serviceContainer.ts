/**
 * Service Container for Dependency Injection
 * Provides centralized service management and reduces instantiation overhead
 */

import { ProgramService } from './programService';
import { MigrationService } from './migrationService';
import { DatabaseService } from './databaseService';
import { SchedulerService } from './schedulerService';
import { ValidationService } from './validationService';
import { NotificationService } from './notificationService';

export class ServiceContainer {
  private static instance: ServiceContainer;
  
  // Service instances
  private _databaseService: DatabaseService | null = null;
  private _schedulerService: SchedulerService | null = null;
  private _programService: ProgramService | null = null;
  private _migrationService: MigrationService | null = null;
  private _notificationService: NotificationService | null = null;

  constructor() {
    if (ServiceContainer.instance) {
      return ServiceContainer.instance;
    }
    ServiceContainer.instance = this;
  }

  /**
   * Get singleton instance
   */
  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Get database service (singleton)
   */
  getDatabaseService(): DatabaseService {
    if (!this._databaseService) {
      this._databaseService = new DatabaseService();
    }
    return this._databaseService;
  }

  /**
   * Get scheduler service (singleton)
   */
  getSchedulerService(): SchedulerService {
    if (!this._schedulerService) {
      this._schedulerService = new SchedulerService();
    }
    return this._schedulerService;
  }

  /**
   * Get program service (singleton)
   */
  getProgramService(): ProgramService {
    if (!this._programService) {
      this._programService = new ProgramService();
    }
    return this._programService;
  }

  /**
   * Get migration service (singleton)
   */
  getMigrationService(): MigrationService {
    if (!this._migrationService) {
      this._migrationService = new MigrationService();
    }
    return this._migrationService;
  }

  /**
   * Get notification service (singleton)
   */
  getNotificationService(): NotificationService {
    if (!this._notificationService) {
      this._notificationService = new NotificationService();
    }
    return this._notificationService;
  }

  /**
   * Get validation service (static methods, no instantiation needed)
   */
  getValidationService(): typeof ValidationService {
    return ValidationService;
  }

  /**
   * Reset all services (useful for testing)
   */
  reset(): void {
    this._databaseService = null;
    this._schedulerService = null;
    this._programService = null;
    this._migrationService = null;
    this._notificationService = null;
  }
}
