/**
 * Database service for common Firestore operations
 * Centralizes database access patterns and reduces code duplication
 */

import * as admin from 'firebase-admin';
import { getDatabaseConfig } from '../utils/config';
import { NotFoundError, ValidationError, AppError, ErrorType } from '../utils/errorHandler';

export interface ProgramData {
  id: string;
  status: 'upcoming' | 'ongoing' | 'ended';
  startDate: string;
  duration: number;
  [key: string]: any;
}

export interface ParticipantData {
  id: string;
  timezone?: string;
  personalStartDate?: string;
  personalEndDate?: string;
  personalProgramStatus?: 'upcoming' | 'ongoing' | 'ended';
  personalCurrentDay?: number;
  livesLeft?: number;
  disqualified?: boolean;
  [key: string]: any;
}

export interface SubmissionData {
  status: 'upcoming' | 'submitted' | 'bailed' | 'not_submitted';
  timestamp?: string;
  attachment?: string;
  [key: string]: any;
}

export class DatabaseService {
  private db: admin.firestore.Firestore;
  private config = getDatabaseConfig();

  constructor() {
    this.db = admin.firestore();
  }

  /**
   * Get program document with validation
   */
  async getProgram(programId: string): Promise<ProgramData> {
    if (!programId) {
      throw new ValidationError('Program ID is required');
    }

    const programDoc = await this.db.collection(this.config.collections.programs).doc(programId).get();
    
    if (!programDoc.exists) {
      throw new NotFoundError('Program', programId);
    }

    const programData = programDoc.data();
    if (!programData) {
      throw new AppError('Program data is empty', ErrorType.INTERNAL_ERROR);
    }

    return {
      id: programId,
      ...programData
    } as ProgramData;
  }

  /**
   * Get participant document with validation
   */
  async getParticipant(programId: string, userId: string): Promise<ParticipantData> {
    if (!programId || !userId) {
      throw new ValidationError('Program ID and User ID are required');
    }

    const participantDoc = await this.db
      .collection(this.config.collections.programs)
      .doc(programId)
      .collection(this.config.collections.participants)
      .doc(userId)
      .get();

    if (!participantDoc.exists) {
      throw new NotFoundError(`Participant ${userId} in program`, programId);
    }

    const participantData = participantDoc.data();
    if (!participantData) {
      throw new AppError('Participant data is empty', ErrorType.INTERNAL_ERROR);
    }

    return {
      id: userId,
      ...participantData
    } as ParticipantData;
  }

  /**
   * Get all participants for a program
   */
  async getProgramParticipants(programId: string): Promise<ParticipantData[]> {
    if (!programId) {
      throw new ValidationError('Program ID is required');
    }

    const participantsSnapshot = await this.db
      .collection(this.config.collections.programs)
      .doc(programId)
      .collection(this.config.collections.participants)
      .get();

    return participantsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ParticipantData[];
  }

  /**
   * Update program status with transaction
   */
  async updateProgramStatus(
    programId: string, 
    status: 'upcoming' | 'ongoing' | 'ended',
    additionalData?: Record<string, any>
  ): Promise<void> {
    await this.db.runTransaction(async (transaction) => {
      const programRef = this.db.collection(this.config.collections.programs).doc(programId);
      const updateData = {
        status,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        ...additionalData
      };
      transaction.update(programRef, updateData);
    });
  }

  /**
   * Update participant data
   */
  async updateParticipant(
    programId: string,
    userId: string,
    updateData: Partial<ParticipantData>
  ): Promise<void> {
    const participantRef = this.db
      .collection(this.config.collections.programs)
      .doc(programId)
      .collection(this.config.collections.participants)
      .doc(userId);

    await participantRef.update(updateData);
  }

  /**
   * Get or create submission document
   */
  async getSubmission(programId: string, userId: string, dayNumber: number): Promise<SubmissionData | null> {
    const submissionDoc = await this.db
      .collection(this.config.collections.programs)
      .doc(programId)
      .collection(this.config.collections.participants)
      .doc(userId)
      .collection(this.config.collections.submissions)
      .doc(`Day ${dayNumber}`)
      .get();

    if (!submissionDoc.exists) {
      return null;
    }

    return submissionDoc.data() as SubmissionData;
  }

  /**
   * Get participant submission (alias for getSubmission for compatibility)
   */
  async getParticipantSubmission(programId: string, userId: string, dayNumber: number): Promise<SubmissionData | null> {
    return this.getSubmission(programId, userId, dayNumber);
  }

  /**
   * Update submission status
   */
  async updateSubmission(
    programId: string,
    userId: string,
    dayNumber: number,
    submissionData: Partial<SubmissionData>
  ): Promise<void> {
    const submissionRef = this.db
      .collection(this.config.collections.programs)
      .doc(programId)
      .collection(this.config.collections.participants)
      .doc(userId)
      .collection(this.config.collections.submissions)
      .doc(`Day ${dayNumber}`);

    await submissionRef.set(submissionData, { merge: true });
  }

  /**
   * Store GitHub OAuth token
   */
  async storeGitHubOAuthToken(state: string, tokenData: any): Promise<void> {
    await this.db.collection(this.config.collections.githubOAuthTokens).doc(state).set(tokenData);
  }

  /**
   * Create notification for a user
   */
  async createNotification(userId: string, notificationData: {
    title: string;
    message: string;
    type: 'account' | 'program' | 'points' | 'reminder';
    priority: 'low' | 'medium' | 'high';
    data?: Record<string, string>;
    imageUrl?: string;
  }): Promise<void> {
    const fullNotificationData = {
      ...notificationData,
      time: admin.firestore.FieldValue.serverTimestamp(),
      read: false,
      pushSent: false,
    };

    await this.db
      .collection(this.config.collections.users)
      .doc(userId)
      .collection(this.config.collections.notifications)
      .add(fullNotificationData);
  }



  /**
   * Check if all participants have ended their personal programs
   */
  async checkAllParticipantsEnded(programId: string): Promise<boolean> {
    const participants = await this.getProgramParticipants(programId);
    
    if (participants.length === 0) {
      return false;
    }

    return participants.every(participant => 
      participant.personalProgramStatus === 'ended'
    );
  }

  /**
   * Atomically update participant and submission data
   */
  async updateParticipantAndSubmission(
    programId: string,
    userId: string,
    dayNumber: number,
    participantUpdate: Partial<ParticipantData>,
    submissionUpdate: Partial<SubmissionData>
  ): Promise<void> {
    await this.db.runTransaction(async (transaction) => {
      const participantRef = this.db
        .collection(this.config.collections.programs)
        .doc(programId)
        .collection(this.config.collections.participants)
        .doc(userId);

      const submissionRef = this.db
        .collection(this.config.collections.programs)
        .doc(programId)
        .collection(this.config.collections.participants)
        .doc(userId)
        .collection(this.config.collections.submissions)
        .doc(`Day ${dayNumber}`);

      // Update participant data
      transaction.update(participantRef, participantUpdate);

      // Update submission data
      transaction.set(submissionRef, submissionUpdate, { merge: true });
    });
  }

  /**
   * Get database instance for advanced operations
   */
  getDatabase(): admin.firestore.Firestore {
    return this.db;
  }
}
