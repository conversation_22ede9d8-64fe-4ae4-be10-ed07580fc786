{"name": "accustom", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "migrate:analytics": "tsx scripts/migrateAnalytics.ts", "migrate:analytics:dry-run": "tsx scripts/migrateAnalytics.ts --dry-run", "migrate:analytics:cleanup": "tsx scripts/migrateAnalytics.ts --cleanup"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@shopify/flash-list": "1.7.3", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@stripe/stripe-react-native": "0.38.6", "expo": "^52.0.47", "expo-blur": "~14.0.3", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.1", "expo-constants": "~17.0.3", "expo-device": "~7.0.3", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-localization": "~16.0.1", "expo-location": "~18.0.10", "expo-maps": "^0.11.0", "expo-notifications": "^0.29.14", "expo-print": "~14.0.3", "expo-router": "~4.0.21", "expo-sharing": "~13.0.1", "expo-splash-screen": "^0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-web-browser": "~14.0.2", "firebase": "^11.10.0", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "^0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-signature-canvas": "^5.0.1", "react-native-svg": "15.8.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.6", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "private": true}